{"name": "dbos-monitoring-api", "version": "1.0.1", "license": "ISC", "keywords": ["api", "monitoring", "openapi"], "publishConfig": {"registry": "http://triton.bscpraha.cz:8082/nexus/repository/npm-internal/"}, "scripts": {"clean": "node_modules/.bin/rimraf target", "mock": "openapi mock api/index.yaml", "render": "openapi render-bundle api/index.yaml -e swagger-ui -o target/html", "validate": "openapi validate api/index.yaml", "bundle": "openapi bundle api/index.yaml -o target/api"}, "dependencies": {}, "devDependencies": {"@gemini/openapi-cli": "0.5.0", "rimraf": "^3.0.2"}}