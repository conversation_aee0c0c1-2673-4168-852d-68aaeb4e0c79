<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cz.finshape.vb</groupId>
        <artifactId>vb-mock</artifactId>
        <version>5.0.44-SNAPSHOT</version>
    </parent>

    <artifactId>vb-mock-app</artifactId>
    <!--
      The packaging must be WAR.
      See: https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/#web.servlet.embedded-container.jsp-limitations
    -->
    <packaging>jar</packaging>

    <name>VB Mock Application</name>

    <properties>
        <!-- Project -->
        <component.module.name>Application</component.module.name>
        <main-class>cz.finshape.vb.mock.VbMockApplication</main-class>

        <!-- Docker -->
        <jib-maven-plugin.to.image-artefact>mock:${project.version}</jib-maven-plugin.to.image-artefact>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cz.finshape.vb</groupId>
            <artifactId>vb-mock-core</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>cz.finshape.vb</groupId>
            <artifactId>vb-test</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>

        <!-- Spring -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <!-- Spring Web Services for our MPay endpoint -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web-services</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <!-- Required for loading custom <component>.properties -->
            <groupId>cz.bsc.commons</groupId>
            <artifactId>bsc-commons-spring-environment</artifactId>
        </dependency>

        <!-- JSP -->
        <dependency>
            <groupId>org.apache.tomcat.embed</groupId>
            <artifactId>tomcat-embed-jasper</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.servlet.jsp.jstl</groupId>
            <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.glassfish.web</groupId>
            <artifactId>jakarta.servlet.jsp.jstl</artifactId>
        </dependency>

        <!-- Tools -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!-- Security certificates -->
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.santuario</groupId>
            <artifactId>xmlsec</artifactId>
        </dependency>

        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>

        <resources>
            <resource>
                <directory>src/main/resources/security</directory>
                <targetPath>security</targetPath>
            </resource>
            <resource>
                <directory>src/main/resources/META-INF</directory>
                <targetPath>META-INF</targetPath>
            </resource>
            <resource>
                <directory>src/main/webapp/WEB-INF</directory>
                <targetPath>WEB-INF</targetPath>
            </resource>
            <resource>
                <directory>src/main/resources/wsdl</directory>
                <targetPath>wsdl</targetPath>
                <includes>
                    <include>**/*.wsdl</include>
                </includes>
                <!-- Activate the `filtering` and specify the `targetPath` for correct propagation of @...@ properties, for example into a custom banner. -->
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources/merchant</directory>
                <targetPath>merchant</targetPath>
                <!-- FALSE!! Otherwise, the property placeholders will be replaced, but we don't want that  -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources/data</directory>
                <targetPath>data</targetPath>
                <!-- FALSE!! Otherwise, the property placeholders will be replaced, but we don't want that  -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <!-- Include static resources to the classpath: JS scripts. -->
                <directory>src/main/webapp/scripts</directory>
                <targetPath>META-INF/resources/scripts</targetPath>
            </resource>
            <resource>
                <!-- Include static resources to the classpath: CSS styles. -->
                <directory>src/main/webapp/styles</directory>
                <targetPath>META-INF/resources/styles</targetPath>
            </resource>
            <resource>
                <!-- Include static resources to the classpath: img resources. -->
                <directory>src/main/webapp/img</directory>
                <targetPath>META-INF/resources/img</targetPath>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <!-- Repackage Application submodule as an executable Spring Boot fat JAR. -->
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>docker-local</id>
            <build>
                <plugins>
                    <plugin>
                        <!-- Docker image (Application). -->
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>docker-harbor</id>
            <build>
                <plugins>
                    <plugin>
                        <!-- Docker image (Application). -->
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
