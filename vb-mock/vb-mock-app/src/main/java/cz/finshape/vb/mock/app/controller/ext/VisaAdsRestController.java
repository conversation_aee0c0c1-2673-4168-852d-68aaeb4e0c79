/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.mock.app.controller.ext;


import java.time.YearMonth;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import jakarta.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.finshape.vb.core.utils.TemporalUtils;
import cz.finshape.vb.domain.internal.mock.MockP2PAliasJson;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.Address;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryEntityDetail;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryEntityItem;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryItem;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryResponseInternal;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquirySummary;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasNonDeletedStatuses;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasResolveBasicResponse;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasResolveRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasStatuses;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasValueRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.BaseAliasRequestProfile;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.ConsentConsent;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.CreateAlias200Response;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.CreateAliasRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.CreatePaymentCredential200Response;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.CreatePaymentCredentialRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.GetAlias200Response;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.GetAliasIdFromValue200Response;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialCommonResponsePreferredForInner;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialCreateUpdateResponse;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialItemAliasResponse;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialItemGetResponse;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialPreferredForWithDateItem;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialType;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.SharedAccountNumberType;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.UpdateAliasRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.UpdateAliasStatusRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.UpdatePaymentCredentialRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.UpdatePaymentCredentialStatusRequest;
import cz.finshape.vb.domain.thirdparty.generated.way4.Way4Card;
import cz.finshape.vb.mock.core.rest.ExtRestController;
import cz.finshape.vb.mock.core.service.ProductService;
import cz.finshape.vb.mock.core.service.visa.VisaAdsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * REST Controller for mocking P2P aliases part of EXT (external services).
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@RestController
@RequestMapping("/ext/aliasdirectory/v1")
@RequiredArgsConstructor
public class VisaAdsRestController implements ExtRestController {

    @Value("${mock.core.service.visa.entity-id.value}")
    private String entityIdValue;

    private final VisaAdsService visaAdsService;
    private final ProductService productService;
    private final ObjectMapper objectMapper;

    /**
     * Creates an Alias in the Alias Directory and associate one or more payment credentials with the Alias.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/createAlias_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param request request
     * @return response
     */
    @PostMapping("/aliases")
    public CreateAlias200Response createAlias(@RequestBody final CreateAliasRequest request) throws JsonProcessingException {
        Assert.notNull(request, "request must not be null");

        final var paymentCredentials = request.getPaymentCredentials()
                .stream()
                .map(paymentCredentialItemRequest -> {
                    final var externalId = paymentCredentialItemRequest.getExternalId();
                    Assert.notNull(externalId, "request.paymentCredentialItemRequest[?].externalId must not be null");

                    final var accountNumberType = resolveAccountNumberType(externalId);

                    return new MockP2PAliasJson.PaymentCredential()
                    .withId(UUID.randomUUID().toString())
                    .withAccountNumber(paymentCredentialItemRequest.getAccountNumber())
                    .withAccountNumberType(accountNumberType.getValue())
                    .withBillingAddress(new MockP2PAliasJson.BillingAddress()
                        .withCountry(paymentCredentialItemRequest.getBillingAddress().getCountry()))
                    .withCardType(paymentCredentialItemRequest.getCardType())
                    .withExpirationDate(YearMonth.parse(paymentCredentialItemRequest.getExpirationDate()))
                    .withExternalId(externalId)
                    .withIssuerName(paymentCredentialItemRequest.getIssuerName())
                    .withNameOnCard(paymentCredentialItemRequest.getNameOnCard())
                    .withStatus(UpdatePaymentCredentialStatusRequest.StatusEnum.ACTIVE.getValue())
                    .withPreferredFor(Optional.ofNullable(paymentCredentialItemRequest.getPreferredFor())
                        .orElse(List.of())
                        .stream()
                        .map(preferredFor -> new MockP2PAliasJson.PreferredFor()
                            .withType(preferredFor.getType().getValue()))
                        .toList())
                    .withType(paymentCredentialItemRequest.getType());
                })
            .toList();

        final var profile = new MockP2PAliasJson.Profile()
            .withFirstName(request.getProfile().getFirstName())
            .withLastName(request.getProfile().getLastName());
        final var consent = new MockP2PAliasJson.Consent()
            .withPresenter(request.getConsent().getPresenter())
            .withVersion(request.getConsent().getVersion())
            .withValidFromDateTime(request.getConsent().getValidFromDateTime().toLocalDateTime());

        final var createdMockP2PAlias = visaAdsService.createP2PAlias(new MockP2PAliasJson()
            .withAliasType(request.getAliasType().getValue())
            .withAliasValue(request.getAliasValue())
            .withStatus(AliasStatuses.ACTIVE.getValue())
            .withProfile(objectMapper.valueToTree(profile))
            .withConsent(objectMapper.valueToTree(consent))
            .withPaymentCredentials(objectMapper.valueToTree(paymentCredentials)));

        return new CreateAlias200Response()
            .id(createdMockP2PAlias.getAliasId())
            .externalId(request.getExternalId())
            .paymentCredentials(paymentCredentials.stream()
                .map(pc -> new PaymentCredentialCreateUpdateResponse()
                    .id(pc.getId())
                    .externalId(pc.getExternalId())
                    .type(PaymentCredentialType.fromValue(pc.getType())))
                .toList());
    }

    /**
     * Deletes a specific Alias and the associated payment credentials.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/deleteAlias_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param aliasId alias ID
     */
    @DeleteMapping("/aliases/{aliasId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteAlias(@PathVariable final String aliasId) {
        visaAdsService.deleteP2PAlias(aliasId);
    }

    /**
     * Retrieves the information about a specific Alias.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/getAlias_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param aliasId alias ID
     * @return response
     */
    @GetMapping("/aliases/{aliasId}")
    public GetAlias200Response getAlias(@PathVariable final String aliasId) throws JsonProcessingException {
        final var mockP2PAlias = visaAdsService.getP2PAlias(aliasId).orElseThrow();
        final var profile = objectMapper.treeToValue(mockP2PAlias.getProfile(), MockP2PAliasJson.Profile.class);
        final var consent = objectMapper.treeToValue(mockP2PAlias.getConsent(), MockP2PAliasJson.Consent.class);
        return new GetAlias200Response()
            .aliasType(GetAlias200Response.AliasTypeEnum.fromValue(mockP2PAlias.getAliasType()))
            .aliasValue(mockP2PAlias.getAliasValue())
            .status(AliasNonDeletedStatuses.fromValue(mockP2PAlias.getStatus()))
            .profile(new BaseAliasRequestProfile()
                .firstName(profile.getFirstName())
                .lastName(profile.getLastName()))
            .consent(new ConsentConsent()
                .version(consent.getVersion())
                .presenter(consent.getPresenter())
                .validFromDateTime(TemporalUtils.asInUtc(consent.getValidFromDateTime())));
    }

    /**
     * Updates a P2P alias.
     *
     * @param aliasId alias ID
     * @param request alias request
     */
    @PutMapping("/aliases/{aliasId}")
    public void updateAlias(@PathVariable final String aliasId, @RequestBody final UpdateAliasRequest request) throws JsonProcessingException {
        Assert.notNull(request, "request must not be null");

        final var mockP2PAlias = visaAdsService.getP2PAlias(aliasId).orElseThrow();
        final var profile = objectMapper.treeToValue(mockP2PAlias.getProfile(), MockP2PAliasJson.Profile.class);
        final var consent = objectMapper.treeToValue(mockP2PAlias.getConsent(), MockP2PAliasJson.Consent.class);

        final var newProfile = request.getProfile();
        profile.setFirstName(newProfile.getFirstName());
        profile.setLastName(newProfile.getLastName());

        final var newConsent = request.getConsent();
        consent.setPresenter(newConsent.getPresenter());
        consent.setValidFromDateTime(newConsent.getValidFromDateTime().toLocalDateTime());

        mockP2PAlias.setProfile(objectMapper.valueToTree(profile));
        mockP2PAlias.setConsent(objectMapper.valueToTree(consent));

        visaAdsService.updateP2PAlias(aliasId, mockP2PAlias);
        log.debug("Mock P2P alias '{}' updated", aliasId);
    }

    /**
     * Retrieves the information about a specific Alias identified by external Id.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/getAliasIdFromValue_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param request alias value request
     * @return alias response
     */
    @PostMapping("/aliases/aliasId")
    public GetAliasIdFromValue200Response getAliasIdFromValue(@RequestBody final AliasValueRequest request) {
        Assert.notNull(request, "request must not be null");
        Assert.notNull(request.getAliasValue(), "request.aliasValue must not be null");

        final var mockP2PAlias = visaAdsService.getAliasIdFromValue(request.getAliasValue());
        return new GetAliasIdFromValue200Response()
            .id(mockP2PAlias.getAliasId());
    }

    /**
     * Updates the status of an Alias to active or disabled.
     * A disabled Alias will not be available for Alias resolution.
     * However, lifecycle management may still occur on a disabled Alias.
     * A disabled Alias can be changed back to an Active alias.
     * Please note that any status change to an Alias will be applied to all associated Additional Aliases as well.
     * For example, if an Alias is changed to disabled, then all associated Additional Aliases is also changed to disabled.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/updateAliasStatus_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param aliasId alias ID
     * @param request request
     */
    @PutMapping("/aliases/{aliasId}/status")
    public void updateAliasStatus(@PathVariable final String aliasId, @RequestBody final UpdateAliasStatusRequest request) {
        Assert.notNull(request, "request must not be null");
        Assert.notNull(request.getStatus(), "request.status must not be null");

        final var updatedMockP2PAlias = visaAdsService.updateP2PAliasStatus(aliasId, request.getStatus().getValue());
        log.debug("Mock P2P alias '{}' updated status to '{}'", aliasId, updatedMockP2PAlias.getStatus());
    }

    /**
     * Retrieves information about an Alias and the payment credential(s) associated with the Alias.
     * This information can then be used to initiate a funds movement transaction.
     * The parameters in the response may vary depending on the program configuration.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Resolution/operation/aliasResolve_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param request request
     * @return response
     */
    @PostMapping("/aliases/resolve")
    public AliasResolveBasicResponse resolveAlias(@RequestBody final AliasResolveRequest request) throws JsonProcessingException {
        Assert.notNull(request, "request must not be null");
        Assert.notNull(request.getAliasType(), "request.aliasType must not be null");
        Assert.notNull(request.getAliasValue(), "request.aliasValue must not be null");

        final var mockP2PAlias =  visaAdsService.resolveAlias(request.getAliasType().getValue(), request.getAliasValue());
        final var profile = objectMapper
            .treeToValue(mockP2PAlias.getProfile(), MockP2PAliasJson.Profile.class);
        final var paymentCredentials = objectMapper
            .treeToValue(mockP2PAlias.getPaymentCredentials(), MockP2PAliasJson.PaymentCredential[].class);

        return new AliasResolveBasicResponse()
            .profile(new BaseAliasRequestProfile()
                .firstName(profile.getFirstName())
                .lastName(profile.getLastName()))
            .paymentCredentials(Arrays.stream(paymentCredentials)
                .map(paymentCredential -> new PaymentCredentialItemAliasResponse()
                    .accountNumber(paymentCredential.getAccountNumber())
                    .accountNumberType(SharedAccountNumberType.valueOf(paymentCredential.getAccountNumberType()))
                    .billingAddress(new Address()
                        .country(paymentCredential.getBillingAddress().getCountry()))
                    .cardType(paymentCredential.getCardType())
                    .expirationDate(paymentCredential.getExpirationDate().toString())
                    .issuerName(paymentCredential.getIssuerName())
                    .nameOnCard(paymentCredential.getNameOnCard())
                    .type(paymentCredential.getType())
                    .preferredFor(paymentCredential.getPreferredFor().stream()
                        .map(preferredFor -> new PaymentCredentialPreferredForWithDateItem()
                            .type(PaymentCredentialPreferredForWithDateItem.TypeEnum.fromValue(preferredFor.getType())))
                         .toList()))
                .toList());
    }

    /**
     * Adds a new Payment Credential to an existing Alias.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/createPaymentCredential_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param aliasId alias ID
     * @param request request
     * @return response
     */
    @PostMapping("/aliases/{aliasId}/paymentCredentials")
    public CreatePaymentCredential200Response createPaymentCredentials(
        @PathVariable final String aliasId,
        @RequestBody final CreatePaymentCredentialRequest request) throws JsonProcessingException {

        final var externalId = request.getExternalId();
        Assert.notNull(externalId, "request.paymentCredentialItemRequest[?].externalId must not be null");

        final var accountNumberType = resolveAccountNumberType(externalId);

        final var paymentCredential = new MockP2PAliasJson.PaymentCredential()
            .withId(UUID.randomUUID().toString())
            .withAccountNumber(request.getAccountNumber())
            .withAccountNumberType(accountNumberType.getValue())
            .withBillingAddress(new MockP2PAliasJson.BillingAddress()
                .withCountry(request.getBillingAddress().getCountry()))
            .withCardType(request.getCardType())
            .withExpirationDate(YearMonth.parse(request.getExpirationDate()))
            .withExternalId(request.getExternalId())
            .withIssuerName(request.getIssuerName())
            .withNameOnCard(request.getNameOnCard())
            .withStatus(UpdatePaymentCredentialStatusRequest.StatusEnum.ACTIVE.getValue())
            .withPreferredFor(Optional.ofNullable(request.getPreferredFor())
                .orElse(List.of())
                .stream()
                .map(preferredFor -> new MockP2PAliasJson.PreferredFor()
                    .withType(preferredFor.getType().getValue()))
                .toList())
            .withType(request.getType());

        final var updatedMockP2PAlias = visaAdsService.createPaymentCredentials(aliasId, paymentCredential);
        log.debug("Mock P2P alias '{}' updated : added new payment credentials '{}'",
            updatedMockP2PAlias.getAliasId(), paymentCredential.getId());

        return new CreatePaymentCredential200Response()
            .id(paymentCredential.getId())
            .externalId(paymentCredential.getExternalId())
            .type(PaymentCredentialType.fromValue(paymentCredential.getType()));
    }

    /**
     * Get details for a specific Payment Credential.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/getPaymentCredential_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param paymentCredentialId payment credential ID
     * @return response
     */
    @GetMapping("/paymentCredentials/{paymentCredentialId}")
    public PaymentCredentialItemGetResponse getPaymentCredential(@PathVariable final String paymentCredentialId) {
        final var paymentCredential = visaAdsService.getPaymentCredential(paymentCredentialId);
        return new PaymentCredentialItemGetResponse()
            .id(paymentCredential.getId())
            .status(PaymentCredentialItemGetResponse.StatusEnum.fromValue(paymentCredential.getStatus()))
            .accountNumber(paymentCredential.getAccountNumber())
            .accountNumberType(SharedAccountNumberType.valueOf(paymentCredential.getAccountNumberType()))
            .billingAddress(new Address()
                .country(paymentCredential.getBillingAddress().getCountry()))
            .cardType(paymentCredential.getCardType())
            .expirationDate(paymentCredential.getExpirationDate().toString())
            .issuerName(paymentCredential.getIssuerName())
            .nameOnCard(paymentCredential.getNameOnCard())
            .type(PaymentCredentialType.fromValue(paymentCredential.getType()))
            .externalId(paymentCredential.getExternalId())
            .preferredFor(paymentCredential.getPreferredFor().stream()
                .map(preferredFor -> new PaymentCredentialCommonResponsePreferredForInner()
                    .type(PaymentCredentialCommonResponsePreferredForInner.TypeEnum.fromValue(preferredFor.getType())))
                .toList());
    }

    /**
     * Get all Payment Credentials of an existing Alias.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/getPaymentCredentials_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param aliasId alias ID
     * @return response
     */
    @GetMapping("/aliases/{aliasId}/paymentCredentials")
    public List<PaymentCredentialItemGetResponse> getPaymentCredentials(@PathVariable final String aliasId) throws JsonProcessingException {
        Assert.notNull(aliasId, "aliasId must not be null");
        final var paymentCredentials = visaAdsService.getPaymentCredentials(aliasId);

        return paymentCredentials
            .stream()
            .map(paymentCredential -> new PaymentCredentialItemGetResponse()
                .id(paymentCredential.getId())
                .status(PaymentCredentialItemGetResponse.StatusEnum.fromValue(paymentCredential.getStatus()))
                .accountNumber(paymentCredential.getAccountNumber())
                .accountNumberType(SharedAccountNumberType.valueOf(paymentCredential.getAccountNumberType()))
                .billingAddress(new Address()
                    .country(paymentCredential.getBillingAddress().getCountry()))
                .cardType(paymentCredential.getCardType())
                .expirationDate(paymentCredential.getExpirationDate().toString())
                .issuerName(paymentCredential.getIssuerName())
                .nameOnCard(paymentCredential.getNameOnCard())
                .type(PaymentCredentialType.fromValue(paymentCredential.getType()))
                .externalId(paymentCredential.getExternalId())
                .preferredFor(paymentCredential.getPreferredFor().stream()
                    .map(preferredFor -> new PaymentCredentialCommonResponsePreferredForInner()
                        .type(PaymentCredentialCommonResponsePreferredForInner.TypeEnum.fromValue(preferredFor.getType())))
                    .toList()))
            .toList();
    }

    /**
     * Update an existing Payment Credential for an existing Alias.
     * This API replaces the target resource with the content in the request payload, including omitted fields.
     * Clients should perform a GET Payment Credential request prior to using this API.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/updatePaymentCredential_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param paymentCredentialId payment credential ID
     * @param request request
     */
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @PutMapping("/paymentCredentials/{paymentCredentialId}")
    public void updatePaymentCredentials(@PathVariable final String paymentCredentialId,
                                         @RequestBody final UpdatePaymentCredentialRequest request) throws JsonProcessingException {

        final var paymentCredential = new MockP2PAliasJson.PaymentCredential()
            .withExternalId(request.getExternalId())
            .withBillingAddress(new MockP2PAliasJson.BillingAddress()
                .withCountry(request.getBillingAddress().getCountry()))
            .withCardType(request.getCardType())
            .withExpirationDate(YearMonth.parse(request.getExpirationDate()))
            .withIssuerName(request.getIssuerName())
            .withNameOnCard(request.getNameOnCard())
            .withPreferredFor(Optional.ofNullable(request.getPreferredFor())
                .orElse(List.of())
                .stream()
                .map(preferredFor -> new MockP2PAliasJson.PreferredFor()
                    .withType(preferredFor.getType().getValue()))
                .toList())
            .withType(request.getType());

        visaAdsService.updatePaymentCredentials(paymentCredentialId, paymentCredential);
    }

    /**
     * Delete Payment Credentials by identificator
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Lifecycle-Management/operation/deletePaymentCredential_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param paymentCredentialId payment credential ID
     */
    @DeleteMapping("/paymentCredentials/{paymentCredentialId}")
    public void deletePaymentCredentials(@PathVariable final String paymentCredentialId) throws JsonProcessingException {
        visaAdsService.deletePaymentCredentials(paymentCredentialId);
    }

    /**
     * This enables clients to check if an Alias or list of Aliases is available for Alias resolution.
     * The response will indicate the Aliases that are available for Alias resolution.
     * Up to 1000 Aliases can be sent in a single request.
     *
     * @see <a href="https://developer.visa.com/capabilities/alias-directory-service/reference#tag/Alias-Resolution/operation/aliasInquiry_v1%20-%20Latest">Visa ADS Reference</a>
     *
     * @param request request
     * @return response
     */
    @PostMapping("/aliases/inquiry")
    public AliasInquiryResponseInternal inquiryAlias(@RequestBody final AliasInquiryRequest request) {
        Assert.notNull(request, "request must not be null");
        Assert.notNull(request.getAliases(), "request.aliases must not be null");
        Assert.notEmpty(request.getAliases(), "request.aliases must not be empty");

        final var aliases = request.getAliases();
        final var uniqueAliases = new HashSet<>(request.getAliases());

        final var mockP2PAliases = visaAdsService.inquiryAlias(aliases);
        final var details = mockP2PAliases.stream()
            .map(mockP2PAliasJson -> {
                try {
                    return new AliasInquiryItem()
                        .aliasType(AliasInquiryItem.AliasTypeEnum.fromValue(mockP2PAliasJson.getAliasType()))
                        .aliasValue(mockP2PAliasJson.getAliasValue())
                        .directories(List.of(new AliasInquiryEntityDetail()
                            .entities(Arrays.stream(objectMapper
                                    .treeToValue(mockP2PAliasJson.getPaymentCredentials(), MockP2PAliasJson.PaymentCredential[].class))
                                .map(paymentCredential -> new AliasInquiryEntityItem()
                                    .id(entityIdValue)
                                    .preferredFor(paymentCredential.getPreferredFor()
                                        .stream()
                                        .map(preferredFor -> new PaymentCredentialPreferredForWithDateItem()
                                            .type(PaymentCredentialPreferredForWithDateItem.TypeEnum.fromValue(preferredFor.getType())))
                                        .toList()))
                                .toList())
                        ));
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            })
            .toList();


        return new AliasInquiryResponseInternal()
            .details(details)
            .summary(new AliasInquirySummary()
                .aliasesTotal(aliases.size())
                .aliasesFound(details.size())
                .aliasesNotFound(aliases.size() - details.size())
                .aliasesRepeated(aliases.size() - uniqueAliases.size()));
    }

    @Nonnull
    private SharedAccountNumberType resolveAccountNumberType(@Nonnull final String cardId) {
        final SharedAccountNumberType accountNumberType;
        try {
            final var card = productService.getProduct(cardId);
            Assert.notNull(card, "card must not be null");
            Assert.notNull(card.getPayload(), "card.payload must not be null");
            final var cardPayload = objectMapper.readValue(card.getPayload(), Way4Card.class);
            final var issuer = cardPayload.getIssuer();
            Assert.notNull(issuer, "issuer must not be null");
            accountNumberType = switch (issuer) {
                case "MC" -> SharedAccountNumberType.PAN;
                case "VISA" -> SharedAccountNumberType.TOKEN;
                default -> {
                    log.error("Unknown issuer : {}", issuer);
                    yield null;
                }
            };
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Serialization failed", e);
        }
        Assert.notNull(accountNumberType, "accountNumberType must not be null");
        return accountNumberType;
    }
}
