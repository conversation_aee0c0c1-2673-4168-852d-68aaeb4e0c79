package cz.finshape.vb.mock.app.controller.ext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.easymock.EasyMock;
import org.easymock.EasyMockSupport;
import org.easymock.Mock;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.finshape.vb.mock.core.config.server.RestServerExceptionHandlerConfiguration;
import cz.finshape.vb.mock.core.service.ProductService;
import cz.finshape.vb.mock.core.service.visa.VisaAdsService;


/**
 * Tests {@link VisaAdsRestController}.
 *
 * <AUTHOR> Charalambidis
 */
//@SpringBootTest(
//    classes = {
//        VisaAdsRestController.class,
//        RestServerExceptionHandlerConfiguration.class,
//        VisaAdsRestControllerTest.TestConfiguration.class
//    },
//    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Import({VisaAdsRestControllerTest.TestConfiguration.class, RestServerExceptionHandlerConfiguration.class})
@WebMvcTest(controllers = VisaAdsRestController.class)
@AutoConfigureMockMvc(addFilters = false)
class VisaAdsRestControllerTest extends EasyMockSupport {

    @Autowired
    protected MockMvc mockMvc;

    @Test
    void validateExistingUser_user_doesnt_exist() throws Exception {

        replayAll();
        mockMvc.perform(post("/ext/aliasdirectory/v1/aliases/inquiry")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .content("""
                    {
                        "aliases": ["alias1", "alias2"]
                    }
                    """))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.exceptionType").value("cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException"))
            .andExpect(jsonPath("$.exceptionMessage").value("User doesnt exist"))
            .andExpect(jsonPath("$.errorCode").value("ENR_IDNP_NOT_VALID"));
        verifyAll();
    }

    @Configuration
    @TestPropertySource(properties =
        """
        mock.core.service.visa.entity-id.value=682bb6af-850b-46d0-955a-d118b2356b7d
        """
    )
    static class TestConfiguration {

        @Bean
        VisaAdsService visaAdsService() {
            return EasyMock.createMock(VisaAdsService.class);
        }

        @Bean
        ProductService productService() {
            return EasyMock.createMock(ProductService.class);
        }

        @Bean
        ObjectMapper objectMapper() {
            return new ObjectMapper();
        }
    }
}
