@Library("bsc-jen<PERSON>@v0.15.0") _
import cz.bsc.jenkins.BscArchiPublisher
import cz.bsc.jenkins.BscOpenApiProject
import cz.bsc.jenkins.BscRelease

def bscProject = new BscOpenApiProject([
    script: this,

    release: new BscRelease([
        script: this,
        snapshotSuffix: '-DRAFT'
    ]),

    archive: [
        artifacts: '**/target/*.tgz',
        allowEmptyArchive: true
    ],

    custom: [
        publishHTML: [
            allowMissing: false,
            alwaysLinkToLastBuild: false,
            keepAll: true,
            reportDir: 'target/html',
            reportFiles: 'index.html',
            reportName: 'DBOS API Catalog Service',
            reportTitles: '',
        ],

        archi: new BscArchiPublisher([
            publishScript: './bin/render_publish.sh',
            apiName: 'dbos',
            apis: ['api-catalog-service']
        ])
    ]
] as Map)

bscNodeFlow(bscProject) {
    echo "BSC Project branch=${bscProject.branch.name}"

    bscProject.prepareTools([
        node: 'NodeJS 16 LTS'
    ])

    properties([
        bscProject.jira(),
        disableConcurrentBuilds(),
        buildDiscarder(
            logRotator(
                artifactDaysToKeepStr: '',
                artifactNumToKeepStr: '1',
                daysToKeepStr: '',
                numToKeepStr: '5'
            )
        ),
        parameters(bscDefineJobParameters(bscProject))
    ])

    bscCheckoutStage(bscProject)
    bscReleaseStartStage(bscProject)

    stage('Prepare') {
        sh './bin/build-prepare.sh'
    }

    stage('Analyze') {
        recordPmdFindbugsCheckstyleIssues()
    }

    stage('Finalize') {
        sh './bin/build-finalize.sh'

        publishRenderedOpenApiLocally(bscProject)
    }

    bscPublishToArchiStage(bscProject)
    bscReleaseFinishStage(bscProject)
}

def recordPmdFindbugsCheckstyleIssues() {

    recordIssues([
        referenceJobName: 'dbos-api-catalog-service/develop',
        healthy: 1,
        unhealthy: 5,
        minimumSeverity: 'NORMAL',
        qualityGates: [
            [threshold: 1, type: 'TOTAL_ERROR', unstable: true],
            [threshold: 1, type: 'TOTAL_HIGH', unstable: true],
            [threshold: 1, type: 'TOTAL_NORMAL', unstable: true],
            [threshold: 50, type: 'TOTAL_LOW', unstable: true]
        ],
        tools: [
            taskScanner([
                excludePattern: 'Jenkinsfile, **/target/**, **/node_modules/**',
                includePattern: '**/*.xml, **/*.json, **/*.yaml, **/*.sh',
                highTags: 'FIXME',
                lowTags: 'TODO'
            ])
        ]
    ])

}

def publishRenderedOpenApiLocally(bscProject) {
    if (bscProject.custom.publishHTML) {
        publishHTML(bscProject.custom.publishHTML)
    }
}
