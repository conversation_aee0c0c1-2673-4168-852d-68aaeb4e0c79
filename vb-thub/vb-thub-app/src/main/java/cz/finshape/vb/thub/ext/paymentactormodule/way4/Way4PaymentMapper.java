/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.thub.ext.paymentactormodule.way4;


import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.mapstruct.Mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import cz.finshape.vb.domain.dbos.tpm.P2PPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.cbs.CbsPaymentStatusEnum;
import cz.finshape.vb.domain.internal.ips.IpsInstructionKafkaMessage;
import cz.finshape.vb.domain.internal.ips.IpsPaymentJson;
import cz.finshape.vb.domain.internal.spx.SpxWay4P2PPaymentRequest;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.domain.internal.tpm.System;
import cz.finshape.vb.domain.internal.tpm.TpmInstructionKafkaMessage;
import cz.finshape.vb.domain.thirdparty.internal.ChargeType;
import cz.finshape.vb.domain.thirdparty.internal.Way4PaymentJsonRequest;
import cz.finshape.vb.domain.thirdparty.internal.Way4PaymentJsonResponse;
import cz.finshape.vb.thub.ext.VbAction;
import cz.finshape.vb.thub.ext.paymentactormodule.AbstractPaymentActionMapper;
import cz.finshape.vb.thub.ext.paymentactormodule.VbResultType;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.g8.thub.actor.ActionResult;
import cz.bsc.g8.thub.actor.ResultType;


/**
 * Mapper for {@link Way4PaymentAction} implementations.
 *
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Way+4+module">Confluence</a>
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@Mapper(componentModel = "spring")
public abstract class Way4PaymentMapper extends AbstractPaymentActionMapper {

    /**
     * Maps TPM Instruction Kafka message into Way4 payment request: {@link VbAction#WAY4_DO_PAYMENT}.
     *
     * @param message TPM Instruction Kafka message
     * @return Way4 payment request
     */
    @Nonnull
    public Way4PaymentJsonRequest mapDoPayment(@Nonnull final TpmInstructionKafkaMessage message) {
        final var instruction = message.getInstruction();
        final var instructionItem = message.getItem();
        final var instructionItemData = instructionItem.getData();
        final var instructionItemExternalIds = Optional
            .ofNullable(instructionItem.getExternalIds())
            .orElse(Map.of());

        log.info("Mapping dynamically: "
                + "instructionItem.id={}, instruction(type={}, version={}, clientId={}, userId={})",
            instructionItem.getId(), instruction.getType(), instruction.getVersion(), instruction.getClientId(), instruction.getUserId());

        final var request = mapCommonFields(instructionItemData, instructionItemExternalIds);
        mapDoPaymentCommonFields(request, instructionItemData);
        request.setPaymentType(findText(instructionItemData, "cbsPaymentType"));
        log.debug("Generated msgId={}", request.getMsgId());
        return request;
    }

    /**
     * Maps IPS Instruction Kafka message into Way4 payment request: {@link VbAction#WAY4_DO_PAYMENT}.
     *
     * @param message TPM Instruction Kafka message
     * @return Way4 payment request
     */
    @Nonnull
    public Way4PaymentJsonRequest mapDoPayment(@Nonnull final IpsInstructionKafkaMessage message) {
        final var instructionData = message.getInstruction();
        final var request = mapIpsCommonFields(instructionData);
        request.setPaymentType(instructionData.getPaymentType());
        return request;
    }

    /**
     * Maps TPM Instruction Kafka message into Way4 payment request: {@link VbAction#WAY4_DO_INTRABANK_IPS_PAYMENT}.
     *
     * @param message TPM Instruction Kafka message
     * @param isCredit true/false indicates if it is a isCredit or debit
     * @return Way4 payment request
     */
    @Nonnull
    public Way4PaymentJsonRequest mapDoIntrabankIpsPayment(@Nonnull final TpmInstructionKafkaMessage message, final boolean isCredit) {
        final var instruction = message.getInstruction();
        final var instructionItem = message.getItem();
        final var instructionItemData = instructionItem.getData();
        final var instructionItemExternalIds = Optional
            .ofNullable(instructionItem.getExternalIds())
            .orElse(Map.of());

        log.info("Mapping dynamically: "
                + "instructionItem.id={}, instruction(type={}, version={}, clientId={}, userId={})",
            instructionItem.getId(), instruction.getType(), instruction.getVersion(), instruction.getClientId(), instruction.getUserId());

        final var request = mapCommonFields(instructionItemData, instructionItemExternalIds);
        mapDoPaymentCommonFields(request, instructionItemData);
        request.setPaymentType(CbsPaymentType.getIpsIntrabankPaymentType(findText(instructionItemData, "cbsPaymentType"), "W4", isCredit));
        log.debug("Generated msgId={}", request.getMsgId());
        return request;
    }

    /**
     * Maps TPM Instruction Kafka message into Way4 payment request for P2P payment.
     *
     * @param message TPM Instruction Kafka message
     * @return Way4 payment request
     */
    @Nonnull
    public SpxWay4P2PPaymentRequest mapDoP2PPayment(@Nonnull final TpmInstructionKafkaMessage message) throws JsonProcessingException {
        final var instruction = message.getInstruction();
        final var instructionItem = message.getItem();
        final var p2pInstruction = thubObjectMapper.treeToValue(instructionItem.getData(), P2PPaymentOrderInstruction.class);

        final var instructionItemExternalIds = Optional
            .ofNullable(instructionItem.getExternalIds())
            .orElse(Map.of());

        log.info("Mapping : "
                + "instructionItem.id={}, instruction(type={}, version={}, clientId={}, userId={})",
            instructionItem.getId(), instruction.getType(), instruction.getVersion(), instruction.getClientId(), instruction.getUserId());

        return new SpxWay4P2PPaymentRequest()
            .withAmount(p2pInstruction.getAmount())
            .withBranchId(p2pInstruction.getBranchId())
            .withCbsPaymentType(p2pInstruction.getCbsPaymentType())
            .withChannel(p2pInstruction.getChannel())
            .withClientName(p2pInstruction.getClientName())
            .withContinuallyCoordinateClientCenteredManufacturedId(p2pInstruction.getContinuallyCoordinateClientCenteredManufacturedId())
            .withCurrency(p2pInstruction.getCurrency())
            .withDebitedCardId(p2pInstruction.getDebitedCardId())
            .withDebitedCardMaskedNumber(p2pInstruction.getDebitedCardMaskedNumber())
            .withIpAddress(p2pInstruction.getIpAddress())
            .withPanId(p2pInstruction.getPanId())
            .withPartnerName(p2pInstruction.getPartnerName())
            .withPartnerPhoneNumber(p2pInstruction.getPartnerPhoneNumber())
            .withPaymentFee(p2pInstruction.getPaymentFee())
            .withRemittanceInformation(p2pInstruction.getRemittanceInformation())
            .withRrn(instructionItemExternalIds.get(System.VB_CBS.getId()))
            .withSignedData(p2pInstruction.getSignedData())
            .withValueDate(p2pInstruction.getValueDate());
    }

    /**
     * Maps TPM Instruction Kafka message into Way4 payment request: {@link VbAction#WAY4_REVERT_PAYMENT}.
     *
     * @param message TPM Instruction Kafka message
     * @return Way4 payment request
     */
    @Nonnull
    public Way4PaymentJsonRequest mapRevertPayment(@Nonnull final TpmInstructionKafkaMessage message) {
        final var instruction = message.getInstruction();
        final var instructionItem = message.getItem();
        final var instructionItemData = instructionItem.getData();
        final var instructionItemExternalIds = Optional
            .ofNullable(instructionItem.getExternalIds())
            .orElse(Map.of());

        log.info("Mapping dynamically: "
                + "instructionItem.id={}, instruction(type={}, version={}, clientId={}, userId={})",
            instructionItem.getId(), instruction.getType(), instruction.getVersion(), instruction.getClientId(), instruction.getUserId());

        final var request = mapCommonFields(instructionItemData, instructionItemExternalIds);
        mapDoPaymentCommonFields(request, instructionItemData);
        request.setPaymentType(findText(instructionItemData, "cbsPaymentType") + "REV");
        log.debug("Generated msgId={}", request.getMsgId());
        return request;
    }

    /**
     * Maps TPM Instruction Kafka message into Way4 payment request: {@link VbAction#WAY4_REVERT_INTRABANK_IPS_PAYMENT}.
     *
     * @param message TPM Instruction Kafka message
     * @param isCredit true/false indicates if it is a isCredit or debit
     * @return Way4 payment request
     */
    @Nonnull
    public Way4PaymentJsonRequest mapRevertIntrabankIpsPayment(@Nonnull final TpmInstructionKafkaMessage message, final boolean isCredit) {
        final var instruction = message.getInstruction();
        final var instructionItem = message.getItem();
        final var instructionItemData = instructionItem.getData();
        final var instructionItemExternalIds = Optional
            .ofNullable(instructionItem.getExternalIds())
            .orElse(Map.of());

        log.info("Mapping dynamically: "
                + "instructionItem.id={}, instruction(type={}, version={}, clientId={}, userId={})",
            instructionItem.getId(), instruction.getType(), instruction.getVersion(), instruction.getClientId(), instruction.getUserId());

        final var request = mapCommonFields(instructionItemData, instructionItemExternalIds);
        mapDoPaymentCommonFields(request, instructionItemData);
        request.setPaymentType(
            CbsPaymentType.getIpsIntrabankPaymentType(findText(instructionItemData, "cbsPaymentType"), "W4", isCredit) + "REV");
        log.debug("Generated msgId={}", request.getMsgId());
        return request;
    }

    /**
     * Maps TPM Instruction Kafka message into Way4 payment request: {@link VbAction#WAY4_DO_C2C_FOREX_DEBIT}.
     *
     * @param message TPM Instruction Kafka message
     * @return Way4 payment request
     */
    @Nonnull
    public Way4PaymentJsonRequest mapDoC2CForexDebitPayment(@Nonnull final TpmInstructionKafkaMessage message) {
        final var instruction = message.getInstruction();
        final var instructionItem = message.getItem();
        final var instructionItemData = instructionItem.getData();
        final var instructionItemExternalIds = Optional
            .ofNullable(instructionItem.getExternalIds())
            .orElse(Map.of());

        log.info("Mapping dynamically: "
                + "instructionItem.id={}, instruction(type={}, version={}, clientId={}, userId={})",
            instructionItem.getId(), instruction.getType(), instruction.getVersion(), instruction.getClientId(), instruction.getUserId());

        // The field is overridden with a constant, but we want to validate the prefix is consistent.
        final var cbsPaymentType = findText(instructionItemData, "cbsPaymentType");
        if (!"C2C_FX".equals(cbsPaymentType)) {
            log.error("Unexpected cbsPaymentType for do-c2c-forex-debit : {}", cbsPaymentType);
        }

        final var request = mapCommonFields(instructionItemData, instructionItemExternalIds);
        request.setBankName(null);
        request.setClientName(null);
        request.setFiscalCode(null);
        request.setResidency(null);
        request.setPartnerName(null);
        request.setPartnerFiscalCode(null);
        request.setPartnerResidency(null);
        // paymentType is C2C_FX_DT for do-c2c-forex-debit.
        request.setPaymentType("C2C_FX_DT");
        request.setSubdivisionCode(null);
        request.setTransactionCode(null);

        log.debug("Generated msgId={}", request.getMsgId());
        return request;
    }


    /**
     * Maps TPM Instruction Kafka message into Way4 payment request: {@link VbAction#WAY4_DO_C2C_FOREX_CREDIT}.
     *
     * @param message TPM Instruction Kafka message
     * @return Way4 payment request
     */
    @Nonnull
    public Way4PaymentJsonRequest mapDoC2CForexCreditPayment(@Nonnull final TpmInstructionKafkaMessage message) {
        final var instruction = message.getInstruction();
        final var instructionItem = message.getItem();
        final var instructionItemData = instructionItem.getData();
        final var instructionItemExternalIds = Optional
            .ofNullable(instructionItem.getExternalIds())
            .orElse(Map.of());

        log.info("Mapping dynamically: "
                + "instructionItem.id={}, instruction(type={}, version={}, clientId={}, userId={})",
            instructionItem.getId(), instruction.getType(), instruction.getVersion(), instruction.getClientId(), instruction.getUserId());

        // The field is overridden with a constant, but we want to validate the prefix is consistent.
        final var cbsPaymentType = findText(instructionItemData, "cbsPaymentType");
        if (!"C2C_FX".equals(cbsPaymentType)) {
            log.error("Unexpected cbsPaymentType for do-c2c-forex-debit : {}", cbsPaymentType);
        }

        final var request = mapCommonFields(instructionItemData, instructionItemExternalIds);
        request.setBankName(null);
        request.setClientName(null);
        request.setFiscalCode(null);
        request.setResidency(null);
        request.setPartnerName(null);
        request.setPartnerFiscalCode(null);
        request.setPartnerResidency(null);
        // paymentType is C2C_FX_CT for do-c2c-forex-credit.
        request.setPaymentType("C2C_FX_CT");
        request.setSubdivisionCode(null);
        request.setTransactionCode(null);

        log.debug("Generated msgId={}", request.getMsgId());
        return request;
    }

    /**
     * Maps TPM Instruction Kafka message into Way4 payment request: {@link VbAction#WAY4_REVERT_C2C_FOREX_DEBIT}.
     *
     * @param message TPM Instruction Kafka message
     * @return Way4 payment request
     */
    @Nonnull
    public Way4PaymentJsonRequest mapRevertC2CForexDebitPayment(@Nonnull final TpmInstructionKafkaMessage message) {
        final var instruction = message.getInstruction();
        final var instructionItem = message.getItem();
        final var instructionItemData = instructionItem.getData();
        final var instructionItemExternalIds = Optional
            .ofNullable(instructionItem.getExternalIds())
            .orElse(Map.of());

        log.info("Mapping dynamically: "
                + "instructionItem.id={}, instruction(type={}, version={}, clientId={}, userId={})",
            instructionItem.getId(), instruction.getType(), instruction.getVersion(), instruction.getClientId(), instruction.getUserId());

        // The field is overridden with a constant, but we want to validate the prefix is consistent.
        final var cbsPaymentType = findText(instructionItemData, "cbsPaymentType");
        if (!"C2C_FX".equals(cbsPaymentType)) {
            log.error("Unexpected cbsPaymentType for do-c2c-forex-debit : {}", cbsPaymentType);
        }

        final var request = mapCommonFields(instructionItemData, instructionItemExternalIds);
        request.setBankName(null);
        request.setClientName(null);
        request.setFiscalCode(null);
        request.setResidency(null);
        request.setPartnerName(null);
        request.setPartnerResidency(null);
        // paymentType is C2C_FX_DT for do-c2c-forex-debit, it also has REV suffix for a revert.
        request.setPaymentType("C2C_FX_DTREV");
        request.setSubdivisionCode(null);
        request.setTransactionCode(null);

        log.debug("Generated msgId={}", request.getMsgId());
        return request;
    }

    /**
     * Maps TPM Instruction Kafka message data into common Way4 payment request fields.
     *
     * @param data TPM Instruction Kafka message instruction item data
     * @param externalIds TPM Instruction Kafka message external IDs
     * @return Way4 payment request
     */
    @Nonnull
    private Way4PaymentJsonRequest mapCommonFields(@Nonnull final JsonNode data, @Nonnull final Map<String, String> externalIds) {
        final var request = new Way4PaymentJsonRequest();
        request.setAccountNumber(findText(data, "accountNumber"));
        request.setBranch(findText(data, "branchId"));
        request.setChannelType(findText(data, "channel"));
        request.setChargeType(findEnum(data, "feeDistribution", ChargeType::fromValue));
        request.setCorrelationId(null); // TODO: Correlation ID from the context
        request.setDestinationAmount(findAmount(data, "creditedAmount"));
        request.setDestinationCurrency(findText(data, "creditedAccountCurrency"));
        request.setExchangeRate(findExchangeRate(data, "exchangeRate"));
        request.setIpAddress(findText(data, "ipAddress"));
        // According to VB Finshape v1.4.9 the flag is true/false, hence non-null.
        request.setIsHappyHour(findBoolean(data, "happyHourFlag", false));
        // msgId must be unique for each request (different even for the payment and its revert).
        request.setMsgId(UUID.randomUUID().toString());
        request.setParentId(findText(data, "continuallyCoordinateClientCenteredManufacturedId"));
        request.setPartnerAccountNumber(findText(data, "partnerAccountNumber"));
        request.setPaymentOrderNumber(findText(data, "paymentOrderNumber"));
        request.setPriority(findText(data, "priority"));
        request.setRrn(externalIds.get(System.VB_CBS.getId()));
        request.setRemittanceInformation(findText(data, "remittanceInformation"));
        request.setSourceAmount(findAmount(data, "debitedAmount"));
        request.setSourceCurrency(findText(data, "debitedAccountCurrency"));
        request.setTransactionAmount(findAmount(data, "amount"));
        request.setFee(findAmount(data, "paymentFee"));
        request.setTransactionCurrency(findText(data, "currency"));
        request.setValueDate(findObjectAsText(data, "valueDate", LocalDate.class));

        return request;
    }

    private void mapDoPaymentCommonFields(final Way4PaymentJsonRequest request, final JsonNode instructionItemData) {
        request.setBankName(findText(instructionItemData, "bankName"));
        request.setClientName(findText(instructionItemData, "clientName"));
        request.setFiscalCode(findText(instructionItemData, "fiscalCode"));
        request.setResidency(findText(instructionItemData, "residency"));
        request.setPartnerName(findText(instructionItemData, "partnerName"));
        request.setPartnerBankName(findText(instructionItemData, "partnerBankName"));
        request.setPartnerFiscalCode(findText(instructionItemData, "partnerFiscalCode"));
        request.setPartnerResidency(findText(instructionItemData, "partnerResidency"));
        request.setSubdivisionCode(findText(instructionItemData, "subdivisionCode"));
        request.setTransactionCode(findText(instructionItemData, "transactionCode"));
    }

    /**
     * Maps IPS Instruction Kafka message data into common Way4 payment request fields.
     *
     * @param ipsPaymentData IPS Instruction data
     * @return Way4 payment request
     */
    @Nonnull
    private Way4PaymentJsonRequest mapIpsCommonFields(@Nonnull final IpsPaymentJson ipsPaymentData) {
        final var request = new Way4PaymentJsonRequest();
        request.setMsgId(UUID.randomUUID().toString());
        request.setCorrelationId(null); // TODO: Correlation ID from the context
        request.setAccountNumber(ipsPaymentData.getPartnerIban());
        request.setFiscalCode(ipsPaymentData.getPartnerFiscalCode());
        request.setClientName(ipsPaymentData.getPartnerName());
        request.setPartnerAccountNumber(ipsPaymentData.getAccountNumber());
        request.setPartnerFiscalCode(ipsPaymentData.getFiscalCode());
        request.setPartnerName(ipsPaymentData.getClientName());
        request.setTransactionCurrency(ipsPaymentData.getTransactionCurrency());
        request.setTransactionAmount(ipsPaymentData.getTransactionAmount());
        request.setValueDate(ipsPaymentData.getValueDate());
        request.setRemittanceInformation(ipsPaymentData.getRemittanceInformation());
        request.setTransactionCode(ipsPaymentData.getTransactionCode());
        request.setRrn(ipsPaymentData.getRrn());

        log.debug("Generated msgId={}", request.getMsgId());
        return request;
    }

    /**
     * Maps Way4 payment response to {@link ActionResult}.
     *
     * @param response response
     * @return {@link ActionResult}
     */
    @Nonnull
    public ActionResult mapActionResult(@Nullable final Way4PaymentJsonResponse response) {
        if (response == null) {
            log.error("Null response");
            return ActionResult.of(ResultType.TECHNICAL_ERROR.getId(), Map.of());
        }
        final var actionResult = Optional.ofNullable(response.getStatus())
            .flatMap(this::mapActionResult)
            .orElseGet(() -> {
                final var auxData = new HashMap<String, Object>();
                auxData.put("reference", response.getReference());
                auxData.put("transMsg", response.getTransMsg());
                auxData.put("status", response.getStatus());

                return ActionResult.of(ResultType.TECHNICAL_ERROR.getId(), auxData);
            });

        log.info("Mapped Way4PaymentJsonResponse to result type {}", actionResult.getResultTypeId());
        return actionResult;
    }

    /**
     * Maps status to {@link ActionResult}.
     * The API implementation details can be found in the MS Word documents attached in the Confluence page.
     *
     * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/100+-+API+register">Confluence</a>
     *
     * @param status status
     * @return optional {@link ActionResult}
     */
    @Nonnull
    private Optional<ActionResult> mapActionResult(@Nullable final String status) {
        return Optional.ofNullable(status)
            .flatMap(CbsPaymentStatusEnum::fromCode)
            .map(e -> switch (e) {
                case ARCHIVED -> ResultType.SUCCESS.getId();
                case REFUSED -> VbResultType.BUSINESS_ERROR.getId();
                default -> {
                    // Way4 does not return OK type, so leave null and let the caller construct the default technical-error action type.
                    log.error("Received unexpected payment status : {}", status);
                    yield null;
                }
            })
            .map(ActionResult::of);
    }
}
