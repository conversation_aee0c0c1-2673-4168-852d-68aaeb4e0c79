openapi: 3.0.1
info:
  title: API Catalog Service
  version: '1.0'
  description: |-
    API provides a catalog of APIs implemented or defined by the component with links to APIs specification.

servers:
  - url: /api-catalog/v1

paths:
  /apis:
    get:
      summary: List of APIs
      description: |-
        Endpoint provides a list of catalog items representing component APIs.
      responses:
        '200':
          description: Successful response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiCatalogItemList'

  /apis/{apiId}:
    get:
      summary: Single API
      description: |-
        API Catalog item for one specific API.
      parameters:
        - $ref: '#/components/parameters/ApiId'

      responses:
        '200':
          description: Successful response.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiCatalogItem'
        '403':
          $ref: 'error.yaml#/components/responses/403'


  /api-specifications/ui:
    get:
      summary: Rendered API specifications.
      description: Provides rendered API specification.
      responses:
        '302':
          description: Redirect to the HTML viewer, which will render API specifications.

components:
  parameters:
    ApiId:
      name: apiId
      description: |-
        Unique identification of an API.
        Different versions of the same API MUST use different apiId.
        Recommended is the combination of the API name and the version with `-` as a separator.
      in: path
      schema:
        $ref: '#/components/schemas/ApiId'
      required: true
      examples:
        nameOnly:
          summary: Name only
          value: integration
        nameVersion:
          summary: Name + version
          value: integration-v2
        uuid:
          summary: UUID
          value: 2a2f0b30-1c24-31bf-8ceb-7b4332dd74ae
  schemas:
    ApiCatalogItemList:
      description: List of the APIs catalog metadata.
      required:
        - apis
      properties:
        apis:
          description: All the APIs implemented by the component.
          type: array
          items:
            $ref: '#/components/schemas/ApiCatalogItem'

    ApiCatalogItem:
      description: The description of the API catalog metadata.
      required:
        - id
        - name
        - presentationName
        - description
        - specificationType
        - specificationPath
        - specificationVersion
        - configurationType
      properties:
        id:
          $ref: '#/components/schemas/ApiId'
        name:
          description: Name of the API.
          type: string
          example: integration
        presentationName:
          description: Name of the API for presentation purposes.
          type: string
          example: Integration API
        description:
          description: Description of the API.
          type: string
          example: API exposing component services for intranet usage.
        specificationType:
          description: |-
            Defines API specification type: OpenAPI for REST APIs, AsyncAPI for event based communication like Kafka or MQ.
            GraphQL is currently not supported.
          type: string
          enum:
            - OpenAPI
            - AsyncAPI
        specificationPath:
          description: |-
            The path where OpenAPI specification is located.
            The recommended path is /api-specifications/{apiId}.
          type: string
          example: /api-specifications/integration_v2
        specificationVersion:
          description: |-
            The version of an API specification that is implemented by the component.
            The version can be used by the CI/CD pipeline to quickly identify that the specification was changed
            since the last API publication on the API portal.
          type: string
        configurationType:
          description: |-
            Configuration type identifies the relation between endpoints and component configuration - the structure of entities.
            Value `dynamic` MUST be used for the API that contains dynamically generated endpoints based on the component configuration (e.g., new endpoints generated for a new entity).
            Changes to the configuration do not change the API version.
            This is typical for Instant Data and Payments & Instruction components.

            API that is not changed based on the component configuration MUST have `static` configuration type.
          type: string
          enum:
            - static
            - dynamic
          example: static
        version:
          description: Version of an API.
          type: string
          example: v2
        documentationURL:
          description: |-
            URL for external API documentation.
            Can be used to point a user to extended documentation.
          type: string
          example: https://developer.portal.finshape.com/gen/integration/
        imagePath:
          description: path to an endpoint providing an image representing the API.
          type: string
          example: /icons?id=integration_v2
        tags:
          description: |-
            Tags describing the API.

            It should contain all relevant tags regarding the type and semantics of the API.

            The allowed (authorization) **type** tags:

            * business
            * admin
            * integration

            Additional semantic tags should describe what is the purpose of the API.
            There are no restrictions on such tags.
            For example:

            * replication
            * signing
            * certification
            * partner
            * openid
          type: array
          items:
            type: string
            minLength: 1
          example:
            - integration
            - replication

    ApiId:
      description: |-
        Unique identification of an API.
        Different versions of the same API MUST use different apiId.
        Recommended is the combination of the API name and the version with `-` as a separator.
      type: string
      example: integration-v2
