/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;

import java.math.BigDecimal;
import java.time.LocalDate;

import javax.sql.DataSource;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Repository;

import com.querydsl.core.types.dsl.Expressions;
import cz.finshape.vb.tpm.ext.generated.QVbdomesticpayments;
import cz.finshape.vb.tpm.ext.generated.Vbdomesticpayments;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.commons.spring.business.framework.resources.dao.impl.AbstractDSLDao;


/**
 * Implementation of the {@link DomesticPaymentsRepository}.
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class DomesticPaymentsRepositoryImpl extends AbstractDSLDao implements DomesticPaymentsRepository {

    private static final QVbdomesticpayments Q_VBDOMESTICPAYMENTS = QVbdomesticpayments.vbdomesticpayments;

    /**
     * Primary constructor.
     *
     * @param dataSource data source
     */
    public DomesticPaymentsRepositoryImpl(final DataSource dataSource) {
        super(dataSource);
    }

    @Nonnull
    @Override
    public BigDecimal sumMdlAmount(@Nonnull final String externalClientId, @Nonnull final LocalDate signetAt) {
        final var queryResult = withQuery(query -> query
            .select(Q_VBDOMESTICPAYMENTS.amountinmdl.sum()))
            .from(Q_VBDOMESTICPAYMENTS)
            .where(Q_VBDOMESTICPAYMENTS.clientid.eq(externalClientId)
                .and(Q_VBDOMESTICPAYMENTS.active.isTrue())
                .and(Q_VBDOMESTICPAYMENTS.signedat.after(signetAt.atStartOfDay()))
                )
            .fetch();

        return queryResult.get(0) == null ? BigDecimal.ZERO : queryResult.get(0);
    }

    @Override
    public void registerOrUpdatePayment(@Nonnull final Vbdomesticpayments paymentLimit) {
        final var instructionId = paymentLimit.getInstructionid();
        final var clientId = paymentLimit.getClientid();

        final var whereExpression = Q_VBDOMESTICPAYMENTS.instructionid.eq(instructionId)
            .and(Q_VBDOMESTICPAYMENTS.clientid.eq(clientId));

        final var notExisting = withQuery(query -> query.select()
            .select(Expressions.constant(1)))
            .from(Q_VBDOMESTICPAYMENTS)
            .where(whereExpression)
            .fetchResults()
            .isEmpty();

        // Make sure the record is active.
        paymentLimit.setActive(true);

        if (notExisting) {
            withInsertClause(Q_VBDOMESTICPAYMENTS, clause -> clause
                .populate(paymentLimit)
                .execute());
            log.debug("Created a record for instructionId={} and clientId={}",
                paymentLimit.getInstructionid(), paymentLimit.getClientid());
        } else {
            final var count = withUpdateClause(Q_VBDOMESTICPAYMENTS, clause -> clause
                .populate(paymentLimit)
                .where(whereExpression)
                .execute());
            log.debug("Updated {} record(s) where instructionId={} and clientId={}",
                count, instructionId, clientId);
        }
    }

    @Override
    public long deactivatePayment(@Nonnull final Long instructionId, @Nonnull final String clientExternalId) {
        final var count = withUpdateClause(Q_VBDOMESTICPAYMENTS, clause -> clause
            .where(Q_VBDOMESTICPAYMENTS.instructionid.eq(instructionId)
                .and(Q_VBDOMESTICPAYMENTS.clientid.eq(clientExternalId))
                .and(Q_VBDOMESTICPAYMENTS.active.isTrue()))
            .set(Q_VBDOMESTICPAYMENTS.active, false) // De/activation is driven with `active` column.
            .execute());
        log.debug("Deactivated {} record(s) where instructionId={} and clientExternalId={}",
            count, instructionId, clientExternalId);
        return count;
    }

    @Override
    public long removePayments(@Nonnull final LocalDate signedBefore) {
        final var count = withDeleteClause(Q_VBDOMESTICPAYMENTS, clause -> clause
            .where(Q_VBDOMESTICPAYMENTS.signedat.before(signedBefore.atStartOfDay()))
            .execute());
        log.debug("Removed {} record(s) signed before {}", count, signedBefore);
        return count;
    }
}
