/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.listener;


import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CREDITEDACCOUNTCURRENCY;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_DEBITEDACCOUNTCURRENCY;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getStringValue;

import java.math.BigDecimal;
import java.util.Optional;

import jakarta.annotation.Nonnull;

import com.fasterxml.jackson.databind.JsonNode;
import cz.finshape.vb.domain.dbos.tpm.CorpOwnAccountPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.tpm.InstructionType;
import cz.finshape.vb.tpm.ext.generated.Vbhappyhourpayments;
import cz.finshape.vb.tpm.ext.persistence.repository.HappyHourPaymentsRepository;
import cz.finshape.vb.tpm.ext.service.HappyHourService;
import cz.finshape.vb.tpm.ext.validation.validators.instruction.HappyHourValidator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.business.instruction.InstructionBase;
import cz.bsc.tpm.business.instruction.InstructionItem;
import cz.bsc.tpm.core.persistence.Timestamps;
import cz.bsc.tpm.plugin.certification.SignInstructionListener;
import cz.bsc.tpm.plugin.certification.SignInstructionListenerContext;
import cz.bsc.tpm.plugin.transition.InstructionStatusTransition;
import cz.bsc.tpm.plugin.transition.InstructionStatusTransitionListener;
import cz.bsc.tpm.service.api.instruction.InstructionDetailService;
import cz.bsc.tpm.service.api.instruction.InstructionItemService;


/**
 * Happy Hour instruction item listener.
 * <p>
 * It registers a payment to the table consisting of the Happy Hour instruction to ensure the amount in the limit currency is not reached.
 * Every each such Happy Hour instruction is hence validated against the table of accumulated Happy Hour payments.
 * <p>
 * <strong>Important</strong>:
 * The {@link InstructionStatusTransitionListener} is triggered only if the instruction flow definition (Financial, NonFinancial, ...)
 * has configured {@code event}, for example {@code "fireAfter": true}.
 * This is handled via {@link cz.bsc.tpm.service.flow.transition.ExternalEventSender}.
 * <p>
 * Supported instruction types: {@link OwnAccountPaymentOrderInstruction}.
 *
 * @see HappyHourValidator
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@RequiredArgsConstructor
public class HappyHourInstructionItemListener implements SignInstructionListener, InstructionStatusTransitionListener {

    private final HappyHourConfigurationProperties happyHourConfigurationProperties;
    private final HappyHourPaymentsRepository happyHourPaymentsRepository;
    private final HappyHourService happyHourService;

    private final InstructionDetailService instructionDetailService;
    private final InstructionItemService instructionItemService;

    /**
     * Registers the Happy Hour Payment upon <strong>full</strong> instruction signing.
     *
     * @param signingContext signing context
     */
    @Override
    public void onInstructionFullySigned(final SignInstructionListenerContext signingContext) {
        final var instructionId = signingContext.getInstructionId();
        final var instructionBase = instructionDetailService.findInstructionBaseById(instructionId)
            // This will never be thrown.
            .orElseThrow();

        if (InstructionType.isInstructionType(instructionBase.getInstructionTypeCode(), OwnAccountPaymentOrderInstruction.class)
            || InstructionType.isInstructionType(instructionBase.getInstructionTypeCode(), CorpOwnAccountPaymentOrderInstruction.class)) {

            final var instructionItem = getInstructionItem(instructionId);
            final var json = instructionItem.getJsonData();

            final var debitedAccountCurrency = getStringValue(json, FN_DEBITEDACCOUNTCURRENCY).orElseThrow();
            final var creditedAccountCurrency = getStringValue(json, FN_CREDITEDACCOUNTCURRENCY).orElseThrow();

            if (debitedAccountCurrency.equals(creditedAccountCurrency)) {
                // This condition MIGHT NOT be necessary as the happy-hour flag SHOULD NOT be activated for non-Forex instructions.
                // BUT include it for the sake of completeness (ForexInstructionItemListener).
                // For that reason, the log is ERROR.
                log.error("Skipping registering instruction into the limits table : "
                    + "It is not a Forex instruction (regardless of happy hour flag)");
                return;
            }

            final var happyHourFlag = hasHappyHourFlag(instructionItem, OwnAccountPaymentOrderInstruction.FN_HAPPYHOURFLAG);
            if (happyHourFlag) {
                final var amount = instructionItem.getAmount();
                final var currencyId = instructionItem.getCurrencyId();

                final var transactionAmountInLimitCurrency = happyHourService.convertInLimitCurrency(amount, currencyId);

                log.info("Registering signed Happy Hour instruction into the limits table : "
                        + "instruction(id={}, amount={}, currencyId={}), externalClientId={}, transactionAmountInLimitCurrency={}",
                    instructionId, amount, currencyId, instructionBase.getOwnership().getClientId(), transactionAmountInLimitCurrency);

                final var payment = createHappyHourPayment(instructionBase, transactionAmountInLimitCurrency);

                happyHourPaymentsRepository.registerOrUpdatePayment(payment);
            }
        }
    }

    /**
     * Deactivates Happy Hour payment if a certain transition happened for which the deactivation is configured.
     *
     * @param transition instruction status transition.
     */
    @Override
    public void onSuccessfulTransition(final InstructionStatusTransition transition) {
        final var instructionId = transition.getInstructionId();
        final var deactivationStatusPairs = happyHourConfigurationProperties.getPaymentDeactivation().getStatusPairs();
        if (deactivationStatusPairs.contains(transition.getFromStatus() + ":" + transition.getToStatus())) {
            log.debug("Found transition {} -> {} : instruction.id={}", transition.getFromStatus(), transition.getToStatus(), instructionId);
            if (InstructionType.isInstructionType(transition.getInstructionTypeCode(), OwnAccountPaymentOrderInstruction.class)
                || InstructionType.isInstructionType(transition.getInstructionTypeCode(), CorpOwnAccountPaymentOrderInstruction.class)) {

                deactivateHappyHourPayment(transition, OwnAccountPaymentOrderInstruction.FN_HAPPYHOURFLAG);
            }
        }
    }

    /**
     * Deactivates a Happy Hour payment for the given combination of instruction ID and external client ID.
     *
     * @param transition transition
     * @param fnHappyHourFlag happy hour flag instruction field name
     */
    @SuppressWarnings("SameParameterValue")
    private void deactivateHappyHourPayment(@Nonnull final InstructionStatusTransition transition,
                                            @Nonnull final String fnHappyHourFlag) {

        final var instructionId = transition.getInstructionId();
        final var externalClientId = transition.getInstruction().getOwnership().getClientId();

        final var instructionItem = getInstructionItem(instructionId);
        final var happyHourFlag = hasHappyHourFlag(instructionItem, fnHappyHourFlag);
        if (happyHourFlag) {
            log.info("Deactivating Happy Hour instruction in the limits table : instruction.id={}, externalClientId={}",
                instructionId, externalClientId);
            happyHourPaymentsRepository.deactivatePayment(instructionId, externalClientId);
        }
    }

    /**
     * Gets the first and only Instruction Item based on given Instruction ID.
     *
     * @param instructionId instruction ID
     * @return instruction item
     */
    @Nonnull
    private InstructionItem getInstructionItem(@Nonnull final Long instructionId) {
        final var instructionItems = instructionItemService.findFirstInstructionItemByInstructionId(instructionId);
        // Forex payments are never in batches and this never fails.
        return instructionItems.orElseThrow();
    }

    /**
     * Checks whether the given instruction item has enabled Happy Hour flag.
     *
     * @param instructionItem instruction item
     * @param fnHappyHourFlag Happy Hour flag parameter name
     * @return whether Happy Hour is activated
     */
    @SuppressWarnings("SameParameterValue")
    private boolean hasHappyHourFlag(@Nonnull final InstructionItem instructionItem, @Nonnull final String fnHappyHourFlag) {
        final var json = instructionItem.getJsonData();

        final var hasHappyHourFlag = Optional.ofNullable(json.get(fnHappyHourFlag))
            .map(JsonNode::booleanValue)
            .orElse(false);
        if (hasHappyHourFlag) {
            log.debug("Detected signed Happy Hour instruction with instruction.id={}", instructionItem.getInstructionId());
        }
        return hasHappyHourFlag;
    }

    /**
     * Creates a new Happy Hour Payment.
     *
     * @param instructionBase instruction base
     * @param transactionAmountInLimitCurrency transaction amount in the limit currency
     * @return new Happy Hour Payment
     */
    @Nonnull
    private Vbhappyhourpayments createHappyHourPayment(@Nonnull final InstructionBase instructionBase,
                                                       @Nonnull final BigDecimal transactionAmountInLimitCurrency) {

        final var payment = new Vbhappyhourpayments();
        payment.setInstructionid(instructionBase.getInstructionId());
        payment.setExternalclientid(instructionBase.getOwnership().getClientId());
        payment.setSignedat(Timestamps.now());
        payment.setActive(true);
        payment.setTransactionamountinlimitcurrency(transactionAmountInLimitCurrency);
        return payment;
    }
}
