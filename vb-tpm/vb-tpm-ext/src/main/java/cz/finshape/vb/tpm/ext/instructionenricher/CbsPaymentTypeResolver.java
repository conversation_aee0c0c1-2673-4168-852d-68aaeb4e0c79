/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import jakarta.annotation.Nullable;

import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import lombok.extern.slf4j.Slf4j;


/**
 * CBS payment type resolver for {@link cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction}
 * <p>
 * <a href="https://cz-support.finshape.com/confl/display/*********/OwnAccountsPaymentOrder#OwnAccountsPaymentOrder-Sameaccounts">Technical
 * analysis</a>'currencies
 * Isolated to separate class for testing purposes.
 *
 * <AUTHOR> Cernil
 */
@Slf4j
public final class CbsPaymentTypeResolver {

    private CbsPaymentTypeResolver() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * Resolve same currency CBS payment type
     *
     * @param isLocalCurrency true if MDL payment or false if FCY (foreign currency) payment
     * @param debSystem debit system
     * @param credSystem credit system
     * @return CBS payment type
     */
    @Nullable
    public static String resolveSameCurrency(final boolean isLocalCurrency,
                                             final ExternalSystem debSystem,
                                             final ExternalSystem credSystem) {
        final String systemPair = debSystem + "_" + credSystem;
        final String currencyType = isLocalCurrency ? "LCY" : "FCY";

        return switch (systemPair) {
            case "T24_T24" -> "A2A_" + currencyType;
            case "T24_WAY4" -> "A2C_" + currencyType;
            case "WAY4_T24" -> "C2A_" + currencyType;
            case "WAY4_WAY4" -> "C2C_" + currencyType;
            default -> {
                log.warn(
                    "Unknown CBS payment type for same currency payment. MDL: {}, debitSystem: {}, creditSystem: {}",
                    isLocalCurrency,
                    debSystem,
                    credSystem);
                yield null;
            }
        };
    }

    /**
     * Resolve forex CBS payment type
     *
     * @param debSystem debit system
     * @param credSystem credit system
     * @return CBS payment type
     */
    @Nullable
    public static String resolveForex(final ExternalSystem debSystem, final ExternalSystem credSystem) {
        final String systemPair = debSystem + "_" + credSystem;
        return switch (systemPair) {
            case "T24_T24" -> "A2A_FX";
            case "T24_WAY4" -> "A2C_FX";
            case "WAY4_T24" -> "C2A_FX";
            case "WAY4_WAY4" -> "C2C_FX";
            default -> {
                log.warn(
                    "Unknown CBS payment type for forex payment. DebitSystem: {}, creditSystem: {}",
                    debSystem,
                    credSystem);
                yield null;
            }
        };
    }
}
