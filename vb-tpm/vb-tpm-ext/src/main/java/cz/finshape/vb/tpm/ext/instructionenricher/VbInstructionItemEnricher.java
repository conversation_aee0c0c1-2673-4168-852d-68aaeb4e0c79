/*
 * Copyright (c) 2023 Banking Software Company s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;

import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.core.annotation.Order;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Account;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.core.entity.EntityRelationType;
import cz.bsc.tpm.core.user.LoggedUser;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricher;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Instruction item instruction base class with common methods for enriching instruction items.
 *
 * <AUTHOR> (copy of JNT version)
 */
@Slf4j
@RequiredArgsConstructor
@Order(0) // As long as any child uses @Order, the annotation should be applied
public abstract class VbInstructionItemEnricher implements InstructionItemEnricher {

    protected final GdsIntegrationApiConnector gdsIntegrationApiConnector;

    /**
     * Returns related entity Id from instruction context by type
     *
     * @param context instruction context data
     * @param entityName name of related entity type
     * @return id of related entity
     */
    @Nonnull
    protected Optional<String> getRelatedEntityId(@Nonnull final InstructionItemEnricherContext context, @Nonnull final String entityName) {
        final var relatedEntities = context.getInstruction().getRelations();
        return Optional.ofNullable(relatedEntities)
                       .flatMap(re -> re.findId(EntityRelationType.of(entityName)));
    }

    /**
     * Sets the account number to the item data
     *
     * @param context instruction context
     * @param itemData item data
     * @param property property name
     */
    protected void setAccountNumber(final InstructionItemEnricherContext context, final ObjectNode itemData, final String property) {
        final Optional<String> debitAccountIdAttr = getRelatedEntityId(context, Account.EN_SINGULAR);
        String accountNumber = null;
        if (debitAccountIdAttr.isPresent()) {
            final Account account = gdsIntegrationApiConnector.findEntityById(Account.EN_PLURAL, debitAccountIdAttr.get(), Account.FN_IBAN);
            if (account != null) {
                accountNumber = account.getIban();
                log.debug("Setting Account number: {}", accountNumber);
            }
        } else {
            log.error("Account id is missing in the instruction");
        }

        itemData.put(property, accountNumber);
    }

    /**
     * Returns whether the instruction contains an unknown entity relation.
     *
     * @param context instruction item enricher context
     * @return whether the instruction contains an unknown entity relation
     */
    protected boolean containsUnknownEntityRelation(@Nonnull final InstructionItemEnricherContext context) {
        // In case of instruction templates, the instruction itself does not exist (it's just a template, not an instruction).
        final var instructionId = context.getInstructionId();
        final var result = context.getInstruction().getRelations().containsUnknownRelation();
        log.debug("Instruction '{}' contains an unknown relation : {}", instructionId, result);
        return result;
    }

    /**
     * Returns whether the instruction contains an unknown ownership.
     *
     * @param context instruction item enricher context
     * @return whether the instruction contains an unknown ownership
     */
    protected boolean isUnknownOwnership(@Nonnull final InstructionItemEnricherContext context) {
        // In case of instruction templates, the instruction itself does not exist (it's just a template, not an instruction).
        final var instructionId = context.getInstructionId();
        final var result = context.getInstruction().getOwnership().isUnknownOwnership();
        log.debug("Instruction '{}' has an unknown ownership : {}", instructionId, result);
        return result;
    }

    /**
     * Returns whether the instruction contains an unknown logged user.
     *
     * @param context instruction item enricher context
     * @return whether the instruction contains an unknown logged user
     */
    protected boolean isUnknownUser(@Nonnull final InstructionItemEnricherContext context) {
        // In case of instruction templates, the instruction itself does not exist (it's just a template, not an instruction).
        final var instructionId = context.getInstructionId();
        final var result = context.getLoggedUser().map(LoggedUser::isUnknownUser).orElse(false);
        log.debug("Instruction '{}' has an unknown user : {}", instructionId, result);
        return result;
    }
}
