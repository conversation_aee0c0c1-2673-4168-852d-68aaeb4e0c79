/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import static cz.finshape.vb.domain.dbos.tpm.DomesticPaymentOrderInstruction.FN_CBSPAYMENTTYPE;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.DOMESTIC_PAYMENT_ORDER;

import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.dbos.tpm.DomesticPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.domain.thirdparty.internal.TreasuryAccountResponse;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import cz.finshape.vb.tpm.ext.support.InstructionItemUtils;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for DomesticPaymentOrder instructions.
 *
 * <ul>
 *     <li>adds bank's information</li>
 *     <li>adds payment information</li>
 *     <li>adds account bisAccountId dependant data</li>
 *     <li>adds clientName (depend on client id)</li>
 * </ul>
 * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?pageId=*********">01 - Payment in MDL (MB)</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/*********/Domestic+payment+order">Domestic and Intra bank payments Analysis</a>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Jirsa
 * */
@Slf4j
@Component
public class DomesticPaymentOrderEnricher extends VbInstructionItemEnricher {
    private final static String PAYMENT_ORDER_BIS_TAPE = "1";

    private final TpmInstructionService tpmInstructionService;

    /**
     * Instantiates a new Domestic payment order enricher.
     * @param tpmInstructionService TPM enricher service
     * @param gdsIntegrationApiConnector GDS Integration API connector
     */
    public DomesticPaymentOrderEnricher(
        final TpmInstructionService tpmInstructionService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
    }

    @Override
    public ObjectNode enrich(@Nonnull final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!DOMESTIC_PAYMENT_ORDER.equalsInstructionTypeCode(type)) {
            return itemData;
        }
        log.debug("Enriching instruction of type {}", type);

        enrichByTransactionStaticData(itemData);
        enrichByAccountData(context, itemData);
        enrichByClientData(context, itemData);
        enrichByBankData(itemData);

        InstructionItemUtils.getStringValue(itemData, DomesticPaymentOrderInstruction.FN_PARTNERACCOUNTNUMBER)
            .filter(VbConstants.Payments::isTreasuryAccount)
            .ifPresent(iban -> enrichByTreasuryData(itemData, iban));

        return itemData;
    }

    /**
     * This should be initiated only for batch payments.
     *
     * @param itemData the item data
     */
    private void enrichByBankData(final ObjectNode itemData) {
        final Optional<String> bankBicOpt = InstructionItemUtils.getStringValue(itemData, DomesticPaymentOrderInstruction.FN_PARTNERBANKBICCODE);
        if (bankBicOpt.isPresent()) {
            log.debug("BatchPayment detected. Bank BIC code: {}", bankBicOpt.get());
            final String bankBic = bankBicOpt.get();
            final Optional<String> bankName = tpmInstructionService.findInstitutionName(bankBic);
            if (bankName.isEmpty()) {
                log.warn("Bank not found for BIC code: {}", bankBic);
                itemData.set(DomesticPaymentOrderInstruction.FN_PARTNERBANKNAME, null);
            } else {
                itemData.put(DomesticPaymentOrderInstruction.FN_PARTNERBANKNAME, bankName.get());
            }

        } else {
            log.debug("No bank BIC code found in the instruction");
        }
    }

    /**
     * Adds treasury data - empty strings if account was not found
     */
    private void enrichByTreasuryData(final ObjectNode itemData, final String iban) {
        log.debug("Treasury account detected: {}", iban);
        final TreasuryAccountResponse treasuryAccount = tpmInstructionService.findTreasuryAccount(iban);
        if (treasuryAccount != null) {
            itemData.put(DomesticPaymentOrderInstruction.FN_PARTNERFISCALCODE, treasuryAccount.getFiscalCode());
            if (!VbConstants.Payments.isTreasuryWithGAccount(iban)) {
                // If the treasury account is not with G, we can use remittance information from CBS. Keep the provided one otherwise.
                itemData.put(DomesticPaymentOrderInstruction.FN_REMITTANCEINFORMATION, treasuryAccount.getRemittanceInformation());
            }
            itemData.put(DomesticPaymentOrderInstruction.FN_PARTNERNAME, treasuryAccount.getDepartmentName());
            itemData.put(DomesticPaymentOrderInstruction.FN_PARTNERBANKNAME, treasuryAccount.getBankName());
        } else {
            log.error("Treasury account not found for IBAN: {}", iban);
            itemData.set(DomesticPaymentOrderInstruction.FN_PARTNERFISCALCODE, null);
            itemData.set(DomesticPaymentOrderInstruction.FN_REMITTANCEINFORMATION, null);
            itemData.set(DomesticPaymentOrderInstruction.FN_PARTNERNAME, null);
            itemData.set(DomesticPaymentOrderInstruction.FN_PARTNERBANKNAME, null);
        }
    }

    /**
     * Enriches the instruction by transaction static data.
     *
     * @param itemData the item data
     */
    private void enrichByTransactionStaticData(@Nonnull final ObjectNode itemData) {
        itemData.put(DomesticPaymentOrderInstruction.FN_BANKNAME, VbConstants.VICTORIA_BANK_NAME);
        itemData.put(DomesticPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE, PAYMENT_ORDER_BIS_TAPE);
    }

    /**
     * Enriches the instruction by branch id.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByAccountData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (containsUnknownEntityRelation(context)) {
            return;
        }
        final Optional<String> debitAccountIdOpt = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (debitAccountIdOpt.isEmpty()) {
            log.error("Account id is missing in the instruction");
            return;
        }
        final String debitAccountId = debitAccountIdOpt.get();
        itemData.put(FN_CBSPAYMENTTYPE, getCbsPaymentType(debitAccountId).getCode());
        setAccountNumber(context, itemData, DomesticPaymentOrderInstruction.FN_ACCOUNTNUMBER);
    }

    /**
     * Enriches the instruction by client data.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByClientData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (isUnknownOwnership(context)) {
            return;
        }
        if (context.getInstruction().getOwnership() == null || !context.getInstruction().getOwnership().isOwnedByClient()) {
            log.error("Client id is missing in the instruction");
            return;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client clientsData = tpmInstructionService.getClientData(externalClientId);
        final var residency = Optional.ofNullable(clientsData.getResidency()).map(ResidencyType::getId).orElse(null);

        itemData.put(DomesticPaymentOrderInstruction.FN_CLIENTNAME, clientsData.getFullName());
        itemData.put(DomesticPaymentOrderInstruction.FN_FISCALCODE, clientsData.getFiscalCode());
        itemData.put(DomesticPaymentOrderInstruction.FN_RESIDENCY, residency);
    }

    /**
     * Gets the CBS payment type for the account.
     *
     * @param accountId {@link Account#FN_BISACCOUNTID}
     * @return CBS payment type
     */
    private CbsPaymentType getCbsPaymentType(@Nonnull final String accountId) {
        if (ExternalSystem.T24.isPrefixed(accountId)) {
            return CbsPaymentType.DomFA;
        } else if (ExternalSystem.WAY4.isPrefixed(accountId)) {
            return CbsPaymentType.DomFC;
        }
        log.error("Unknown CBS payment type for account id {}", accountId);
        return CbsPaymentType.NONE;
    }
}
