/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import java.util.Set;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.tpm.CorpSetCardLimitsRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.SetCardLimitsRequestInstruction;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException;
import cz.finshape.vb.domain.internal.tpm.InstructionType;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enriches {@link SetCardLimitsRequestInstruction} and {@link CorpSetCardLimitsRequestInstruction}.
 * <p>
 * The masked number is enriched separately via {@link CardMaskedNumberEnricher}.
 *
 * @see <a href="https://cz-support.finshape.com/confl/display/*********/Technical+Analysis+-+Manage+Limits">Manage Limits</a>
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
public class SetCardLimitsRequestEnricher extends VbInstructionItemEnricher {

    private static final Set<String> SUPPORTED_INSTRUCTION_TYPES = Set.of(
        InstructionType.SET_CARD_LIMITS_REQUEST.getInstructionTypeCode(),
        InstructionType.CORP_SET_CARD_LIMITS_REQUEST.getInstructionTypeCode()
    );

    /**
     * Primary constructor.
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     */
    public SetCardLimitsRequestEnricher(final GdsIntegrationApiConnector gdsIntegrationApiConnector) {
        super(gdsIntegrationApiConnector);
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (SUPPORTED_INSTRUCTION_TYPES.contains(type)) {
            final var cardRelation = Card.EN_SINGULAR;
            if (containsUnknownEntityRelation(context)) {
                return itemData;
            }
            final var cardId = getRelatedEntityId(context, cardRelation)
                .orElseThrow(() -> new VbBusinessException(VbErrorCodeEnum.ARGUMENT_NULL, "Missing %s relation".formatted(cardRelation)));

            final var card = gdsIntegrationApiConnector.<Card>getEntityById(Card.EN_PLURAL, cardId,
                Card.FN_ALLRETAILOPERATIONLIMITCURRENCY,
                Card.FN_ALLOPERATIONABROADLIMITCURRENCY,
                Card.FN_ATMRMLIMITCURRENCY,
                Card.FN_ATMABROADLIMITCURRENCY
            );

            itemData.put("allRetailOperationLimitCurrency", card.getAllRetailOperationLimitCurrency().getId());
            itemData.put("allOperationAbroadLimitCurrency", card.getAllOperationAbroadLimitCurrency().getId());
            itemData.put("atmRmLimitCurrency", card.getAtmRmLimitCurrency().getId());
            itemData.put("atmAbroadLimitCurrency", card.getAtmAbroadLimitCurrency().getId());

            log.info("Enriched instruction(id={}, type={}, item.id={}) with card limits for card={}",
                context.getInstructionId(), type, context.getInstructionItemId().orElse(null), cardId);
        }
        return itemData;
    }
}
