/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import static cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction.FN_ACCOUNTNUMBER;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction.FN_BANKNAME;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction.FN_CBSPAYMENTTYPE;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction.FN_CLIENTNAME;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction.FN_FISCALCODE;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction.FN_PARTNERBANKNAME;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction.FN_RESIDENCY;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.INTRABANK_FCY_PAYMENT_ORDER;

import java.util.Map;
import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.SwiftPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.domain.internal.tpm.instruction.SwiftPaymentOrderAttachment;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.file.FileMetadata;
import cz.bsc.tpm.file.instruction.InstructionAttachment;
import cz.bsc.tpm.file.instruction.InstructionAttachmentService;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for {@link IntrabankFCyPaymentOrderInstruction}.
 *
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Swift+payment+order">Swift and IntraFcy bank payments Analysis</a>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Cernil
 * */
@Slf4j
@Component
public class IntrabankFCyPaymentOrderEnricher extends VbInstructionItemEnricher {

    private static final TypeReference<Map<String, SwiftPaymentOrderAttachment>> ATTACHMENTS_TYPE_REFERENCE = new TypeReference<>() {};

    private final ObjectMapper objectMapper;
    private final TpmInstructionService tpmInstructionService;
    private final InstructionAttachmentService instructionAttachmentService;

    /**
     * Endues into existence an instance of enricher fortifying intra bank foreign currency payment orders.
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param objectMapper object mapper
     * @param tpmInstructionService TPM enricher service
     * @param instructionAttachmentService instruction attachment service
     */
    public IntrabankFCyPaymentOrderEnricher(
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final ObjectMapper objectMapper,
        final TpmInstructionService tpmInstructionService,
        final InstructionAttachmentService instructionAttachmentService) {

        super(gdsIntegrationApiConnector);
        this.objectMapper = objectMapper;
        this.tpmInstructionService = tpmInstructionService;
        this.instructionAttachmentService = instructionAttachmentService;
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!INTRABANK_FCY_PAYMENT_ORDER.equalsInstructionTypeCode(type)) {
            return itemData;
        }

        enrichByTransactionStaticData(itemData);
        enrichByAccountData(context, itemData);
        enrichClientData(context, itemData);
        enrichAttachments(context, itemData);
        return itemData;
    }

    /**
     * Enriches the instruction by transaction static data.
     *
     * @param itemData the item data
     */
    private void enrichByTransactionStaticData(@Nonnull final ObjectNode itemData) {
        itemData.put(FN_BANKNAME, VbConstants.VICTORIA_BANK_NAME);
        itemData.put(FN_PAYMENTORDERBISTYPE, VbConstants.BIS_TYPE_70);
        itemData.put(FN_PARTNERBANKNAME, VbConstants.VICTORIA_BANK_NAME);
    }

    /**
     * Enriches the instruction by branch id.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByAccountData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (containsUnknownEntityRelation(context)) {
            return;
        }
        final Optional<String> accountIdOpt = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (accountIdOpt.isEmpty()) {
            log.error("Account id is missing in the instruction");
            return;
        }
        final String accountId = accountIdOpt.get();
        itemData.put(FN_CBSPAYMENTTYPE, getCbsPaymentType(accountId).getCode());
        setAccountNumber(context, itemData, FN_ACCOUNTNUMBER);
    }

    /**
     * Enriches the instruction's client data.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichClientData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (isUnknownOwnership(context)) {
            return;
        }
        final String clientId = context.getInstruction().getOwnership().getClientId();
        final Client clientsData = tpmInstructionService.getClientData(clientId);
        final var residency = Optional.ofNullable(clientsData.getResidency()).map(ResidencyType::getId).orElse(null);

        itemData.put(FN_CLIENTNAME, clientsData.getFullName());
        itemData.put(FN_FISCALCODE, clientsData.getFiscalCode());
        itemData.put(FN_RESIDENCY, residency);
    }

    /**
     * Enriches {@link IntrabankFCyPaymentOrderInstruction#FN_ATTACHMENTS}.
     *
     * @param itemData item data to be enriched
     */
    private void enrichAttachments(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        final var attachmentsJson = itemData.get(IntrabankFCyPaymentOrderInstruction.FN_ATTACHMENTS);
        if (attachmentsJson == null) {
            return;
        }

        try {
            // Enrich ATTACHMENTS.
            final var attachments = objectMapper.treeToValue(attachmentsJson, ATTACHMENTS_TYPE_REFERENCE);
            attachments.forEach((attachmentId, swiftPaymentOrderAttachment) -> {
                instructionAttachmentService.findAttachment(Long.valueOf(attachmentId))
                    .map(InstructionAttachment::getFileMetadata)
                    .flatMap(FileMetadata::getFilename)
                    .ifPresentOrElse(filename -> {
                        if (swiftPaymentOrderAttachment.getFilename() != null) {
                            log.warn("The filename is present before enriching and will be overridden, former filename : {}",
                                swiftPaymentOrderAttachment.getFilename());
                        }
                        log.debug("Enriched attachment '{}' with filename : {}", attachmentId, filename);
                        swiftPaymentOrderAttachment.setFilename(filename);
                    },
                    () -> log.warn("No filename is available for attachment : {}", attachmentId));
            });
            final var updatedAttachmentsJson = objectMapper.valueToTree(attachments);
            itemData.set(SwiftPaymentOrderInstruction.FN_ATTACHMENTS, updatedAttachmentsJson);

            log.debug("Enriched instruction(id={}) with {} attachments : {}",
                context.getInstructionId(), attachments.size(), attachments);
        } catch (JsonProcessingException e) {
            log.error("Cannot enrich TPM attachments", e);
        }
    }

    /**
     * Gets the CBS payment type for the account.
     *
     * @param accountId account id
     * @return CBS payment type
     */
    private CbsPaymentType getCbsPaymentType(@Nonnull final String accountId) {
        if (ExternalSystem.T24.isPrefixed(accountId)) {
            return CbsPaymentType.IntFFA;
        } else if (ExternalSystem.WAY4.isPrefixed(accountId)) {
            return CbsPaymentType.IntFFC;
        }
        log.error("Unknown CBS payment type for account id {}", accountId);
        return CbsPaymentType.NONE;
    }
}
