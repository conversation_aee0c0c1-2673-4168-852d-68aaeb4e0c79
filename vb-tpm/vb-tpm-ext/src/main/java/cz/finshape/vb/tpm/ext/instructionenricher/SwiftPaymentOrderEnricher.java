/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import static cz.finshape.vb.domain.dbos.tpm.SwiftPaymentOrderInstruction.FN_CBSPAYMENTTYPE;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.SWIFT_PAYMENT_ORDER;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.xcr.XcrIntegrationApiConnector;
import cz.finshape.vb.core.client.xcr.json.CurrencyConversionRequestJson;
import cz.finshape.vb.core.client.xcr.json.CurrencyConversionRequestPackageJson;
import cz.finshape.vb.core.client.xcr.json.CurrencyConversionResponseJson;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.dbos.tpm.SwiftPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.domain.internal.tpm.instruction.SwiftPaymentOrderAttachment;
import cz.finshape.vb.domain.internal.xcr.MarketType;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import cz.finshape.vb.tpm.ext.support.InstructionItemUtils;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.file.FileMetadata;
import cz.bsc.tpm.file.instruction.InstructionAttachment;
import cz.bsc.tpm.file.instruction.InstructionAttachmentService;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;

/**
 * Enricher for SwiftPaymentOrder instructions.
 *
 * <ul>
 *     <li>add bank's information</li>
 *     <li>add payment information</li>
 *     <li>add data depend on account id</li>
 *     <li>add attachments technical information</li>
 * </ul>
 * <a href="https://cz-support.finshape.com/confl/display/P20009724/Swift+payment+order">Swift payment Analysis</a>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Jirsa
 * */
@Slf4j
@Component
public class SwiftPaymentOrderEnricher extends VbInstructionItemEnricher {

    private static final TypeReference<Map<String, SwiftPaymentOrderAttachment>> ATTACHMENTS_TYPE_REFERENCE = new TypeReference<>() {};

    private final ObjectMapper objectMapper;
    private final TpmInstructionService tpmInstructionService;
    private final XcrIntegrationApiConnector xcrIntegrationApiConnector;
    private final InstructionAttachmentService instructionAttachmentService;

    /**
     * Instantiates a new Swift payment order enricher.
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param objectMapper object mapper
     * @param tpmInstructionService TPM enricher service
     * @param xcrIntegrationApiConnector xcr integration api connector
     * @param instructionAttachmentService instruction attachment service
     */
    public SwiftPaymentOrderEnricher(
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final ObjectMapper objectMapper,
        final TpmInstructionService tpmInstructionService,
        final XcrIntegrationApiConnector xcrIntegrationApiConnector,
        final InstructionAttachmentService instructionAttachmentService) {

        super(gdsIntegrationApiConnector);
        this.objectMapper = objectMapper;
        this.tpmInstructionService = tpmInstructionService;
        this.xcrIntegrationApiConnector = xcrIntegrationApiConnector;
        this.instructionAttachmentService = instructionAttachmentService;
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!SWIFT_PAYMENT_ORDER.equalsInstructionTypeCode(type)) {
            return itemData;
        }

        enrichByTransactionStaticData(itemData);
        enrichByAccountData(context, itemData);
        enrichAttachments(context, itemData);

        if (isUnknownOwnership(context)) {
            return itemData;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client client = tpmInstructionService.getClientData(externalClientId);

        enrichClientData(client, itemData);

        if (GdsCatalogValues.LegalCategoryTypeValue.isRetail(client.getLegalCategory())) {
            final Optional<String> currencyOpt = InstructionItemUtils.getStringValue(itemData, SwiftPaymentOrderInstruction.FN_CURRENCY);
            final Optional<BigDecimal> amountOpt = InstructionItemUtils.getBigDecimalValue(itemData, SwiftPaymentOrderInstruction.FN_AMOUNT);
            if (currencyOpt.isEmpty() || amountOpt.isEmpty()) {
                log.error("Currency or amount is missing in the instruction ({}, {}), will fail later", currencyOpt, amountOpt);
                // terminate the enrichment
                return itemData;
            }

            final BigDecimal mdlAmount;
            final BigDecimal eurAmount;
            if (GdsCatalogValues.CurrencyValue.isLocal(currencyOpt.get())) {
                mdlAmount = amountOpt.get();
                eurAmount = xcrIntegrationApiConnector.convertCurrency(
                    MarketType.STANDARD.getId(),
                    amountOpt.get(),
                    currencyOpt.get(),
                    GdsCatalogValues.CurrencyValue.EUR.getEntityUniqueId());
            } else if (GdsCatalogValues.CurrencyValue.isEur(currencyOpt.get())) {
                // put eur as is
                eurAmount = amountOpt.get();
                mdlAmount = xcrIntegrationApiConnector.convertCurrency(
                    MarketType.STANDARD.getId(),
                    amountOpt.get(),
                    currencyOpt.get(),
                    GdsCatalogValues.CurrencyValue.MDL.getEntityUniqueId());
            } else {
                // convert to currency to MDL
                final var mdlConversion = CurrencyConversionRequestPackageJson.ofStandard(
                    amountOpt.get(),
                    currencyOpt.get(),
                    GdsCatalogValues.CurrencyValue.MDL.getEntityUniqueId()
                );

                // convert currency to EUR
                final var eurConversion = CurrencyConversionRequestPackageJson.ofStandard(
                    amountOpt.get(),
                    currencyOpt.get(),
                    GdsCatalogValues.CurrencyValue.EUR.getEntityUniqueId()
                );
                final Map<String, BigDecimal> currencyConversion = getCurrencyConversion(List.of(mdlConversion, eurConversion));
                mdlAmount = currencyConversion.get(GdsCatalogValues.CurrencyValue.MDL.getEntityUniqueId());
                eurAmount = currencyConversion.get(GdsCatalogValues.CurrencyValue.EUR.getEntityUniqueId());
            }

            itemData.put(SwiftPaymentOrderInstruction.FN_AMOUNTINMDL, mdlAmount);
            itemData.put(SwiftPaymentOrderInstruction.FN_AMOUNTINEUR, eurAmount);
            log.debug("Calculated amounts mdlAmount: {} eurAmount: {}", mdlAmount, eurAmount);
        } else {
            log.debug("No currency limit conversion for legal client");
        }

        return itemData;
    }

    private Map<String, BigDecimal> getCurrencyConversion(final List<CurrencyConversionRequestPackageJson> conversions) {
        log.debug("Converting currencies: {}", conversions);
        final CurrencyConversionResponseJson currencyConversionResponseJson =
            xcrIntegrationApiConnector.convertCurrencies(new CurrencyConversionRequestJson(conversions, null));

        final Map<String, BigDecimal> result = new HashMap<>();
        currencyConversionResponseJson.getPackages().forEach(p -> p.getConversions().forEach(c -> {
            c.getTo().forEach((k, v) -> {
                log.debug("Converted amount: {} {}", v.getAmount(), k);
                result.put(k, v.getAmount());
            });
        }));
        return result;
    }

    /**
     * Enriches the instruction's client data.
     *
     * @param client Client from GDS
     * @param itemData the item data
     */
    private void enrichClientData(@Nonnull final Client client, @Nonnull final ObjectNode itemData) {
        final var residency = Optional.ofNullable(client.getResidency()).map(ResidencyType::getId).orElse(null);
        itemData.put(SwiftPaymentOrderInstruction.FN_CLIENTNAME, client.getFullName());
        itemData.put(SwiftPaymentOrderInstruction.FN_FISCALCODE, client.getFiscalCode());
        itemData.put(SwiftPaymentOrderInstruction.FN_RESIDENCY, residency);
    }

    /**
     * Enriches the instruction by transaction static data.
     *
     * @param itemData the item data
     */
    private void enrichByTransactionStaticData(@Nonnull final ObjectNode itemData) {
        itemData.put(SwiftPaymentOrderInstruction.FN_BANKNAME, VbConstants.VICTORIA_BANK_NAME);
        itemData.put(SwiftPaymentOrderInstruction.FN_BANKBICCODE, VbConstants.VICTORIA_BANK_BIC);
        itemData.put(SwiftPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE, VbConstants.BIS_TYPE_70);
    }

    /**
     * Enriches the instruction by branch id.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByAccountData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (containsUnknownEntityRelation(context)) {
            return;
        }
        final Optional<String> debitAccountIdOpt = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (debitAccountIdOpt.isEmpty()) {
            log.error("Account id is missing in the instruction");
            return;
        }
        final String debitAccountId = debitAccountIdOpt.get();
        itemData.put(FN_CBSPAYMENTTYPE, getCbsPaymentType(debitAccountId).getCode());
        setAccountNumber(context, itemData, SwiftPaymentOrderInstruction.FN_ACCOUNTNUMBER);
    }


    /**
     * Enriches {@link SwiftPaymentOrderInstruction#FN_ATTACHMENTS}.
     *
     * @param itemData item data to be enriched
     */
    private void enrichAttachments(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        final var attachmentsJson = itemData.get(SwiftPaymentOrderInstruction.FN_ATTACHMENTS);
        if (attachmentsJson == null) {
            return;
        }

        try {
            // Enrich ATTACHMENTS.
            final var attachments = objectMapper.treeToValue(attachmentsJson, ATTACHMENTS_TYPE_REFERENCE);
            attachments.forEach((attachmentId, swiftPaymentOrderAttachment) -> {
                instructionAttachmentService.findAttachment(Long.valueOf(attachmentId))
                    .map(InstructionAttachment::getFileMetadata)
                    .flatMap(FileMetadata::getFilename)
                    .ifPresentOrElse(filename -> {
                        if (swiftPaymentOrderAttachment.getFilename() != null) {
                            log.warn("The filename is present before enriching and will be overridden, former filename : {}",
                                swiftPaymentOrderAttachment.getFilename());
                        }
                        log.debug("Enriched attachment '{}' with filename : {}", attachmentId, filename);
                        swiftPaymentOrderAttachment.setFilename(filename);
                    },
                    () -> log.warn("No filename is available for attachment : {}", attachmentId));
            });
            final var updatedAttachmentsJson = objectMapper.valueToTree(attachments);
            itemData.set(SwiftPaymentOrderInstruction.FN_ATTACHMENTS, updatedAttachmentsJson);

            log.debug("Enriched instruction(id={}) with {} attachments : {}",
                context.getInstructionId(), attachments.size(), attachments);
        } catch (JsonProcessingException e) {
            log.error("Cannot enrich TPM attachments", e);
        }
    }

    /**
     * Gets the CBS payment type for the account.
     *
     * @param accountId account id
     * @return CBS payment type
     */
    private CbsPaymentType getCbsPaymentType(@Nonnull final String accountId) {
        if (ExternalSystem.T24.isPrefixed(accountId)) {
            return CbsPaymentType.SFA;
        } else if (ExternalSystem.WAY4.isPrefixed(accountId)) {
            return CbsPaymentType.SFC;
        }
        log.error("Unknown CBS payment type for account id {}", accountId);
        return CbsPaymentType.NONE;
    }
}
