/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import static cz.finshape.vb.domain.internal.tpm.InstructionType.ADD_ACCOUNT_WITH_CARD_REQUEST;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.ADD_CARD_REQUEST;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.ADD_CURRENT_ACCOUNT_REQUEST;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.ADD_PHYSICAL_CARD_TO_DIGITAL_REQUEST;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.CORP_OWN_ACCOUNT_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.DOMESTIC_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.INTRABANK_FCY_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.INTRABANK_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.IPS_M2M_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.IPS_PHONE_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.IPS_QRC_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.IPS_RTP_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.MPAY_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.OWN_ACCOUNT_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.P2P_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.REISSUE_CARD_REQUEST;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.SWIFT_PAYMENT_ORDER;

import java.util.Optional;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.domain.internal.tpm.InstructionType;
import cz.finshape.vb.tpm.ext.context.VbPluginContext;
import cz.finshape.vb.tpm.ext.context.VbRestRequestPluginContextProvider;
import cz.finshape.vb.tpm.ext.support.InstructionItemUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.context.PluginContextHolder;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricher;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for payment instructions which adds IP address to the instruction.
 *
 * @see VbPluginContext
 * @see VbRestRequestPluginContextProvider
 *
 * <AUTHOR> Jirsa
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class IpAddressEnricher implements InstructionItemEnricher {

    private static final String UNKNOWN_IP_ADDRESS = "UNKNOWN";

    // This field has a common name.
    private static final String FN_IPADDRESS = "ipAddress";

    private static final Set<InstructionType> AVAILABLE_INSTRUCTION_TYPES = Set.of(
        ADD_ACCOUNT_WITH_CARD_REQUEST,
        ADD_CARD_REQUEST,
        ADD_CURRENT_ACCOUNT_REQUEST,
        ADD_PHYSICAL_CARD_TO_DIGITAL_REQUEST,
        CORP_OWN_ACCOUNT_PAYMENT_ORDER,
        DOMESTIC_PAYMENT_ORDER,
        INTRABANK_FCY_PAYMENT_ORDER,
        INTRABANK_PAYMENT_ORDER,
        IPS_M2M_PAYMENT_ORDER,
        IPS_PHONE_PAYMENT_ORDER,
        IPS_QRC_PAYMENT_ORDER,
        IPS_RTP_PAYMENT_ORDER,
        MPAY_PAYMENT_ORDER,
        OWN_ACCOUNT_PAYMENT_ORDER,
        P2P_PAYMENT_ORDER,
        REISSUE_CARD_REQUEST,
        SWIFT_PAYMENT_ORDER
    );

    private final PluginContextHolder pluginContextHolder;

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (AVAILABLE_INSTRUCTION_TYPES.stream().noneMatch(instructionType -> instructionType.equalsInstructionTypeCode(type))) {
            return itemData;
        }
        final Optional<String> ipAddressOpt = InstructionItemUtils.getStringValue(itemData, FN_IPADDRESS);
        if (ipAddressOpt.isPresent() && StringUtils.isNotBlank(ipAddressOpt.get())) {
            // This should happen for batch payments as the IP address is provided via mapping in Unmarshaller.
            log.debug("The IP address is already present and it will not be replaced for instruction: {}", ipAddressOpt.get());
            return itemData;
        }

        log.trace("Enriching instruction of type {} with {}", type, FN_IPADDRESS);
        itemData.put(FN_IPADDRESS, getVbPluginContext()
            .map(VbPluginContext::getIpAddress)
            .orElseGet(() -> {
                log.warn("IP address not found for instruction: {}", context.getInstruction().getInstructionId());
                return UNKNOWN_IP_ADDRESS;
            }));
        return itemData;
    }

    /**
     * Get IP address from plugin VB extra context.
     *
     * @return IP address
     */
    private Optional<VbPluginContext> getVbPluginContext() {
        return pluginContextHolder.getContext();
    }
}
