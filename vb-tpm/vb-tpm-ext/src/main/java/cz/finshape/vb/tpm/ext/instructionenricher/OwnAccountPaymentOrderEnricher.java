/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_ACCOUNTNUMBER;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_AMOUNT;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_BUYRATE;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CALCULATEDCREDITEDAMOUNT;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CALCULATEDCREDITEDAMOUNTINEUR;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CALCULATEDDEBITEDAMOUNT;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CBSPAYMENTTYPE;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CLIENTLEGALCATEGORY;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CLIENTNAME;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CLIENTTARIFFPACKET;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CREDITEDACCOUNTCURRENCY;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_CURRENCY;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_DEBITEDACCOUNTCURRENCY;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_EXCHANGERATE;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_INDIVIDUALRATEUSED;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_PARTNERACCOUNT;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_PARTNERACCOUNTNUMBER;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_PARTNERFISCALCODE;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_PARTNERNAME;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE;
import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_SELLRATE;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.CORP_OWN_ACCOUNT_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.OWN_ACCOUNT_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.xcr.ExchangeRateType.BUY;
import static cz.finshape.vb.domain.internal.xcr.ExchangeRateType.SELL;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.xcr.XcrIntegrationApiConnector;
import cz.finshape.vb.core.client.xcr.json.ConversionJson;
import cz.finshape.vb.core.client.xcr.json.ConversionRequestJson;
import cz.finshape.vb.core.client.xcr.json.CurrencyConversionExchangeRateJson;
import cz.finshape.vb.core.client.xcr.json.CurrencyConversionRequestJson;
import cz.finshape.vb.core.client.xcr.json.CurrencyConversionRequestPackageJson;
import cz.finshape.vb.core.client.xcr.json.CustomRateJson;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.tpm.CorpOwnAccountPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues.CurrencyValue;
import cz.finshape.vb.domain.internal.xcr.Currency;
import cz.finshape.vb.domain.internal.xcr.MarketType;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import cz.finshape.vb.tpm.ext.support.InstructionItemUtils;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for {@link OwnAccountPaymentOrderInstruction} and {@link CorpOwnAccountPaymentOrderInstruction}.
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/OwnAccountsPaymentOrder#OwnAccountsPaymentOrder-Instructionenriching">Confluence</a>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Cernil
 */
@Component
@Slf4j
public class OwnAccountPaymentOrderEnricher extends VbInstructionItemEnricher {

    private final TpmInstructionService tpmInstructionService;
    private final XcrIntegrationApiConnector xcrIntegrationApiConnector;

    /**
     * Instantiates a new Own account payment order enricher.
     *
     * @param tpmInstructionService TPM enricher service
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param xcrIntegrationApiConnector xcr connector
     */
    public OwnAccountPaymentOrderEnricher(
        final TpmInstructionService tpmInstructionService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final XcrIntegrationApiConnector xcrIntegrationApiConnector) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
        this.xcrIntegrationApiConnector = xcrIntegrationApiConnector;
    }

    @Override
    public ObjectNode enrich(@Nonnull final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var instructionTypeCode = context.getInstructionTypeDefinition().getCode();
        if (!OWN_ACCOUNT_PAYMENT_ORDER.equalsInstructionTypeCode(instructionTypeCode)
            && !CORP_OWN_ACCOUNT_PAYMENT_ORDER.equalsInstructionTypeCode(instructionTypeCode)) {

            return itemData;
        }
        log.debug("Enriching {} instruction : {}", instructionTypeCode, context.getInstructionId());

        enrichClientData(context, itemData);
        if (containsUnknownEntityRelation(context)) {
            return itemData;
        }
        final Optional<String> debitAccountId = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (debitAccountId.isPresent()) {
            // enrich the account data
            enrichAccountData(itemData, debitAccountId.get());
        } else {
            log.warn("debitAccountId not found for instruction: {}", context.getInstruction().getInstructionId());
        }
        return itemData;
    }

    /**
     * Enriches instruction's client data.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichClientData(@Nonnull final InstructionItemEnricherContext context,
                                  @Nonnull final ObjectNode itemData) {

        if (isUnknownOwnership(context)) {
            return;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client clientsData = tpmInstructionService.getClientData(externalClientId);
        itemData.put(FN_CLIENTNAME, clientsData.getFullName());
        itemData.put(FN_PARTNERNAME, clientsData.getFullName());
        itemData.put(FN_CLIENTLEGALCATEGORY, clientsData.getLegalCategory().getId());
        itemData.put(FN_CLIENTTARIFFPACKET, clientsData.getTariffPacket());
        itemData.put(FN_PARTNERFISCALCODE, clientsData.getFiscalCode());
    }

    /**
     * Enriches various values based on account data.
     *
     * @param itemData item data
     * @param debitAccountId debit account id
     */
    private void enrichAccountData(@Nonnull final ObjectNode itemData,
                                   @Nonnull final String debitAccountId) {

        final String creditAccountId = itemData.get(FN_PARTNERACCOUNT).asText();

        final ExternalSystem debitSystem = ExternalSystem.getExternalSystemFromPrefix(debitAccountId);
        final ExternalSystem creditSystem;
        try {
             creditSystem = ExternalSystem.getExternalSystemFromPrefix(creditAccountId);
        } catch (IllegalArgumentException e) {
            log.warn("Unknown external system: {}", creditAccountId);
            itemData.set(FN_PARTNERACCOUNTNUMBER, null);
            return;
        }

        final Account debitAcc = gdsIntegrationApiConnector.findEntityById(
            Account.EN_PLURAL,
            debitAccountId,
            Account.FN_CURRENCY,
            Account.FN_IBAN);

        final Account creditAcc = gdsIntegrationApiConnector.findEntityById(
            Account.EN_PLURAL,
            creditAccountId,
            Account.FN_CURRENCY,
            Account.FN_IBAN);

        if (creditAcc == null) {
            log.warn("Credit account not found for bisAccountId: {}", creditAccountId);
            return;
        }

        if (debitAcc == null) {
            log.warn("Debit account not found for bisAccountId: {}", debitAccountId);
            return;
        }

        itemData.put(FN_ACCOUNTNUMBER, debitAcc.getIban());
        itemData.put(FN_PARTNERACCOUNTNUMBER, creditAcc.getIban());

        final String creditAccountCurrency = creditAcc.getCurrency().getId();
        final String debitAccountCurrency = debitAcc.getCurrency().getId();

        itemData.put(FN_PAYMENTORDERBISTYPE, getPaymentOrderBisType(debitAccountCurrency, creditAccountCurrency));
        itemData.put(FN_CBSPAYMENTTYPE, getCbsPaymentType(debitSystem, creditSystem, debitAccountCurrency, creditAccountCurrency));
        itemData.put(FN_DEBITEDACCOUNTCURRENCY, debitAccountCurrency);
        itemData.put(FN_CREDITEDACCOUNTCURRENCY, creditAccountCurrency);

        if (!debitAccountCurrency.equals(creditAccountCurrency)) {
            log.info("debitCurrency {} and creditCurrency {} differs", debitAccountCurrency, creditAccountCurrency);
            final Optional<BigDecimal> amount = InstructionItemUtils.getBigDecimalValue(itemData, FN_AMOUNT);
            final Optional<String> currency = InstructionItemUtils.getStringValue(itemData, FN_CURRENCY);
            final Optional<BigDecimal> xRate = InstructionItemUtils.getBigDecimalValue(itemData, FN_EXCHANGERATE);
            if (amount.isEmpty() || currency.isEmpty() || xRate.isEmpty()) {
                log.warn("Missing amount/currency/xRate. {}, {}, {}", amount, currency, xRate);
                return;
            }
            enrichExchangeRateData(amount.get(), currency.get(), xRate.get(), debitAccountCurrency, creditAccountCurrency, itemData);
        } else {
            log.debug("Debit and credit account currencies are equal : {}", debitAccountCurrency);
        }
    }

    private void enrichExchangeRateData(
        @Nonnull final BigDecimal amount,
        @Nonnull final String currency,
        @Nonnull final BigDecimal xRate,
        @Nonnull final String debitAccountCurrency,
        @Nonnull final String creditAccountCurrency,
        @Nonnull final ObjectNode itemData) {

        log.debug("enrichExchangeRateData (from {} {} to [{},{}] at rate {}",
            amount, currency, debitAccountCurrency, creditAccountCurrency, xRate);

        final boolean isCurrencyDebitCurrency = currency.equals(debitAccountCurrency);
        final CurrencyConversionRequestPackageJson packageJson = new CurrencyConversionRequestPackageJson();
        final Boolean happyHourUsed = InstructionItemUtils.getBooleanValue(
                itemData,
                OwnAccountPaymentOrderInstruction.FN_HAPPYHOURFLAG
            ).orElseGet(() -> {
                log.warn("Missing HappyHour flag: {}", OwnAccountPaymentOrderInstruction.FN_HAPPYHOURFLAG);
                return false;
        });

        // resolve the market type
        MarketType marketType = MarketType.STANDARD;
        if (happyHourUsed) {
            marketType = MarketType.HAPPYHOUR;
        // FN_CLIENTLEGALCATEGORY already enriched by method enrichClientData
        } else if (GdsCatalogValues.LegalCategoryTypeValue.isCorporate(itemData.get(FN_CLIENTLEGALCATEGORY).textValue())) {
            marketType = MarketType.CORPORATE;
        }

        packageJson.setConversionExchangeRateSetId(marketType.getId());
        packageJson.setWithConversionDetail(true);
        packageJson.setConversions(List.of(
            ConversionRequestJson.of(BigDecimal.ONE, debitAccountCurrency, creditAccountCurrency), // 1 debit => ? credit
            ConversionRequestJson.of(BigDecimal.ONE, creditAccountCurrency, debitAccountCurrency) // 1 credit => ? debit
        ));

        // first check the buy/sell rates and find out, if you are using the individual rate
        var response = xcrIntegrationApiConnector.convertCurrencies(new CurrencyConversionRequestJson(packageJson));

        List<ConversionJson> conversions = response.getPackages().get(0).getConversions();
        BigDecimal buyRate = null;
        BigDecimal sellRate = null;

        for (ConversionJson conversion : conversions) {
            for (CurrencyConversionExchangeRateJson detail : conversion.getDetail()) {
                if (SELL.name().equals(detail.getRateType())) {
                    sellRate = detail.getRate();
                } else if (BUY.name().equals(detail.getRateType())) {
                    buyRate = detail.getRate();
                }
            }
        }

        final boolean individualRateUsed = xRate.compareTo(buyRate) != 0 && xRate.compareTo(sellRate) != 0;
        CustomRateJson extendedInfo = null;
        if (individualRateUsed) {
            extendedInfo = new CustomRateJson(xRate);
            log.debug("Individual rate used: {}, buy: {}, sell: {}", xRate, buyRate, sellRate);
        }

        // reuse the packageJson and query XCR for calculated amounts (desired and EUR for check]
        packageJson.setConversions(List.of(
            isCurrencyDebitCurrency ?
                ConversionRequestJson.of(amount, debitAccountCurrency, creditAccountCurrency, extendedInfo) :
                ConversionRequestJson.of(debitAccountCurrency, amount, creditAccountCurrency, extendedInfo),
            ConversionRequestJson.of(amount, currency, CurrencyValue.EUR.getEntityUniqueId(), extendedInfo) // amount debit => EUR
        ));
        packageJson.setExtendedInfo(null);

        response = xcrIntegrationApiConnector.convertCurrencies(
            new CurrencyConversionRequestJson(List.of(packageJson), null));
        conversions = response.getPackages().get(0).getConversions();

        final BigDecimal debitAmount = conversions.get(0).getFrom().get(debitAccountCurrency).getAmount();
        final BigDecimal creditAmount = conversions.get(0).getTo().get(creditAccountCurrency).getAmount();
        final BigDecimal eurAmount = conversions.get(1).getTo().get(CurrencyValue.EUR.getEntityUniqueId()).getAmount();

        itemData.put(FN_BUYRATE, buyRate);
        itemData.put(FN_SELLRATE, sellRate);
        itemData.put(FN_CALCULATEDDEBITEDAMOUNT, debitAmount);
        itemData.put(FN_CALCULATEDCREDITEDAMOUNT, creditAmount);
        itemData.put(FN_CALCULATEDCREDITEDAMOUNTINEUR, eurAmount);
        itemData.put(FN_INDIVIDUALRATEUSED, individualRateUsed);

        log.info("enrichedExchangeRateData - list: {}, debitAmount={} {}, creditAmount={} {}, eur={}, xRate={}, buyRate={}, sellRate={}, "
                + "individualRate={}", marketType, debitAmount, debitAccountCurrency, creditAmount, creditAccountCurrency, eurAmount, xRate,
            buyRate, sellRate, individualRateUsed);
    }

    /**
     * MDL -> MDL = 1
     * FCY -> FCY = 70 (FCY - foreign currency - two same foreign currencies)
     * FCY1 - FCY2 = 20 (FCY1 - foreign currency 1, FCY2 - foreign currency 2 -> two mutually different currencies)
     *
     * @param debitAccountCurrency debit account currency
     * @param creditAccountCurrency credit account currency
     * @return payment order bis type
     */
    @Nonnull
    private String getPaymentOrderBisType(@Nonnull final String debitAccountCurrency,
                                          @Nonnull final String creditAccountCurrency) {

        if (debitAccountCurrency.equals(creditAccountCurrency)) {
            if (Currency.MDL.getId().equals(debitAccountCurrency)) {
                return VbConstants.BIS_TYPE_1;
            } else {
                return VbConstants.BIS_TYPE_70;
            }
        } else {
            return VbConstants.BIS_TYPE_20;
        }
    }

    /**
     * Gets cbsPaymentType based on various instruction values.
     *
     * @param debitSystem debit system
     * @param creditSystem credit system
     * @param debitAccountCurrency debit account currency
     * @param creditAccountCurrency credit account currency
     * @return cbsPaymentType
     */
    @Nullable
    private String getCbsPaymentType(
        final @Nonnull ExternalSystem debitSystem,
        final @Nonnull ExternalSystem creditSystem,
        final @Nonnull String debitAccountCurrency,
        final @Nonnull String creditAccountCurrency) {

        if (creditAccountCurrency.equals(debitAccountCurrency)) {
            return CbsPaymentTypeResolver.resolveSameCurrency(CurrencyValue.isLocal(creditAccountCurrency), debitSystem, creditSystem);
        }
        return CbsPaymentTypeResolver.resolveForex(debitSystem, creditSystem);
    }
}
