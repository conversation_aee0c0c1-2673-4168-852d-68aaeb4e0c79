/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.spx.SpxIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.tpm.CloseCardRequestInstruction;
import cz.finshape.vb.domain.internal.spx.way4native.SpxCardClosureEligibilityJsonResponse;
import cz.finshape.vb.domain.internal.tpm.InstructionType;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enriches card expiry date to given instructions.
 *
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+analysis+-+Close+card#TechnicalanalysisClosecard-BE-enricher">Confluence</a>
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@Component
public class CardExpiryDateEnricher extends VbInstructionItemEnricher {

    private final SpxIntegrationApiConnector spxIntegrationApiConnector;

    /**
     * Primary constructor.
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param spxIntegrationApiConnector SPX Integration API connector
     */
    public CardExpiryDateEnricher(
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final SpxIntegrationApiConnector spxIntegrationApiConnector
    ) {
        super(gdsIntegrationApiConnector);
        this.spxIntegrationApiConnector = spxIntegrationApiConnector;
    }

    @Override
    @SuppressWarnings("PMD.TooFewBranchesForSwitch") // We like switch.
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();

        final var optionalInstructionType = InstructionType.fromInstructionTypeCode(type);

        if (optionalInstructionType.isEmpty()) {
            log.error("Unknown instruction type code : {}", type);
            return itemData;
        }

        final var instructionTypeCode = optionalInstructionType.get();
        final var fnExpiryDate = switch (instructionTypeCode) {
            case CLOSE_CARD_REQUEST -> CloseCardRequestInstruction.FN_EXPIRYDATE;
            default -> null;
        };

        if (fnExpiryDate != null) {
            if (isUnknownOwnership(context)) {
                return itemData;
            }
            final String externalClientId = context.getInstruction().getOwnership().getClientId();

            final var cardIdOpt = getRelatedEntityId(context, Card.EN_SINGULAR);
            if (cardIdOpt.isEmpty()) {
                log.warn("No card relation entity for {}", instructionTypeCode);
                return itemData;
            }

            final var cardId = cardIdOpt.get();
            final SpxCardClosureEligibilityJsonResponse result = spxIntegrationApiConnector.getCardClosureEligibility(externalClientId, cardId);
            itemData.put(fnExpiryDate, result.getLocalDate().toString());
            log.debug("Enriched '{}' instruction field '{}': {}", instructionTypeCode, fnExpiryDate, result.getLocalDate());
        }

        return itemData;
    }
}
