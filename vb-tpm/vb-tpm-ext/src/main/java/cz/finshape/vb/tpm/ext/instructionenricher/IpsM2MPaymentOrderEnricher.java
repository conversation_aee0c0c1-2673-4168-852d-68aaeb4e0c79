/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;

import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.SPACE;
import static cz.finshape.vb.domain.internal.VbConstants.DateFormats.DATE_ISO_FORMATTER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.IPS_M2M_PAYMENT_ORDER;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getLocalDateValue;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getStringValue;

import java.time.LocalDate;
import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.ips.gw.IpsGwIntegrationApiConnector;
import cz.finshape.vb.core.utils.TemporalUtils;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ClientExtension;
import cz.finshape.vb.domain.dbos.gds.Country;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.dbos.tpm.IpsM2MPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for IpsM2MPaymentOrder instructions.
 *
 * <ul>
 *     <li>adds client's information</li>
 *     <li>adds creditor data information</li>
 *     <li>adds static payment information</li>
 * </ul>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Kundrat
 */
@Slf4j
@Component
public class IpsM2MPaymentOrderEnricher extends AbstractIpsEnricher {

    public static final String REMITTANNCE_INFO_MSG = "Me2Me transfer";
    public static final String TRANSACTION_CODE_M2M = "204";
    public static final String PAYMENT_ORDER_BIS_TYPE = "1";

    private final TpmInstructionService tpmInstructionService;
    private final IpsGwIntegrationApiConnector ipsGwIntegrationApiConnector;

    /**
     * Constructor.
     *
     * @param tpmInstructionService TPM instruction service
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param ipsGwIntegrationApiConnector ips gw integration api connector
     */
    public IpsM2MPaymentOrderEnricher(
        final TpmInstructionService tpmInstructionService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final IpsGwIntegrationApiConnector ipsGwIntegrationApiConnector) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
        this.ipsGwIntegrationApiConnector = ipsGwIntegrationApiConnector;
    }

    @Override
    public ObjectNode enrich(@Nonnull final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!IPS_M2M_PAYMENT_ORDER.equalsInstructionTypeCode(type)) {
            return itemData;
        }
        log.debug("Enriching instruction of type {}", type);

        enrichByTransactionStaticData(itemData);
        enrichByAccountData(context, itemData);
        enrichByClientData(context, itemData);
        enrichByDataFromCas(itemData);
        enrichByPaymentType(itemData, context);

        return itemData;
    }

    /**
     * Enriches the instruction by client data.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByClientData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (isUnknownOwnership(context)) {
            return;
        }
        if (context.getInstruction().getOwnership() == null || !context.getInstruction().getOwnership().isOwnedByClient()) {
            log.error("Client id is missing in the instruction");
            return;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client clientsData = tpmInstructionService.getClientData(externalClientId);
        final ClientExtension clientAddress = tpmInstructionService.getClientAddress(externalClientId);
        final var residency = Optional.ofNullable(clientsData.getResidency()).map(ResidencyType::getId).orElse(null);

        itemData.put(IpsM2MPaymentOrderInstruction.FN_CLIENTNAME, clientsData.getFirstName() + SPACE + clientsData.getSurname());
        itemData.put(IpsM2MPaymentOrderInstruction.FN_FISCALCODE, clientsData.getFiscalCode());

        final var country = ofNullable(clientAddress.getCountry())
            .map(Country::getId)
            .orElseGet(() -> {
                log.warn("Country is not present in client data, using default country");
                return GdsCatalogValues.CountryValue.MOLDOVA.getEntityUniqueId();
            });
        itemData.put(IpsM2MPaymentOrderInstruction.FN_CLIENTCOUNTRY, country);
        itemData.put(IpsM2MPaymentOrderInstruction.FN_RESIDENCY, residency);
    }

    /**
     * Enriches the instruction by data from CAS account info.
     *
     * @param itemData the item data
     */
    private void enrichByDataFromCas(final ObjectNode itemData) {
        final Optional<String> iban = getStringValue(itemData, IpsM2MPaymentOrderInstruction.FN_PARTNERACCOUNTNUMBER);
        if (iban.isEmpty()) {
            log.warn("Iban is not present in instruction data");
            return;
        }

        final String bic = getStringValue(itemData, IpsM2MPaymentOrderInstruction.FN_PARTNERBIC).orElse(null);
        final String memberId = getStringValue(itemData, IpsM2MPaymentOrderInstruction.FN_PARTNERMEMBERID).orElse(null);

        final var accountData = ipsGwIntegrationApiConnector.getAccountInfoByIban(iban.get(), bic, memberId);
        if (isEmpty(accountData)) {
            log.warn("Account data not found for iban: {}", iban);
            return;
        }

        final var partnerName = accountData.getName()
            + (isNotEmpty(accountData.getSurname()) ? SPACE + accountData.getSurname() : StringUtils.EMPTY);

        itemData.put(IpsM2MPaymentOrderInstruction.FN_PARTNERNAME, partnerName);
        itemData.put(IpsM2MPaymentOrderInstruction.FN_PARTNERCOUNTRY, accountData.getAddress() == null ? null :
                                                                                                         accountData.getAddress().getCountry());
        itemData.put(IpsM2MPaymentOrderInstruction.FN_PARTNERFISCALCODE, accountData.getTaxNumber());
    }

    /**
     * Enriches the instruction by transaction static data.
     *
     * @param itemData the item data
     */
    private void enrichByTransactionStaticData(@Nonnull final ObjectNode itemData) {
        final Optional<LocalDate> valueDate = getLocalDateValue(itemData, IpsM2MPaymentOrderInstruction.FN_VALUEDATE);

        if (valueDate.isEmpty()) {
            itemData.put(IpsM2MPaymentOrderInstruction.FN_VALUEDATE, TemporalUtils.nowInMoldova().format(DATE_ISO_FORMATTER));
        }

        itemData.put(IpsM2MPaymentOrderInstruction.FN_BIC, VbConstants.VICTORIA_BANK_BIC);
        itemData.put(IpsM2MPaymentOrderInstruction.FN_REMITTANCEINFORMATION, REMITTANNCE_INFO_MSG);
        itemData.put(IpsM2MPaymentOrderInstruction.FN_TRANSACTIONCODE, TRANSACTION_CODE_M2M);
        itemData.put(IpsM2MPaymentOrderInstruction.FN_BANKNAME, VbConstants.VICTORIA_BANK_NAME.replace(".", EMPTY));
        itemData.put(IpsM2MPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE, PAYMENT_ORDER_BIS_TYPE);
    }

    /**
     * Enriches the instruction by branch id.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByAccountData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (containsUnknownEntityRelation(context)) {
            return;
        }
        final Optional<String> debitAccountIdOpt = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (debitAccountIdOpt.isEmpty()) {
            log.error("Account id is missing in the instruction (instructionId={})", context.getInstruction().getInstructionId());
            return;
        }
        setAccountNumber(context, itemData, IpsM2MPaymentOrderInstruction.FN_ACCOUNTNUMBER);
    }

    @Override
    protected String getPaymentTypePrefix() {
        return CbsPaymentType.IPS_M2M_PREFIX;
    }
}
