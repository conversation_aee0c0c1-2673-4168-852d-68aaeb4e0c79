/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.tpm.P2PPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException;
import cz.finshape.vb.domain.internal.tpm.InstructionType;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enriches selected card instructions with masked number.
 *
 * <AUTHOR>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Activate+Card">Activate Card</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+analyses+-+Block+card">Block Card</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+analysis+-+Close+card">Close Card</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+analyses+-+Freeze+card">Freeze Card</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Unfreeze+Card">Unfreeze Card</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Manage+Limits">Manage Limits</a>
 * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?pageId=273914682">View PAN/CVV/Expiry</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Get+PIN">View PIN</a>
 * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?pageId=273913422">Create/Set PIN</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+analyses+-++Create+VISA+alias#TechnicalanalysesCreateVISAalias-Instructionenriching-BE">CreateP2P alias</a>
 * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?spaceKey=P20009724&title=Technical+analyses+-+Create+a+payment+with+alias#TechnicalanalysesCreateapaymentwithalias-BE-enriched">P2P Payment Order</a>

 */
@Slf4j
@Component
public class CardMaskedNumberEnricher extends VbInstructionItemEnricher {

    public static final String FN_MASKEDNUMBER = "maskedNumber";

    public static final String FN_CARDMASKEDNUMBER = "cardMaskedNumber";

    /**
     * Primary constructor.
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     */
    public CardMaskedNumberEnricher(final GdsIntegrationApiConnector gdsIntegrationApiConnector) {
        super(gdsIntegrationApiConnector);
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        final var optionalInstructionType = InstructionType.fromInstructionTypeCode(type);
        if (optionalInstructionType.isEmpty()) {
            log.error("Unknown instruction type code : {}", type);
            return itemData;
        }
        final var instructionTypeCode = optionalInstructionType.get();
        return switch (instructionTypeCode) {
            case CREATE_P2P_ALIAS_REQUEST,
                 ADD_CARD_P2P_ALIAS_REQUEST -> {
                itemData.put(FN_CARDMASKEDNUMBER, getCardMaskedNumber(context));
                yield itemData;
            }
            case ACTIVATE_CARD_REQUEST,
                 ADD_PHYSICAL_CARD_TO_DIGITAL_REQUEST,
                 BLOCK_CARD_REQUEST,
                 CORP_SET_CARD_INTERNET_TRANS_REQUEST,
                 CORP_SET_CARD_LIMITS_REQUEST,
                 CLOSE_CARD_REQUEST,
                 FREEZE_CARD_REQUEST,
                 REISSUE_CARD_REQUEST,
                 SET_CARD_INTERNET_TRANS_REQUEST,
                 SET_CARD_LIMITS_REQUEST,
                 SET_CARD_PIN_REQUEST,
                 UNFREEZE_CARD_REQUEST,
                 VIEW_CARD_PAN_CVV_REQUEST,
                 VIEW_CARD_PIN_REQUEST -> {
                itemData.put(FN_MASKEDNUMBER, getCardMaskedNumber(context));
                yield itemData;
            }
            case P2P_PAYMENT_ORDER -> {
                itemData.put(P2PPaymentOrderInstruction.FN_DEBITEDCARDMASKEDNUMBER, getCardMaskedNumber(context));
                yield itemData;
            }
            default -> itemData;
        };
    }

    /**
     * Retrieves the masked number of the card related to the instruction.
     *
     * @return Masked number of the card
     */
    @Nullable
    private String getCardMaskedNumber(@Nonnull final InstructionItemEnricherContext context) {
        if (containsUnknownEntityRelation(context)) {
            return null;
        }
        final var cardRelation = Card.EN_SINGULAR;
        final var cardId = getRelatedEntityId(context, cardRelation)
            .orElseThrow(() -> new VbBusinessException(VbErrorCodeEnum.ARGUMENT_NULL, "Missing %s relation".formatted(cardRelation)));

        final var card = gdsIntegrationApiConnector.<Card>getEntityById(Card.EN_PLURAL, cardId, Card.FN_MASKEDNUMBER);
        final var cardMaskedNumber = card.getMaskedNumber();
        log.info(
            "Enriched instruction(id={}, type={}, item.id={}) with maskedNumber={} for card={}",
            context.getInstructionId(),
            context.getInstructionTypeDefinition().getCode(),
            context.getInstructionItemId().orElse(null),
            cardMaskedNumber,
            cardId);
        return cardMaskedNumber;
    }
}
