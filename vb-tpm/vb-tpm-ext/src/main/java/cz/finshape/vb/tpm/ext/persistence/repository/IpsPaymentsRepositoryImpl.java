/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;

import java.math.BigDecimal;
import java.time.LocalDate;

import javax.sql.DataSource;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Repository;

import com.querydsl.core.types.dsl.Expressions;
import cz.finshape.vb.tpm.ext.generated.QVbipspayments;
import cz.finshape.vb.tpm.ext.generated.Vbipspayments;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.commons.spring.business.framework.resources.dao.impl.AbstractDSLDao;


/**
 * IPS of the {@link DomesticPaymentsRepository}.
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class IpsPaymentsRepositoryImpl extends AbstractDSLDao implements IpsPaymentsRepository {

    private static final QVbipspayments Q_VBIPSPAYMENTS = QVbipspayments.vbipspayments;

    /**
     * Primary constructor.
     *
     * @param dataSource data source
     */
    public IpsPaymentsRepositoryImpl(final DataSource dataSource) {
        super(dataSource);
    }

    @Override
    public void registerOrUpdatePayment(@Nonnull final Vbipspayments paymentLimit) {
        final var instructionId = paymentLimit.getInstructionId();
        final var clientId = paymentLimit.getClientId();

        final var whereExpression = Q_VBIPSPAYMENTS.instructionid.eq(instructionId)
                                                                 .and(Q_VBIPSPAYMENTS.clientid.eq(clientId));

        final var notExisting = withQuery(query -> query.select()
            .select(Expressions.constant(1)))
            .from(Q_VBIPSPAYMENTS)
            .where(whereExpression)
            .fetchResults()
            .isEmpty();

        // Make sure the record is active.
        paymentLimit.setActive(true);

        if (notExisting) {
            withInsertClause(Q_VBIPSPAYMENTS, clause -> clause
                .populate(paymentLimit)
                .execute());
            log.debug("Created a record for instructionId={} and clientId={}",
                paymentLimit.getInstructionId(), paymentLimit.getClientId());
        } else {
            final var count = withUpdateClause(Q_VBIPSPAYMENTS, clause -> clause
                .populate(paymentLimit)
                .where(whereExpression)
                .execute());
            log.debug("Updated {} record(s) where instructionId={} and clientId={}",
                count, instructionId, clientId);
        }
    }

    @Nonnull
    @Override
    public BigDecimal sumDailyAmount(@Nonnull final String externalClientId, @Nonnull final LocalDate dateOfTransaction) {
        final var queryResult = withQuery(query -> query
            .select(Q_VBIPSPAYMENTS.amountinmdl.sum()))
            .from(Q_VBIPSPAYMENTS)
            .where(Q_VBIPSPAYMENTS.clientid.eq(externalClientId)
                .and(Q_VBIPSPAYMENTS.active.isTrue())
                .and(Q_VBIPSPAYMENTS.signedat.after(dateOfTransaction.atStartOfDay()))
            )
            .fetch();

        return queryResult.get(0) == null ? BigDecimal.ZERO : queryResult.get(0);
    }

    @Override
    public long countDailyTransactions(@Nonnull final String externalClientId, @Nonnull final LocalDate dateOfTransaction) {
        final var queryResult = withQuery(query -> query
            .select(Q_VBIPSPAYMENTS.instructionid.count()))
            .from(Q_VBIPSPAYMENTS)
            .where(Q_VBIPSPAYMENTS.clientid.eq(externalClientId)
                .and(Q_VBIPSPAYMENTS.active.isTrue())
                .and(Q_VBIPSPAYMENTS.signedat.after(dateOfTransaction.atStartOfDay()))
            )
            .fetch();

        return queryResult.get(0) == null ? 0L : queryResult.get(0);
    }

    @Nonnull
    @Override
    public BigDecimal sumMonthlyAmount(@Nonnull final String externalClientId, @Nonnull final LocalDate dateOfTransaction) {
        final var queryResult = withQuery(query -> query
            .select(Q_VBIPSPAYMENTS.amountinmdl.sum()))
            .from(Q_VBIPSPAYMENTS)
            .where(Q_VBIPSPAYMENTS.clientid.eq(externalClientId)
                .and(Q_VBIPSPAYMENTS.active.isTrue())
                .and(Q_VBIPSPAYMENTS.signedat.after(dateOfTransaction.withDayOfMonth(1).atStartOfDay()))
            )
            .fetch();

        return queryResult.get(0) == null ? BigDecimal.ZERO : queryResult.get(0);
    }

    @Override
    public long deactivatePayment(@Nonnull final Long instructionId, @Nonnull final String clientExternalId) {
        final var count = withUpdateClause(Q_VBIPSPAYMENTS, clause -> clause
            .where(Q_VBIPSPAYMENTS.instructionid.eq(instructionId)
                .and(Q_VBIPSPAYMENTS.clientid.eq(clientExternalId))
                .and(Q_VBIPSPAYMENTS.active.isTrue()))
            .set(Q_VBIPSPAYMENTS.active, false) // De/activation is driven with `active` column.
            .execute());
        log.debug("Deactivated {} record(s) where instructionId={} and clientExternalId={}",
            count, instructionId, clientExternalId);
        return count;
    }

    @Override
    public long removePayments(@Nonnull final LocalDate signedBefore) {
        final var count = withDeleteClause(Q_VBIPSPAYMENTS, clause -> clause
            .where(Q_VBIPSPAYMENTS.signedat.before(signedBefore.atStartOfDay()))
            .execute());
        log.debug("Removed {} record(s) signed before {}", count, signedBefore);
        return count;
    }
}
