/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;

import static cz.finshape.vb.domain.internal.tpm.InstructionType.IPS_PHONE_PAYMENT_ORDER;

import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.ips.gw.IpsGwIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ClientExtension;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.dbos.tpm.DomesticPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.IpsPhonePaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.domain.thirdparty.generated.ipsx.gateway.model.CustomerAccountByAlias;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for IpsPhonePaymentOrderEnricher instructions.
 *
 * <ul>
 *     <li>adds bank's information</li>
 *     <li>adds payment information</li>
 *     <li>adds account bisAccountId dependant data</li>
 *     <li>adds clientName and (depend on client id)</li>
 * </ul>
 * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?spaceKey=P20009724&title=01+Technical+analysis+-+Payment+by+Phone+Number">Payment by phone number analysis</a>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Jirsa
 * */
@Slf4j
@Component
public class IpsPhonePaymentOrderEnricher extends AbstractIpsEnricher {

    private static final String IPS_PHONE_TRANSACTION_CODE_INDIVIDUAL = "201";
    private static final String IPS_PHONE_TRANSACTION_CODE_LEGAL = "301";
    private final static String PAYMENT_ORDER_BIS_TAPE = "1";
    private final static String IPSX_CUSTOMER_TYPE_FIELD = "customerType";
    private final static String IPSX_CUSTOMER_TYPE_INDIVIDUAL = "Individual";

    private static final String IPS_PHONE_TRANSACTION_REMITTANCE_INFORMATION = "MIA P2P transfer by Alias";

    private final TpmInstructionService tpmInstructionService;
    private final IpsGwIntegrationApiConnector ipsGwIntegrationApiConnector;

    /**
     * Instantiates a new IPS phone payment order enricher.
     * @param tpmInstructionService TPM instruction service
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param ipsGwIntegrationApiConnector ips gw integration api connector
     */
    public IpsPhonePaymentOrderEnricher(
        final TpmInstructionService tpmInstructionService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final IpsGwIntegrationApiConnector ipsGwIntegrationApiConnector) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
        this.ipsGwIntegrationApiConnector = ipsGwIntegrationApiConnector;
    }

    @Override
    public ObjectNode enrich(@Nonnull final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!IPS_PHONE_PAYMENT_ORDER.equalsInstructionTypeCode(type)) {
            return itemData;
        }
        log.debug("Enriching instruction of type {}", IPS_PHONE_PAYMENT_ORDER);

        enrichByTransactionData(itemData);
        enrichByAccountData(context, itemData);
        enrichByClientData(context, itemData);
        enrichByIPSXData(itemData);
        enrichByPaymentType(itemData, context);

        return itemData;
    }

    /**
     * Enriches the instruction by transaction static data.
     *
     * @param itemData the item data
     */
    private void enrichByTransactionData(@Nonnull final ObjectNode itemData) {
        itemData.put(IpsPhonePaymentOrderInstruction.FN_BIC, VbConstants.VICTORIA_BANK_BIC);
        itemData.put(IpsPhonePaymentOrderInstruction.FN_BANKNAME, VbConstants.VICTORIA_BANK_NAME);
        itemData.put(DomesticPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE, PAYMENT_ORDER_BIS_TAPE);

        if (itemData.get(IpsPhonePaymentOrderInstruction.FN_REMITTANCEINFORMATION) == null ||
            itemData.get(IpsPhonePaymentOrderInstruction.FN_REMITTANCEINFORMATION).textValue().isEmpty()) {

            itemData.put(IpsPhonePaymentOrderInstruction.FN_REMITTANCEINFORMATION, IPS_PHONE_TRANSACTION_REMITTANCE_INFORMATION);
        }
    }

    /**
     * Enriches the instruction by account data
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByAccountData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (containsUnknownEntityRelation(context)) {
            return;
        }
        final Optional<String> debitAccountIdOpt = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (debitAccountIdOpt.isEmpty()) {
            log.error("Account id is missing in the instruction (instructionId={})", context.getInstruction().getInstructionId());
            return;
        }
        setAccountNumber(context, itemData, IpsPhonePaymentOrderInstruction.FN_ACCOUNTNUMBER);
    }

    /**
     * Enriches the instruction by client data.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByClientData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (isUnknownOwnership(context)) {
            return;
        }
        if (context.getInstruction().getOwnership() == null || !context.getInstruction().getOwnership().isOwnedByClient()) {
            log.error("Client id is missing in the instruction");
            return;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client clientsData = tpmInstructionService.getClientData(externalClientId);
        final ClientExtension clientAddress = tpmInstructionService.getClientAddress(externalClientId);
        final var residency = Optional.ofNullable(clientsData.getResidency()).map(ResidencyType::getId).orElse(null);

        itemData.put(IpsPhonePaymentOrderInstruction.FN_CLIENTNAME, clientsData.getFullName());
        itemData.put(IpsPhonePaymentOrderInstruction.FN_FISCALCODE, clientsData.getFiscalCode());
        itemData.put(IpsPhonePaymentOrderInstruction.FN_CLIENTCOUNTRY, clientAddress.getCountry() == null ?
                                                                                GdsCatalogValues.CountryValue.MOLDOVA.getEntityUniqueId() :
                                                                                clientAddress.getCountry().getId());
        itemData.put(IpsPhonePaymentOrderInstruction.FN_RESIDENCY, residency);
    }

    /**
     * Enriches the instruction by partner data.
     *
     * @param itemData the item data
     */
    private void enrichByIPSXData(@Nonnull final ObjectNode itemData) {
        final String partnerPhoneNumber = itemData.get(IpsPhonePaymentOrderInstruction.FN_PARTNERPHONENUMBER).textValue();
        final CustomerAccountByAlias partnerInfo = ipsGwIntegrationApiConnector.getAccountInfoByPhoneNumber(partnerPhoneNumber);

        itemData.put(IpsPhonePaymentOrderInstruction.FN_PARTNERBIC, partnerInfo.getServicer().getBic());
        itemData.put(IpsPhonePaymentOrderInstruction.FN_PARTNERMEMBERID, partnerInfo.getServicer().getMemberId());
        itemData.put(IpsPhonePaymentOrderInstruction.FN_PARTNERNAME, partnerInfo.getName() + " " + partnerInfo.getSurname());
        itemData.put(IpsPhonePaymentOrderInstruction.FN_PARTNERACCOUNTNUMBER, partnerInfo.getId().getIban());
        itemData.put(IpsPhonePaymentOrderInstruction.FN_PARTNERCOUNTRY, partnerInfo.getAddress().getCountry());
        itemData.put(IpsPhonePaymentOrderInstruction.FN_PARTNERFISCALCODE, partnerInfo.getTaxNumber());

        Optional.ofNullable(partnerInfo.getAdditionalDetails().get(IPSX_CUSTOMER_TYPE_FIELD))
            .ifPresent(customerType -> {
                if (IPSX_CUSTOMER_TYPE_INDIVIDUAL.equals(customerType)) {
                    itemData.put(IpsPhonePaymentOrderInstruction.FN_TRANSACTIONCODE, IPS_PHONE_TRANSACTION_CODE_INDIVIDUAL);
                } else {
                    itemData.put(IpsPhonePaymentOrderInstruction.FN_TRANSACTIONCODE, IPS_PHONE_TRANSACTION_CODE_LEGAL);
                }
            });
    }

    @Override
    protected String getPaymentTypePrefix() {
        return CbsPaymentType.IPS_P2P_PREFIX;
    }
}
