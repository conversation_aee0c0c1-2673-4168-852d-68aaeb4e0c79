/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;


import java.util.List;

import javax.sql.DataSource;

import jakarta.annotation.Nonnull;

import org.springframework.transaction.annotation.Transactional;

import cz.finshape.vb.tpm.ext.persistence.AbstractQueryDslDao;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.domain.generated.q.QTpmInstructionitem;


/**
 * VB instruction item repository.
 *
 * <AUTHOR>
 */
@Slf4j
public class VbInstructionItemRepositoryImpl extends AbstractQueryDslDao implements VbInstructionItemRepository {

    private static final QTpmInstructionitem QTPM_INSTRUCTIONITEM = QTpmInstructionitem.tpmInstructionitem;

    private static final String SEQ_VBINSTRUCTIONITEMEXTERNALID = "SEQ_VBINSTRUCTIONITEMEXTERNALID";

    /**
     * Primary constructor.
     *
     * @param dataSource data source
     */
    public VbInstructionItemRepositoryImpl(final DataSource dataSource) {
        super(dataSource);
    }

    @Nonnull
    @Transactional
    @Override
    public List<Long> getNextInstructionItemExternalIds(final int count) {
        return getNextSequenceValues(SEQ_VBINSTRUCTIONITEMEXTERNALID, count);
    }

    @Nonnull
    @Transactional
    @Override
    public List<String> findInstructionItemIds(@Nonnull final Long instructionId) {
        return createQuery()
                .select(QTPM_INSTRUCTIONITEM.id)
                .from(QTPM_INSTRUCTIONITEM)
                .where(QTPM_INSTRUCTIONITEM.instructionid.eq(instructionId))
                .orderBy(QTPM_INSTRUCTIONITEM.id.asc())
                .distinct()
                .fetch();
    }
}
