/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import static cz.finshape.vb.domain.internal.tpm.InstructionType.MPAY_PAYMENT_ORDER;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getStringValue;

import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.spx.SpxIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.tpm.MPayPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.spx.mpay.Invoices;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for {@link MPayPaymentOrderInstruction}.
 *
 * <ul>
 *     <li>adds bank's information</li>
 *     <li>adds payment information</li>
 *     <li>adds account bisAccountId dependant data</li>
 *     <li>adds clientName and (depend on client id)</li>
 *     <li>adds MPay invoice data payload into instruction</li>
 * </ul>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Jirsa
 * */
@Slf4j
@Component
public class MPayPaymentOrderEnricher extends VbInstructionItemEnricher {

    private final TpmInstructionService tpmInstructionService;
    private final SpxIntegrationApiConnector spxIntegrationApiConnector;

    /**
     * Instantiates a new MPay payment order enricher.
     * @param tpmInstructionService TPM instruction service
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param spxIntegrationApiConnector SPX Integration API connector
     */
    public MPayPaymentOrderEnricher(
        final TpmInstructionService tpmInstructionService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final SpxIntegrationApiConnector spxIntegrationApiConnector) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
        this.spxIntegrationApiConnector = spxIntegrationApiConnector;
    }

    @Override
    public ObjectNode enrich(@Nonnull final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!MPAY_PAYMENT_ORDER.getCode().equals(type)) {
            return itemData;
        }
        log.debug("Enriching instruction of type {}", type);

        enrichByTransactionStaticData(itemData);
        enrichByAccountData(context, itemData);
        enrichByClientData(context, itemData);
        enrichByInvoicePayload(itemData);
        enrichByDataFromInvoiceData(itemData);
        return itemData;
    }

    /**
     * Enriches the instruction by transaction static data.
     *
     * @param itemData the item data
     */
    private void enrichByTransactionStaticData(@Nonnull final ObjectNode itemData) {
        itemData.put(MPayPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE, VbConstants.BIS_TYPE_1);
    }

    /**
     * Enriches the instruction by branch id.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByAccountData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (containsUnknownEntityRelation(context)) {
            return;
        }
        final Optional<String> debitAccountIdOpt = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (debitAccountIdOpt.isEmpty()) {
            log.error("Account id is missing in the instruction");
            return;
        }
        itemData.put(MPayPaymentOrderInstruction.FN_CBSPAYMENTTYPE, CbsPaymentType.MPayFA.getCode());
        setAccountNumber(context, itemData, MPayPaymentOrderInstruction.FN_ACCOUNTNUMBER);
    }

    /**
     * Enriches the instruction by client data.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByClientData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (isUnknownOwnership(context)) {
            return;
        }
        if (context.getInstruction().getOwnership() == null || !context.getInstruction().getOwnership().isOwnedByClient()) {
            log.error("Client id is missing in the instruction");
            return;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client clientsData = tpmInstructionService.getClientData(externalClientId);

        itemData.put(MPayPaymentOrderInstruction.FN_CLIENTNAME, clientsData.getFullName());
    }

    /**
     * Enriches the instruction by invoice payload.
     *
     * @param itemData the item data
     */
    private void enrichByInvoicePayload(@Nonnull final ObjectNode itemData) {
        final Optional<String> invoiceNumber = getStringValue(itemData, MPayPaymentOrderInstruction.FN_INVOICENUMBER);
        if (invoiceNumber.isEmpty()) {
            log.warn("Invoice number is not present in instruction data");
        } else {
            final String invoiceData = spxIntegrationApiConnector.getMPayInvoicePayload(invoiceNumber.get());
            itemData.put(MPayPaymentOrderInstruction.FN_INVOICEPAYLOAD, invoiceData);
        }
    }

    /**
     * Enriches the instruction by data from invoice payload.
     *
     * @param itemData the item data
     */
    private void enrichByDataFromInvoiceData(final ObjectNode itemData) {
        final Optional<String> invoiceNumber = getStringValue(itemData, MPayPaymentOrderInstruction.FN_INVOICENUMBER);
        if (invoiceNumber.isEmpty()) {
            log.warn("Invoice number is not present in instruction data");
            return;
        }

        final Invoices.InvoiceDetail invoiceDetail = spxIntegrationApiConnector.getMPayInvoiceDetail(invoiceNumber.get());
        if (invoiceDetail == null || invoiceDetail.getAttributes() == null) {
            log.warn("Invoice detail not found for invoice number: {}", invoiceNumber);
            return;
        }
        itemData.put(MPayPaymentOrderInstruction.FN_PARTNERID, invoiceDetail.getOrganizationId());
        itemData.put(MPayPaymentOrderInstruction.FN_PARTNERNAME, invoiceDetail.getOrganizationName());
        itemData.put(MPayPaymentOrderInstruction.FN_PAYMENTFEE, invoiceDetail.getAttributes().get("EXTRA_COMISION"));
        itemData.put(MPayPaymentOrderInstruction.FN_REMITTANCEINFORMATION,
                            invoiceDetail.getGlobalNumber() + " " + invoiceDetail.getDestination() + " Nr. " + invoiceDetail.getOwnerKey());
    }
}
