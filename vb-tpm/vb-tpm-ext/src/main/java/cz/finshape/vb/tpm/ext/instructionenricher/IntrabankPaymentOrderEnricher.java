/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import static cz.finshape.vb.domain.dbos.tpm.IntrabankPaymentOrderInstruction.FN_BANKNAME;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankPaymentOrderInstruction.FN_CBSPAYMENTTYPE;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankPaymentOrderInstruction.FN_PARTNERBANKNAME;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE;
import static cz.finshape.vb.domain.dbos.tpm.IntrabankPaymentOrderInstruction.FN_PRIORITY;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.INTRABANK_PAYMENT_ORDER;

import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.dbos.tpm.IntrabankPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;

/**
 * Enricher for {@link IntrabankPaymentOrderInstruction}.
 *
 * <ul>
 *     <li>adds bank's information</li>
 *     <li>adds payment information</li>
 *     <li>adds account bisAccountId dependant data</li>
 *     <li>adds clientName and (depend on client id)</li>
 * </ul>
 * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?pageId=*********">01 - Payment in MDL (MB)</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/*********/Domestic+payment+order">Domestic and Intra bank payments Analysis</a>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Jirsa
 * */
@Slf4j
@Component
public class IntrabankPaymentOrderEnricher extends VbInstructionItemEnricher {

    private final TpmInstructionService tpmInstructionService;

    /**
     * Instantiates a new Intra bank payment order enricher.
     *
     * @param tpmInstructionService TPM enricher service
     * @param gdsIntegrationApiConnector GDS Integration API connector
     */
    public IntrabankPaymentOrderEnricher(
        final TpmInstructionService tpmInstructionService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!INTRABANK_PAYMENT_ORDER.equalsInstructionTypeCode(type)) {
            return itemData;
        }

        enrichByTransactionStaticData(itemData);
        enrichByAccountData(context, itemData);
        enrichClientData(context, itemData);
        return itemData;
    }

    /**
     * Enriches the instruction by transaction static data.
     *
     * @param itemData the item data
     */
    private void enrichByTransactionStaticData(@Nonnull final ObjectNode itemData) {
        itemData.put(FN_BANKNAME, VbConstants.VICTORIA_BANK_NAME);
        itemData.put(FN_PAYMENTORDERBISTYPE, VbConstants.BIS_TYPE_1);
        itemData.put(FN_PARTNERBANKNAME, VbConstants.VICTORIA_BANK_NAME);
        itemData.put(FN_PRIORITY, VbConstants.PRIORITY_NORMAL);
    }

    /**
     * Enriches the instruction by branch id.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByAccountData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (containsUnknownEntityRelation(context)) {
            return;
        }
        final Optional<String> accountIdOpt = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (accountIdOpt.isEmpty()) {
            log.error("Account id is missing in the instruction");
            return;
        }
        final String accountId = accountIdOpt.get();
        itemData.put(FN_CBSPAYMENTTYPE, getCbsPaymentType(accountId).getCode());
        setAccountNumber(context, itemData, IntrabankPaymentOrderInstruction.FN_ACCOUNTNUMBER);
    }

    /**
     * Enriches the instruction's client data.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichClientData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (isUnknownOwnership(context)) {
            return;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client clientsData = tpmInstructionService.getClientData(externalClientId);
        final var residency = Optional.ofNullable(clientsData.getResidency()).map(ResidencyType::getId).orElse(null);

        itemData.put(IntrabankPaymentOrderInstruction.FN_CLIENTNAME, clientsData.getFullName());
        itemData.put(IntrabankPaymentOrderInstruction.FN_FISCALCODE, clientsData.getFiscalCode());
        itemData.put(IntrabankPaymentOrderInstruction.FN_RESIDENCY, residency);
    }

    /**
     * Gets the CBS payment type for the account.
     *
     * @param accountId account id
     * @return CBS payment type
     */
    private CbsPaymentType getCbsPaymentType(@Nonnull final String accountId) {
        if (ExternalSystem.T24.isPrefixed(accountId)) {
            return CbsPaymentType.IntLFA;
        } else if (ExternalSystem.WAY4.isPrefixed(accountId)) {
            return CbsPaymentType.IntLFC;
        }
        log.error("Unknown CBS payment type for account id {}", accountId);
        return CbsPaymentType.NONE;
    }
}
