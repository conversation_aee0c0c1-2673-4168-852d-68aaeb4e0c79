/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import java.util.Set;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.tpm.AddAccountWithCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddCardRequestInstruction;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import cz.finshape.vb.domain.internal.tpm.InstructionType;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.exception.TpmLogicException;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enriches selected card instructions with embossing information.
 *
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Add+Card+-+WIP">Add Card</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Add+Card+-+WIP">Add Account with Card</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Reissue+Card+-+WIP">Reissue Card</a>
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@Component
public class EmbossingEnricher extends VbInstructionItemEnricher {

    /**
     * Only add-card requests require enriching the embossing fields.
     */
    private static final Set<InstructionType> CARD_INSTRUCTION_TYPES = Set.of(
        InstructionType.ADD_ACCOUNT_WITH_CARD_REQUEST,
        InstructionType.ADD_CARD_REQUEST
    );

    private final TpmInstructionService tpmInstructionService;

    /**
     * Primary constructor.
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param tpmInstructionService TPM instruction service
     */
    public EmbossingEnricher(final GdsIntegrationApiConnector gdsIntegrationApiConnector,
                             final TpmInstructionService tpmInstructionService) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        final var optionalInstructionType = InstructionType.fromInstructionTypeCode(type);

        if (optionalInstructionType.isEmpty()) {
            log.error("Unknown instruction type code : {}", type);
            return itemData;
        }

        // Enrich only for qualified instruction types.
        final var instructionTypeCode = optionalInstructionType.get();
        if (!CARD_INSTRUCTION_TYPES.contains(instructionTypeCode)) {
            return itemData;
        }

        if (isUnknownOwnership(context)) {
            return itemData;
        }

        final var externalClientId = context.getInstruction().getOwnership().getClientId();
        final var client = tpmInstructionService.getClientData(externalClientId);

        // Enrich only for retail client.
        if (!GdsCatalogValues.LegalCategoryTypeValue.isRetail(client.getLegalCategory())) {
            log.debug("Skipped enriching of non-retail instruction(id={}, type={}, item.id={})",
                context.getInstructionId(), type, context.getInstructionItemId().orElse(null));
            return itemData;
        }

        final var firstName = client.getFirstName();
        final var lastName = client.getSurname();

        // Enrich the embossingFirstName field name.
        final var fnEmbossingFirstName = switch (instructionTypeCode) {
            case ADD_ACCOUNT_WITH_CARD_REQUEST -> AddAccountWithCardRequestInstruction.FN_EMBOSSINGFIRSTNAME;
            case ADD_CARD_REQUEST -> AddCardRequestInstruction.FN_EMBOSSINGFIRSTNAME;
            default -> throw new TpmLogicException(VbErrorCodeEnum.TPM_UNSUPPORTED_INSTRUCTION_TYPE,
                "Incorrect enricher implementation, missing branch for : " + instructionTypeCode);
        };
        final var currentFirstNameNode = itemData.get(fnEmbossingFirstName);
        if (currentFirstNameNode != null && !currentFirstNameNode.isNull()) {
            log.warn("Overriding the existing value of embossingFirstName={}", currentFirstNameNode.toPrettyString());
        }
        itemData.put(fnEmbossingFirstName, firstName);

        // Enrich the embossingLastName field name.
        final var fnEmbossingLastName = switch (instructionTypeCode) {
            case ADD_ACCOUNT_WITH_CARD_REQUEST -> AddAccountWithCardRequestInstruction.FN_EMBOSSINGLASTNAME;
            case ADD_CARD_REQUEST -> AddCardRequestInstruction.FN_EMBOSSINGLASTNAME;
            default -> throw new TpmLogicException(VbErrorCodeEnum.TPM_UNSUPPORTED_INSTRUCTION_TYPE,
                "Incorrect enricher implementation, missing branch for : " + instructionTypeCode);
        };
        final var currentLastNameNode = itemData.get(fnEmbossingLastName);
        if (currentLastNameNode != null && !currentLastNameNode.isNull()) {
            log.warn("Overriding the existing value of embossingLastName={}", currentLastNameNode.toPrettyString());
        }
        itemData.put(fnEmbossingLastName, lastName);

        log.info("Enriched instruction(id={}, type={}, item.id={}) with {}={}, {}={}",
            context.getInstructionId(), type, context.getInstructionItemId().orElse(null),
            fnEmbossingFirstName, firstName,
            fnEmbossingLastName, lastName);

        return itemData;
    }
}
