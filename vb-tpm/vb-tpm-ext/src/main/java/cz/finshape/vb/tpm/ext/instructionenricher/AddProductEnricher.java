/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import java.util.Set;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.Currency;
import cz.finshape.vb.domain.dbos.tpm.AddAccountWithCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddPhysicalCardToDigitalRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.ReissueCardRequestInstruction;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import cz.finshape.vb.domain.internal.tpm.InstructionType;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.exception.TpmLogicException;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enriches selected add-product instructions.
 *
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+analysis+-+Current+account+request">Add Current Account</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Add+Card+-+WIP#:~:text=Instruction%20mapping-,AddCardRequest,-To%20see%20the">Add Card</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Add+Card+-+WIP#:~:text=Instruction%20mapping-,AddCardRequest,-To%20see%20the">Add Account with Card</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Add+Card#TechnicalAnalysisAddCard-AddPhysicalCardToDigitalRequest">Add Physical to Digital</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Reissue+Card+-+WIP">Reissue Card</a>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@Component
public class AddProductEnricher extends VbInstructionItemEnricher {

    /**
     * Only add-product requests require enriching some fields.
     */
    private static final Set<InstructionType> ADD_PRODUCT_INSTRUCTION_TYPES = Set.of(
        InstructionType.ADD_CURRENT_ACCOUNT_REQUEST,
        InstructionType.ADD_ACCOUNT_WITH_CARD_REQUEST,
        InstructionType.ADD_CARD_REQUEST,
        InstructionType.ADD_PHYSICAL_CARD_TO_DIGITAL_REQUEST,
        InstructionType.REISSUE_CARD_REQUEST
    );

    /**
     * Add card instructions requests requiring currency enrichment.
     */
    private static final Set<InstructionType> CARD_CURRENCY_INSTRUCTION_TYPES = Set.of(
        InstructionType.ADD_PHYSICAL_CARD_TO_DIGITAL_REQUEST,
        InstructionType.REISSUE_CARD_REQUEST
    );

    /**
     * Add card instructions requests requiring IDNP (fiscalCode) enrichment.
     */
    private static final Set<InstructionType> IDNP_INSTRUCTION_TYPES = Set.of(
        InstructionType.ADD_CARD_REQUEST,
        InstructionType.ADD_ACCOUNT_WITH_CARD_REQUEST
    );

    private final TpmInstructionService tpmInstructionService;

    /**
     * Primary constructor.
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param tpmInstructionService TPM instruction service
     */
    public AddProductEnricher(final GdsIntegrationApiConnector gdsIntegrationApiConnector, final TpmInstructionService tpmInstructionService) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        final var optionalInstructionType = InstructionType.fromInstructionTypeCode(type);

        if (optionalInstructionType.isEmpty()) {
            log.error("Unknown instruction type code : {}", type);
            return itemData;
        }

        // Enrich only for qualified instruction types.
        final var instructionTypeCode = optionalInstructionType.get();
        if (!ADD_PRODUCT_INSTRUCTION_TYPES.contains(instructionTypeCode)) {
            return itemData;
        }

        // enrich card currency
        if (CARD_CURRENCY_INSTRUCTION_TYPES.contains(instructionTypeCode)) {
            enrichCardCurrency(context, itemData, instructionTypeCode);
        }

        // enrich IDNP (fiscalCode) only for retail clients
        if (IDNP_INSTRUCTION_TYPES.contains(instructionTypeCode)) {
            enrichHolderIdnp(context, itemData, instructionTypeCode);
        }

        return itemData;
    }

    private void enrichHolderIdnp(@Nonnull final InstructionItemEnricherContext context,
                                  @Nonnull final ObjectNode itemData,
                                  @Nonnull final InstructionType instructionTypeCode) {

        if (isUnknownOwnership(context)) {
            return;
        }
        final var externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client clientData = tpmInstructionService.getClientData(externalClientId);
        if (GdsCatalogValues.LegalCategoryTypeValue.isRetail(clientData.getLegalCategory())) {
            final var fnHolderIdnp = switch (instructionTypeCode) {
                case ADD_CARD_REQUEST -> AddCardRequestInstruction.FN_HOLDERIDNP;
                case ADD_ACCOUNT_WITH_CARD_REQUEST -> AddAccountWithCardRequestInstruction.FN_HOLDERIDNP;
                default -> throw new TpmLogicException(VbErrorCodeEnum.TPM_UNSUPPORTED_INSTRUCTION_TYPE,
                    "Incorrect enricher implementation, missing holderIdnp for : " + instructionTypeCode);
            };
            itemData.put(fnHolderIdnp, clientData.getFiscalCode());
        } else {
            log.debug("Client is not retail, skipping embossing information enrichment for instruction type : {}", instructionTypeCode);
        }
    }

    private void enrichCardCurrency(@Nonnull final InstructionItemEnricherContext context,
                                    @Nonnull final ObjectNode itemData,
                                    @Nonnull final InstructionType instructionTypeCode) {

        final var cardIdOpt = getRelatedEntityId(context, Card.EN_SINGULAR);
        if (containsUnknownEntityRelation(context)) {
            return;
        }
        if (cardIdOpt.isEmpty()) {
            log.warn("No card relation entity for {}", instructionTypeCode);
            return;
        }

        final var cardId = cardIdOpt.get();
        final Card card = gdsIntegrationApiConnector.getEntityById(
            Card.EN_PLURAL, cardId, Card.FN_ACCOUNT + "." + Account.FN_CURRENCY + "." + Currency.FN_ID);

        final var fnCurrency = switch (instructionTypeCode) {
            case ADD_PHYSICAL_CARD_TO_DIGITAL_REQUEST -> AddPhysicalCardToDigitalRequestInstruction.FN_CURRENCY;
            case REISSUE_CARD_REQUEST -> ReissueCardRequestInstruction.FN_CURRENCY;
            default -> throw new TpmLogicException(VbErrorCodeEnum.TPM_UNSUPPORTED_INSTRUCTION_TYPE,
                "Incorrect enricher implementation, missing currency for : " + instructionTypeCode);
        };

        itemData.put(fnCurrency, card.getAccount().getCurrency().getId());
    }
}
