/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;

import java.math.BigDecimal;
import java.time.LocalDate;

import jakarta.annotation.Nonnull;

import cz.finshape.vb.tpm.ext.generated.Vbipspayments;


/**
 * IPS payments repository.
 *
 * <AUTHOR>
 */
public interface IpsPaymentsRepository {

    /**
     * Registers (inserts or updates) IPS payment.
     *
     * @param paymentLimit Domestic payment
     */
    void registerOrUpdatePayment(@Nonnull Vbipspayments paymentLimit);

    /**
     * Get sum of used daily amount for the given client.
     *
     * @param externalClientId client ID
     * @param dateOfTransaction date
     * @return the IPS payment
     */
    @Nonnull
    BigDecimal sumDailyAmount(@Nonnull String externalClientId, @Nonnull LocalDate dateOfTransaction);

    /**
     * Counts the IPS daily transactions.
     *
     * @param externalClientId the client's external ID
     * @param dateOfTransaction date
     * @return the IPS daily transactions
     */
    long countDailyTransactions(@Nonnull String externalClientId, @Nonnull LocalDate dateOfTransaction);

    /**
     * Get sum of used monthly amount for the given client.
     *
     * @param externalClientId client ID
     * @param dateOfTransaction date
     * @return the IPS payment
     */
    @Nonnull
    BigDecimal sumMonthlyAmount(@Nonnull String externalClientId, @Nonnull LocalDate dateOfTransaction);

    /**
     * Deactivates IPS payments limit.
     * <p>
     * The payments are no longer used for the cumulative sum for the given day as become obsolete the following day.
     * For the sake of traceability, the history is retained, so the deactivation happens instead of removal.
     *
     * @param instructionId instruction ID
     * @param clientExternalId client external ID
     * @return number of deactivated payments
     */
    long deactivatePayment(@Nonnull Long instructionId, @Nonnull String clientExternalId);

    /**
     * Removes payment records.
     *
     * @param signedBefore payments signed before the date (exclusive) to be removed
     * @return number of removed payments
     */
    long removePayments(@Nonnull LocalDate signedBefore);
}
