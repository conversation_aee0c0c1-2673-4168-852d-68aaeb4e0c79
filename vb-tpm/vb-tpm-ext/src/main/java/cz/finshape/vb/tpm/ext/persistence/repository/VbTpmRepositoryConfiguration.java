/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;


import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Import;


/**
 * VB TPM Repository auto-configuration.
 *
 * <AUTHOR>
 */
@AutoConfiguration
@Import({
    HappyHourPaymentsRepositoryImpl.class,
    DomesticPaymentsRepositoryImpl.class,
    SwiftPaymentsRepositoryImpl.class,
    ForexPaymentsRepositoryImpl.class,
    VbInstructionItemRepositoryImpl.class,
    IpsPaymentsRepositoryImpl.class
})
public class VbTpmRepositoryConfiguration {
}
