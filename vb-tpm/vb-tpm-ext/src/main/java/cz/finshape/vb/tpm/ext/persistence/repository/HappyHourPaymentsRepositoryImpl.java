/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;


import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.List;

import javax.sql.DataSource;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Repository;

import com.querydsl.core.types.dsl.Expressions;
import cz.finshape.vb.tpm.ext.generated.QVbhappyhourpayments;
import cz.finshape.vb.tpm.ext.generated.Vbhappyhourpayments;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.commons.spring.business.framework.resources.dao.impl.AbstractDSLDao;


/**
 * Happy hour payments repository.
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class HappyHourPaymentsRepositoryImpl extends AbstractDSLDao implements HappyHourPaymentsRepository {

    private static final QVbhappyhourpayments Q_VBHAPPYHOURPAYMENTS = QVbhappyhourpayments.vbhappyhourpayments;

    /**
     * Primary constructor.
     *
     * @param dataSource data source
     */
    public HappyHourPaymentsRepositoryImpl(final DataSource dataSource) {
        super(dataSource);
    }

    @Override
    public void registerOrUpdatePayment(@Nonnull final Vbhappyhourpayments happyHourPayments) {
        final var instructionId = happyHourPayments.getInstructionid();
        final var externalClientId = happyHourPayments.getExternalclientid();

        final var whereExpression = Q_VBHAPPYHOURPAYMENTS.instructionid.eq(instructionId)
            .and(Q_VBHAPPYHOURPAYMENTS.externalclientid.eq(externalClientId));

        final var notExisting = withQuery(query -> query.select()
            .select(Expressions.constant(1)))
            .from(Q_VBHAPPYHOURPAYMENTS)
            .where(whereExpression)
            .fetchResults()
            .isEmpty();

        // Make sure the record is active.
        happyHourPayments.setActive(true);

        if (notExisting) {
            withInsertClause(Q_VBHAPPYHOURPAYMENTS, clause -> clause
                .populate(happyHourPayments)
                .execute());
            log.debug("Created a record for instructionId={} and externalClientId={}",
                happyHourPayments.getInstructionid(), happyHourPayments.getExternalclientid());
        } else {
            final var count = withUpdateClause(Q_VBHAPPYHOURPAYMENTS, clause -> clause
                .populate(happyHourPayments)
                .where(whereExpression)
                .execute());
            log.debug("Updated {} record(s) where instructionId={} and externalClientId={}",
                count, instructionId, externalClientId);
        }
    }

    @Nonnull
    @Override
    public List<Vbhappyhourpayments> findPayments(@Nonnull final String externalClientId,
                                                  @Nonnull final Instant from,
                                                  @Nonnull final Instant to) {

        final var fromUtc = from.atOffset(ZoneOffset.UTC).toLocalDateTime();
        final var toUtc = to.atOffset(ZoneOffset.UTC).toLocalDateTime();

        final var payments = withQuery(query -> query
            .select(Q_VBHAPPYHOURPAYMENTS))
            .from(Q_VBHAPPYHOURPAYMENTS)
            .where(Q_VBHAPPYHOURPAYMENTS.externalclientid.eq(externalClientId)
                .and(Q_VBHAPPYHOURPAYMENTS.active.isTrue())
                .and(Q_VBHAPPYHOURPAYMENTS.signedat.after(fromUtc))
                .and(Q_VBHAPPYHOURPAYMENTS.signedat.before(toUtc)))
            .fetch();
        log.debug("Fetched '{}' record(s) where externalClientId is '{}' and signed between '{}' and '{}'",
            payments.size(), externalClientId, from, to);
        return payments;
    }

    @Override
    public long deactivatePayment(@Nonnull final Long instructionId, @Nonnull final String clientExternalId) {
        final var count = withUpdateClause(Q_VBHAPPYHOURPAYMENTS, clause -> clause
            .where(Q_VBHAPPYHOURPAYMENTS.instructionid.eq(instructionId)
                .and(Q_VBHAPPYHOURPAYMENTS.externalclientid.eq(clientExternalId))
                .and(Q_VBHAPPYHOURPAYMENTS.active.isTrue()))
            .set(Q_VBHAPPYHOURPAYMENTS.active, false) // De/activation is driven with `active` column.
            .execute());
        log.debug("Deactivated {} record(s) where instructionId={} and clientExternalId={}",
            count, instructionId, clientExternalId);
        return count;
    }

    @Override
    public long removePayments(@Nonnull final LocalDate signedBefore) {
        final var count = withDeleteClause(Q_VBHAPPYHOURPAYMENTS, clause -> clause
            .where(Q_VBHAPPYHOURPAYMENTS.signedat.before(signedBefore.atStartOfDay()))
            .execute());
        log.debug("Removed {} record(s) signed before {}", count, signedBefore);
        return count;
    }
}
