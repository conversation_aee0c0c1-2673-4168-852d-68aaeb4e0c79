/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;

import static org.apache.commons.lang3.ObjectUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static cz.finshape.vb.core.utils.NullUtils.valueOrEmpty;
import static cz.finshape.vb.core.utils.NullUtils.valueOrNull;
import static cz.finshape.vb.core.utils.XmlUtils.parseXmlStringToElement;
import static cz.finshape.vb.domain.internal.VbConstants.DateFormats.DATE_ISO_FORMATTER;
import static cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum.TPM_INSTRUCTION_ITEM_NOT_FOUND;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.IPS_RTP_PAYMENT_ORDER;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getLocalDateValue;

import java.time.LocalDate;
import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.ips.access.IpsAccessIntegrationApiConnector;
import cz.finshape.vb.core.utils.TemporalUtils;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.dbos.tpm.IpsRtpPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.ips.rest.RtpRequestInternalDto;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.ips.core.model.pain013.Document;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.exception.TpmSystemException;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for IpsRtpPaymentOrder instructions.
 *
 * <ul>
 *     <li>adds bank's information</li>
 *     <li>adds payment information</li>
 *     <li>adds account bisAccountId dependant data</li>
 *     <li>adds RTP data</li>
 * </ul>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Kundrat
 */
@Slf4j
@Component
public class IpsRtpPaymentOrderEnricher extends AbstractIpsEnricher {

    public final static String PAYMENT_ORDER_BIS_TAPE = "1";

    private final TpmInstructionService tpmInstructionService;

    private final IpsAccessIntegrationApiConnector ipsAccessIntegrationApiConnector;

    /**
     * Instantiates a new IPS RTP payment order enricher.
     *
     * @param tpmInstructionService TPM enricher service
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param ipsAccessIntegrationApiConnector ips access integration api connector
     */
    public IpsRtpPaymentOrderEnricher(
        final TpmInstructionService tpmInstructionService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final IpsAccessIntegrationApiConnector ipsAccessIntegrationApiConnector) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
        this.ipsAccessIntegrationApiConnector = ipsAccessIntegrationApiConnector;
    }

    @Override
    public ObjectNode enrich(@Nonnull final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!IPS_RTP_PAYMENT_ORDER.equalsInstructionTypeCode(type)) {
            return itemData;
        }
        log.debug("Enriching instruction of type {}", IPS_RTP_PAYMENT_ORDER);

        enrichByTransactionStaticData(itemData);
        enrichByClientData(context, itemData);
        enrichByRtpData(itemData);
        enrichByPaymentType(itemData, context);

        return itemData;
    }

    /**
     * Enriches the instruction by transaction static data.
     *
     * @param itemData the item data
     */
    private void enrichByTransactionStaticData(@Nonnull final ObjectNode itemData) {
        itemData.put(IpsRtpPaymentOrderInstruction.FN_BANKNAME, VbConstants.VICTORIA_BANK_NAME.replace(".", EMPTY));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE, PAYMENT_ORDER_BIS_TAPE);

        final Optional<LocalDate> valueDate = getLocalDateValue(itemData, IpsRtpPaymentOrderInstruction.FN_VALUEDATE);

        if (valueDate.isEmpty()) {
            itemData.put(IpsRtpPaymentOrderInstruction.FN_VALUEDATE, TemporalUtils.nowInMoldova().format(DATE_ISO_FORMATTER));
        }
    }

    /**
     * Enriches the instruction by client data.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByClientData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (isUnknownOwnership(context)) {
            return;
        }
        if (context.getInstruction().getOwnership() == null || !context.getInstruction().getOwnership().isOwnedByClient()) {
            log.error("Client id is missing in the instruction");
            return;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client clientsData = tpmInstructionService.getClientData(externalClientId);
        final var residency = Optional.ofNullable(clientsData.getResidency()).map(ResidencyType::getId).orElse(null);

        itemData.put(IpsRtpPaymentOrderInstruction.FN_RESIDENCY, residency);
    }

    /**
     * Enriches the instruction by RTP data.
     *
     * @param itemData the item data
     */
    private void enrichByRtpData(@Nonnull final ObjectNode itemData) {
        final String externalId = itemData.get(IpsRtpPaymentOrderInstruction.FN_EXTERNALID).textValue();
        final var rtpDetailResponse = ipsAccessIntegrationApiConnector.getRtpDetail(externalId);
        final var mxMsgBody = Optional.ofNullable(rtpDetailResponse.getBody())
            .map(RtpRequestInternalDto::getMxMsgBody)
            .filter(string -> !string.isBlank())
            .orElseThrow(() -> {
                log.error("RTP request detail not found for external ID {}", externalId);
                return new TpmSystemException(TPM_INSTRUCTION_ITEM_NOT_FOUND, ("RTP request detail not found for externalId: " + externalId));
            });

        final var rtpParsedDetail = parseXmlStringToDocument(mxMsgBody);

        // always should be present at least 1 payment
        final var rtpMsgData = valueOrEmpty(() -> rtpParsedDetail.getCdtrPmtActvtnReq().getPmtInf().get(0))
            .orElseThrow(() -> {
                log.error("RTP MX message data not found");
                return new TpmSystemException(TPM_INSTRUCTION_ITEM_NOT_FOUND, "RTP MX message data not found");
            });

        itemData.put(IpsRtpPaymentOrderInstruction.FN_CLIENTNAME, valueOrNull(() -> rtpMsgData.getDbtr().getNm()));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_CLIENTCOUNTRY, valueOrNull(() -> rtpMsgData.getDbtr().getCtryOfRes()));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_BIC, valueOrNull(() -> rtpMsgData.getDbtrAgt().getFinInstnId().getBICFI()));

        if (isEmpty(rtpMsgData.getCdtTrfTx())) {
            log.error("RTP MX message amount not found");
            throw new TpmSystemException(TPM_INSTRUCTION_ITEM_NOT_FOUND, "RTP MX message amount not found");
        }
        final var cdtTrfTx = rtpMsgData.getCdtTrfTx().get(0);
        itemData.put(IpsRtpPaymentOrderInstruction.FN_CURRENCY, valueOrNull(() -> cdtTrfTx.getAmt().getInstdAmt().getCcy()));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_AMOUNT, valueOrNull(() -> cdtTrfTx.getAmt().getInstdAmt().getValue()));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_FISCALCODE, valueOrNull(() -> cdtTrfTx.getTax().getDbtr().getTaxId()));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_PARTNERBIC, valueOrNull(() -> cdtTrfTx.getCdtrAgt().getFinInstnId().getBICFI()));
        itemData.put(
            IpsRtpPaymentOrderInstruction.FN_PARTNERMEMBERID,
            valueOrNull(() -> cdtTrfTx.getCdtrAgt().getFinInstnId().getClrSysMmbId().getMmbId()));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_PARTNERNAME, valueOrNull(() -> cdtTrfTx.getCdtr().getNm()));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_PARTNERACCOUNTNUMBER, valueOrNull(() -> cdtTrfTx.getCdtrAcct().getId().getIBAN()));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_PARTNERCOUNTRY, valueOrNull(() -> cdtTrfTx.getCdtr().getCtryOfRes()));
        itemData.put(IpsRtpPaymentOrderInstruction.FN_PARTNERFISCALCODE, valueOrNull(() -> cdtTrfTx.getTax().getCdtr().getTaxId()));
        final var remInfo = valueOrEmpty(() -> cdtTrfTx.getRmtInf().getUstrd())
            .map(infos -> StringUtils.join(infos, ", "))
            .orElse(null);
        itemData.put(IpsRtpPaymentOrderInstruction.FN_REMITTANCEINFORMATION, remInfo);
        itemData.put(IpsRtpPaymentOrderInstruction.FN_TRANSACTIONCODE, valueOrNull(() -> cdtTrfTx.getPurp().getPrtry()));
    }

    @Override
    protected String getPaymentTypePrefix() {
        return CbsPaymentType.IPS_R2P_PREFIX;
    }

    /**
     * Parses the XML string to {@link Document}.
     *
     * @param xmlString the XML string
     * @return the parsed document
     */
    @Nonnull
    private Document parseXmlStringToDocument(@Nonnull final String xmlString) {
        if (StringUtils.isBlank(xmlString)) {
            log.error("XML input is null or empty");
            throw new TpmSystemException(TPM_INSTRUCTION_ITEM_NOT_FOUND, "Empty XML input");
        }

        if (xmlString.length() < "<Document></Document>".length()) {
            log.error("Invalid XML string: {}", xmlString);
            throw new TpmSystemException(TPM_INSTRUCTION_ITEM_NOT_FOUND, "Invalid XML string");
        }

        String documentAsString = xmlString.replace("<ns2:Document", "<Document");
        documentAsString = documentAsString.replace("</ns2:Document>", "</Document>");
        documentAsString = documentAsString.substring(documentAsString.indexOf("<Document"),
                                                      documentAsString.indexOf("</Document>") + "</Document>".length());
        return parseXmlStringToElement(documentAsString, Document.class);
    }
}
