/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

import jakarta.annotation.Nonnull;

import cz.finshape.vb.tpm.ext.generated.Vbswiftpayments;

/**
 * Swifts payments repository.
 *
 * <AUTHOR>
 */
public interface SwiftPaymentsRepository {

    /**
     * Calculate sum of used amount for client after signet at.
     *
     * @param externalClientId external client ID
     * @param signetAt signet at
     * @return sum of used amount
     */
    @Nonnull
    BigDecimal sumMdlAmount(@Nonnull String externalClientId, @Nonnull LocalDate signetAt);

    /**
     * Calculate sums of used amount in MDL and EUR for client after signet at.
     *
     * @param externalClientId external client ID
     * @param signetAt signet at
     * @return sums of used amount
     */
    @Nonnull
    Map<String, BigDecimal> sumMdlAndEurAmounts(@Nonnull String externalClientId, @Nonnull LocalDate signetAt);

    /**
     * Registers (inserts or updates) a Swift payment.
     *
     * @param paymentLimit Swift payment
     */
    void registerOrUpdatePayment(@Nonnull Vbswiftpayments paymentLimit);

    /**
     * Deactivates a Swift payment.
     * <p>
     * The payments are no longer used for the cumulative sum for the given day as become obsolete the following day.
     * For the sake of traceability, the history is retained, so the deactivation happens instead of removal.
     *
     * @param instructionId instruction ID
     * @param clientExternalId client external ID
     * @return number of deactivated payments
     */
    long deactivatePayment(@Nonnull Long instructionId, @Nonnull String clientExternalId);

    /**
     * Removes payment records.
     *
     * @param signedBefore payments signed before the date (exclusive) to be removed
     * @return number of removed payments
     */
    long removePayments(@Nonnull LocalDate signedBefore);
}
