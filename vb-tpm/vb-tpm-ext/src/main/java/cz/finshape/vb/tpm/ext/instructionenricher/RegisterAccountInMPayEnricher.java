/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import static cz.finshape.vb.domain.internal.tpm.InstructionType.REGISTER_ACCOUNT_IN_MPAY_REQUEST;

import java.util.Optional;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.tpm.RegisterAccountInMPayRequestInstruction;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for {@link RegisterAccountInMPayRequestInstruction}
 * Based on <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+analyses+-+Assign+card+account+to+MPay#Technicalanalyses
 * AssigncardaccounttoMPay-BE-enricher">Technical analysis confluence page</a>
 * <AUTHOR> Cernil
 */
@Slf4j
@Component
public class RegisterAccountInMPayEnricher extends VbInstructionItemEnricher {

    /**
     * Instantiates a new Register account in MPay enricher.
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     */
    public RegisterAccountInMPayEnricher(GdsIntegrationApiConnector gdsIntegrationApiConnector) {
        super(gdsIntegrationApiConnector);
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!REGISTER_ACCOUNT_IN_MPAY_REQUEST.equalsInstructionTypeCode(type)) {
            return itemData;
        }

        if (containsUnknownEntityRelation(context)) {
            return itemData;
        }
        final Optional<String> cardIdOpt = getRelatedEntityId(context, Card.EN_SINGULAR);
        if (cardIdOpt.isEmpty()) {
            log.warn("Card Id missing in relation");
            return itemData;
        }

        log.debug("Enriching RegisterAccountInMPay instruction for account id={}", cardIdOpt.get());

        final Card card = gdsIntegrationApiConnector.getEntityById(
            Card.EN_PLURAL,
            cardIdOpt.get(),
            Card.FN_MASKEDNUMBER,
            Card.FN_ACCOUNT + "." + Account.FN_IBAN);

        itemData.put(RegisterAccountInMPayRequestInstruction.FN_CARDMASKEDNUMBER, card.getMaskedNumber());
        itemData.put(RegisterAccountInMPayRequestInstruction.FN_ACCOUNTNUMBER, card.getAccount().getIban());
        return itemData;
    }
}
