/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import javax.sql.DataSource;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.querydsl.core.types.dsl.Expressions;
import cz.finshape.vb.core.utils.TemporalUtils;
import cz.finshape.vb.tpm.ext.generated.QVbexchangeratelimits;
import cz.finshape.vb.tpm.ext.generated.QVbforexpayments;
import cz.finshape.vb.tpm.ext.generated.Vbexchangeratelimits;
import cz.finshape.vb.tpm.ext.generated.Vbforexpayments;
import cz.finshape.vb.tpm.ext.persistence.AbstractQueryDslDao;
import lombok.extern.slf4j.Slf4j;


/**
 * Forex payments repository.
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class ForexPaymentsRepositoryImpl extends AbstractQueryDslDao implements ForexPaymentsRepository {

    private static final QVbforexpayments Q_VBFOREXPAYMENTS = QVbforexpayments.vbforexpayments;
    private static final QVbexchangeratelimits Q_VBEXCHANGERATELIMITS = QVbexchangeratelimits.vbexchangeratelimits;

    private static final String SEQ_VBEXCHANGERATELIMITS = "SEQ_VBEXCHANGERATELIMITS";

    /**
     * Primary constructor.
     *
     * @param dataSource data source
     */
    public ForexPaymentsRepositoryImpl(final DataSource dataSource) {
        super(dataSource);
    }

    @Override
    @Transactional
    public void registerOrUpdatePayment(@Nonnull final Vbforexpayments forexPayments) {
        final var instructionId = forexPayments.getInstructionid();
        final var externalClientId = forexPayments.getExternalclientid();

        final var whereExpression = Q_VBFOREXPAYMENTS.instructionid.eq(instructionId)
            .and(Q_VBFOREXPAYMENTS.externalclientid.eq(externalClientId));

        final var notExisting = createQuery()
            .select(Expressions.constant(1))
            .from(Q_VBFOREXPAYMENTS)
            .where(whereExpression)
            .fetchResults()
            .isEmpty();

        // Make sure the record is active.
        forexPayments.setActive(true);

        if (notExisting) {
            createInsertClause(Q_VBFOREXPAYMENTS)
                .populate(forexPayments)
                .execute();
            log.debug("Created a record for instructionId={} and externalClientId={}",
                forexPayments.getInstructionid(), forexPayments.getExternalclientid());
        } else {
            final var count = createUpdateClause(Q_VBFOREXPAYMENTS)
                .populate(forexPayments)
                .where(whereExpression)
                .execute();
            log.debug("Updated {} record(s) where instructionId={} and externalClientId={}",
                count, instructionId, externalClientId);
        }
    }

    @Nonnull
    @Override
    public List<Vbforexpayments> findPayments(@Nonnull final String externalClientId,
                                              @Nonnull final String currencyId,
                                              @Nonnull final LocalDateTime from) {

        final var payments = withQuery(query -> query
            .select(Q_VBFOREXPAYMENTS))
            .from(Q_VBFOREXPAYMENTS)
            .where(Q_VBFOREXPAYMENTS.externalclientid.eq(externalClientId)
                .and(Q_VBFOREXPAYMENTS.debitedamountcurrency.eq(currencyId)
                    .or(Q_VBFOREXPAYMENTS.creditedamountcurrency.eq(currencyId)))
                .and(Q_VBFOREXPAYMENTS.active.isTrue())
                .and(Q_VBFOREXPAYMENTS.signedat.after(from)))
            .fetch();
        log.debug("Fetched {} record(s) where externalClientId={}, currencyId={} and signed after {}",
            payments.size(), externalClientId, currencyId, from);
        return payments;
    }

    @Override
    public long deactivatePayment(@Nonnull final Long instructionId, @Nonnull final String clientExternalId) {
        final var count = withUpdateClause(Q_VBFOREXPAYMENTS, clause -> clause
            .where(Q_VBFOREXPAYMENTS.instructionid.eq(instructionId)
                .and(Q_VBFOREXPAYMENTS.externalclientid.eq(clientExternalId))
                .and(Q_VBFOREXPAYMENTS.active.isTrue()))
            .set(Q_VBFOREXPAYMENTS.active, false) // De/activation is driven with `active` column.
            .execute());
        log.debug("Deactivated {} record(s) where instructionId={} and clientExternalId={}",
            count, instructionId, clientExternalId);
        return count;
    }

    @Nonnull
    @Override
    public List<Vbexchangeratelimits> findValidExchangeRateLimits(@Nonnull final String... currencies) {
        final var now = TemporalUtils.nowInMoldova().toLocalDateTime();

        final var exchangeRateLimits = withQuery(query -> query
            .select(Q_VBEXCHANGERATELIMITS)
            .from(Q_VBEXCHANGERATELIMITS)
            .where(Q_VBEXCHANGERATELIMITS.currency.in(currencies)
                .and(Q_VBEXCHANGERATELIMITS.validfrom.before(now)))
            .fetch());

        log.debug("Found '{}' record(s) for currencies '{}' before '{}'", exchangeRateLimits.size(), Arrays.toString(currencies), now);
        return exchangeRateLimits;
    }

    @Override
    @Transactional
    public void createExchangeRateLimits(@Nonnull final List<Vbexchangeratelimits> exchangeRateLimits)  {
        final var count = exchangeRateLimits.size();
        final var sequenceValues = getNextSequenceValues(SEQ_VBEXCHANGERATELIMITS, count);

        final var insert = createInsertClause(Q_VBEXCHANGERATELIMITS);
        for (int i=0; i<count; i++) {
            final var id = String.valueOf(sequenceValues.get(i));
            final var exchangeRateLimit = exchangeRateLimits.get(i);

            insert
                .populate(exchangeRateLimit)
                .set(Q_VBEXCHANGERATELIMITS.id, String.valueOf(id))
                .addBatch();

            log.debug("Storing exchange rate limit {} with generated ID {}", exchangeRateLimit.getId(), id);
        }

        final var keys = insert.executeWithKeys(Q_VBEXCHANGERATELIMITS.id);
        log.debug("Created {}/{} records : ids=[{}]", keys.size(), exchangeRateLimits.size(), keys);
    }

    @Override
    public long removePayments(@Nonnull final LocalDate signedBefore) {
        final var count = withDeleteClause(Q_VBFOREXPAYMENTS, clause -> clause
            .where(Q_VBFOREXPAYMENTS.signedat.before(signedBefore.atStartOfDay()))
            .execute());
        log.debug("Removed {} record(s) signed before {}", count, signedBefore);
        return count;
    }
}
