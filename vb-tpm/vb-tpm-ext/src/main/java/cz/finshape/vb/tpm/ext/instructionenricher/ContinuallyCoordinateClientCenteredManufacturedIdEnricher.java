/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import java.util.Optional;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.tpm.AddAccountWithCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddCurrentAccountRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddPhysicalCardToDigitalRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.CorpOwnAccountPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.DomesticPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.IntrabankPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.IpsM2MPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.IpsPhonePaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.IpsQrcPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.IpsRtpPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.MPayPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.P2PPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.ReissueCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.SwiftPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.tpm.InstructionType;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for the following instructions:
 * <ul>
 *     <li><b>Product instructions</b></li>
 *     <ul>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.AddCurrentAccountRequestInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.AddCardRequestInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.AddAccountWithCardRequestInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.ReissueCardRequestInstruction}</li>
 *     </ul>
 *     <li><b>Payment instructions</b></li>
 *     <ul>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.DomesticPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.CorpOwnAccountPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.IntrabankFCyPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.IntrabankPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.IpsM2MPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.IpsPhonePaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.IpsQrcPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.IpsRtpPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.MPayPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.P2PPaymentOrderInstruction}</li>
 *         <li>{@link cz.finshape.vb.domain.dbos.tpm.SwiftPaymentOrderInstruction}</li>
 *     </ul>
 * </ul>
 *
 * IP addresses are enriched via {@link IpAddressEnricher}.
 *
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+analysis+-+Current+account+request">Confluence</a>
 * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Add+Card+-+WIP#TechnicalAnalysisAddCardWIP-Instructionmapping">Confluence</a>
 * <AUTHOR> Cernil
 */
@Slf4j
@Component
public class ContinuallyCoordinateClientCenteredManufacturedIdEnricher extends VbInstructionItemEnricher {

    private final TpmInstructionService tpmInstructionService;

    /**
     * Instantiates a new Register account in MPay enricher.
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param tpmInstructionService TPM instruction service
     */
    public ContinuallyCoordinateClientCenteredManufacturedIdEnricher(
        final TpmInstructionService tpmInstructionService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        final var optionalInstructionType = InstructionType.fromInstructionTypeCode(type);

        if (optionalInstructionType.isEmpty()) {
            log.error("Unknown instruction type code : {}", type);
            return itemData;
        }

        // Enrich only for qualified instruction types.
        final var instructionTypeCode = optionalInstructionType.get();
        final var fnManufacturedId = switch (instructionTypeCode) {
            // Product instructions:
            case ADD_CURRENT_ACCOUNT_REQUEST -> AddCurrentAccountRequestInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case ADD_CARD_REQUEST -> AddCardRequestInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case ADD_ACCOUNT_WITH_CARD_REQUEST -> AddAccountWithCardRequestInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case ADD_PHYSICAL_CARD_TO_DIGITAL_REQUEST ->
                AddPhysicalCardToDigitalRequestInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case REISSUE_CARD_REQUEST -> ReissueCardRequestInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;

            // Payment instructions:
            case CORP_OWN_ACCOUNT_PAYMENT_ORDER -> CorpOwnAccountPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case DOMESTIC_PAYMENT_ORDER -> DomesticPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case INTRABANK_FCY_PAYMENT_ORDER -> IntrabankFCyPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case INTRABANK_PAYMENT_ORDER -> IntrabankPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case IPS_M2M_PAYMENT_ORDER -> IpsM2MPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case IPS_PHONE_PAYMENT_ORDER -> IpsPhonePaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case IPS_QRC_PAYMENT_ORDER -> IpsQrcPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case IPS_RTP_PAYMENT_ORDER -> IpsRtpPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case MPAY_PAYMENT_ORDER -> MPayPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case OWN_ACCOUNT_PAYMENT_ORDER -> OwnAccountPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case P2P_PAYMENT_ORDER -> P2PPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            case SWIFT_PAYMENT_ORDER -> SwiftPaymentOrderInstruction.FN_CONTINUALLYCOORDINATECLIENTCENTEREDMANUFACTUREDID;
            default -> null;
        };

        if (fnManufacturedId == null) {
            return itemData;
        }

        // Check instruction ownership.
        if (isUnknownOwnership(context)) {
            return itemData;
        }

        @SuppressWarnings("PMD.TooFewBranchesForSwitch") // We like switch.
        final var manufacturedIdOpt = switch (instructionTypeCode) {
            case P2P_PAYMENT_ORDER -> getManufacturedIdFromCardRelation(context);
            default -> getManufacturedIdFromClientOwnership(context);
        };
        manufacturedIdOpt.ifPresentOrElse(manufacturedId -> {
                itemData.put(fnManufacturedId, manufacturedId);
                log.debug("Enriched '{}' instruction field '{}': {}", instructionTypeCode, fnManufacturedId, manufacturedId);
            },
            () -> log.debug("No manufactured ID to enrich '{}' instruction field '{}'", instructionTypeCode, fnManufacturedId));

        return itemData;
    }

    private Optional<Integer> getManufacturedIdFromCardRelation(final InstructionItemEnricherContext context) {
        return getRelatedEntityId(context, Card.EN_SINGULAR)
            .map(cardId -> {
                log.debug("Requesting owner for card.id={}", cardId);
                return gdsIntegrationApiConnector.<Card>getEntityById(Card.EN_PLURAL, cardId, Card.FN_OWNER + "." + Client.FN_ID);
            })
            .map(Card::getOwner)
            .map(Client::getId)
            .flatMap(this::getManufacturedIdFromClientData);
    }

    private Optional<Integer> getManufacturedIdFromClientOwnership(final InstructionItemEnricherContext context) {
        if (context.getInstruction().getOwnership() == null || !context.getInstruction().getOwnership().isOwnedByClient()) {
            log.error("Client ID is missing in the instruction");
            return Optional.empty();
        }

        final var externalClientId = context.getInstruction().getOwnership().getClientId();
        return getManufacturedIdFromClientData(externalClientId);
    }

    private Optional<Integer> getManufacturedIdFromClientData(final String externalClientId) {
        final var clientsData = tpmInstructionService.getClientData(externalClientId);
        return Optional.ofNullable(clientsData.getLegalCategory().getContinuallyCoordinateClientCenteredManufacturedId());
    }
}
