/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import jakarta.annotation.Nonnull;

import cz.finshape.vb.tpm.ext.generated.Vbexchangeratelimits;
import cz.finshape.vb.tpm.ext.generated.Vbforexpayments;


/**
 * Forex payments repository.
 *
 * <AUTHOR>
 */
public interface ForexPaymentsRepository {

    /**
     * Registers (inserts or updates) a Forex payment.
     *
     * @param forexPayments forex payment
     */
    void registerOrUpdatePayment(@Nonnull Vbforexpayments forexPayments);

    /**
     * Finds Forex debit payments.
     *
     * @param externalClientId external client ID
     * @param currencyId debited OR credited amount currency ID
     * @param from from
     * @return list of Forex payments
     */
    @Nonnull
    List<Vbforexpayments> findPayments(@Nonnull String externalClientId,
                                       @Nonnull String currencyId,
                                       @Nonnull LocalDateTime from);

    /**
     * Deactivates a Forex payment.
     * <p>
     * The payments are no longer used for the cumulative sum for the given day as become obsolete the following day.
     * For the sake of traceability, the history is retained, so the deactivation happens instead of removal.
     *
     * @param instructionId instruction ID
     * @param clientExternalId client external ID
     * @return number of deactivated payments
     */
    long deactivatePayment(@Nonnull Long instructionId, @Nonnull String clientExternalId);

    /**
     * Finds valid exchange rate limits.
     *
     * @param currencies currencies
     * @return valid exchange rate limits
     */
    @Nonnull
    List<Vbexchangeratelimits> findValidExchangeRateLimits(@Nonnull String... currencies);

    /**
     * Creates a new exchange rate limits.
     *
     * @param exchangeRateLimits exchange rate limits
     */
    void createExchangeRateLimits(@Nonnull List<Vbexchangeratelimits> exchangeRateLimits);

    /**
     * Removes payment records.
     *
     * @param signedBefore payments signed before the date (exclusive) to be removed
     * @return number of removed payments
     */
    long removePayments(@Nonnull LocalDate signedBefore);
}
