/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;


import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

import jakarta.annotation.Nonnull;

import cz.finshape.vb.tpm.ext.generated.Vbhappyhourpayments;


/**
 * Happy Hour payments repository.
 *
 * <AUTHOR>
 */
public interface HappyHourPaymentsRepository {

    /**
     * Registers (inserts or updates) a Happy Hour payment.
     *
     * @param happyHourPayments Happy Hour payment
     */
    void registerOrUpdatePayment(@Nonnull Vbhappyhourpayments happyHourPayments);

    /**
     * Finds Happy Hour payments.
     *
     * @param externalClientId external client ID
     * @param from from
     * @param to to
     * @return list of Happy Hour payments
     */
    @Nonnull
    List<Vbhappyhourpayments> findPayments(@Nonnull String externalClientId,
                                           @Nonnull Instant from,
                                           @Nonnull Instant to);

    /**
     * Deactivates a Happy Hour payment.
     * <p>
     * The payments are no longer used for the cumulative sum for the given day as become obsolete the following day.
     * For the sake of traceability, the history is retained, so the deactivation happens instead of removal.
     *
     * @param instructionId instruction ID
     * @param clientExternalId client external ID
     * @return number of deactivated payments
     */
    long deactivatePayment(@Nonnull Long instructionId, @Nonnull String clientExternalId);

    /**
     * Removes payment records.
     *
     * @param signedBefore payments signed before the date (exclusive) to be removed
     * @return number of removed payments
     */
    long removePayments(@Nonnull LocalDate signedBefore);
}
