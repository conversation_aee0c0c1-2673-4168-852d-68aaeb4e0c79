/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.persistence.repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;

import javax.sql.DataSource;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Repository;

import com.querydsl.core.types.dsl.Expressions;
import cz.finshape.vb.domain.internal.xcr.Currency;
import cz.finshape.vb.tpm.ext.generated.QVbswiftpayments;
import cz.finshape.vb.tpm.ext.generated.Vbswiftpayments;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.commons.spring.business.framework.resources.dao.impl.AbstractDSLDao;

/**
 * Implementation of the {@link SwiftPaymentsRepository}.
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
public class SwiftPaymentsRepositoryImpl extends AbstractDSLDao implements SwiftPaymentsRepository {

    private static final QVbswiftpayments Q_VBSWIFTPAYMENTS = QVbswiftpayments.vbswiftpayments;

    /**
     * Primary constructor.
     *
     * @param dataSource data source
     */
    public SwiftPaymentsRepositoryImpl(final DataSource dataSource) {
        super(dataSource);
    }

    @Nonnull
    @Override
    public BigDecimal sumMdlAmount(@Nonnull final String externalClientId, @Nonnull final LocalDate signetAt) {
        final var queryResult = withQuery(query -> query
            .select(Q_VBSWIFTPAYMENTS.amountinmdl.sum()))
            .from(Q_VBSWIFTPAYMENTS)
            .where(Q_VBSWIFTPAYMENTS.clientid.eq(externalClientId)
                .and(Q_VBSWIFTPAYMENTS.active.isTrue())
                .and(Q_VBSWIFTPAYMENTS.signedat.after(signetAt.atStartOfDay()))
            )
            .fetch();

        return queryResult.get(0) == null ? BigDecimal.ZERO : queryResult.get(0);
    }

    @Nonnull
    @Override
    public Map<String, BigDecimal> sumMdlAndEurAmounts(@Nonnull final String externalClientId, @Nonnull final LocalDate signetAt) {
        final var queryResult = withQuery(query -> query
                        .select(Q_VBSWIFTPAYMENTS.amountinmdl.sum(),
                                Q_VBSWIFTPAYMENTS.amountineur.sum()
                                ))
                        .from(Q_VBSWIFTPAYMENTS)
                        .where(Q_VBSWIFTPAYMENTS.clientid.eq(externalClientId)
                            .and(Q_VBSWIFTPAYMENTS.active.isTrue())
                            .and(Q_VBSWIFTPAYMENTS.signedat.after(signetAt.atStartOfDay()))
                        )
                        .fetch();
        final BigDecimal mdlSum = queryResult.get(0).get(0, BigDecimal.class);
        final BigDecimal eurSum = queryResult.get(0).get(1, BigDecimal.class);
        return Map.of(
            Currency.MDL.getId(), mdlSum == null ? BigDecimal.ZERO : mdlSum,
            Currency.EUR.getId(), eurSum == null ? BigDecimal.ZERO : eurSum);
    }

    @Override
    public void registerOrUpdatePayment(@Nonnull final Vbswiftpayments paymentLimit) {
        final var instructionId = paymentLimit.getInstructionid();
        final var clientId = paymentLimit.getClientid();

        final var whereExpression = Q_VBSWIFTPAYMENTS.instructionid.eq(instructionId)
            .and(Q_VBSWIFTPAYMENTS.clientid.eq(clientId));

        final var notExisting = withQuery(query -> query.select()
            .select(Expressions.constant(1)))
            .from(Q_VBSWIFTPAYMENTS)
            .where(whereExpression)
            .fetchResults()
            .isEmpty();

        // Make sure the record is active.
        paymentLimit.setActive(true);

        if (notExisting) {
            withInsertClause(Q_VBSWIFTPAYMENTS, clause -> clause
                .populate(paymentLimit)
                .execute());
            log.debug("Created a record for instructionId={} and clientId={}",
                paymentLimit.getInstructionid(), paymentLimit.getClientid());
        } else {
            final var count = withUpdateClause(Q_VBSWIFTPAYMENTS, clause -> clause
                .populate(paymentLimit)
                .where(whereExpression)
                .execute());
            log.debug("Updated {} record(s) where instructionId={} and clientId={}",
                count, instructionId, clientId);
        }
    }

    @Override
    public long deactivatePayment(@Nonnull final Long instructionId, @Nonnull final String clientExternalId) {
        final var count = withUpdateClause(Q_VBSWIFTPAYMENTS, clause -> clause
            .where(Q_VBSWIFTPAYMENTS.instructionid.eq(instructionId)
                .and(Q_VBSWIFTPAYMENTS.clientid.eq(clientExternalId))
                .and(Q_VBSWIFTPAYMENTS.active.isTrue()))
            .set(Q_VBSWIFTPAYMENTS.active, false) // De/activation is driven with `active` column.
            .execute());
        log.debug("Deactivated {} record(s) where instructionId={} and clientExternalId={}",
            count, instructionId, clientExternalId);
        return count;
    }

    @Override
    public long removePayments(@Nonnull final LocalDate signedBefore) {
        final var count = withDeleteClause(Q_VBSWIFTPAYMENTS, clause -> clause
            .where(Q_VBSWIFTPAYMENTS.signedat.before(signedBefore.atStartOfDay()))
            .execute());
        log.debug("Removed {} record(s) signed before {}", count, signedBefore);
        return count;
    }
}
