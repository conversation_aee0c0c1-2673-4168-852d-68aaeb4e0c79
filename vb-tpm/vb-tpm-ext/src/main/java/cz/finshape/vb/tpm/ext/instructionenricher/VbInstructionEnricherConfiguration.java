/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import jakarta.annotation.PostConstruct;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import cz.finshape.vb.tpm.ext.enricher.attribute.AcceptLanguageEnricher;
import lombok.extern.slf4j.Slf4j;

/**
 * Configuration for instruction enrichers.
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@Import({
    AcceptLanguageEnricher.class,
    AddProductEnricher.class,
    BranchEnricher.class,
    ContinuallyCoordinateClientCenteredManufacturedIdEnricher.class,
    CardMaskedNumberEnricher.class,
    CardExpiryDateEnricher.class,
    CreateP2PAliasEnricher.class,
    DomesticPaymentOrderEnricher.class,
    EmbossingEnricher.class,
    IntrabankPaymentOrderEnricher.class,
    IntrabankFCyPaymentOrderEnricher.class,
    IpAddressEnricher.class,
    SwiftPaymentOrderEnricher.class,
    MPayPaymentOrderEnricher.class,
    OwnAccountPaymentOrderEnricher.class,
    IpsPhonePaymentOrderEnricher.class,
    IpsM2MPaymentOrderEnricher.class,
    IpsQrcPaymentOrderEnricher.class,
    IpsRtpPaymentOrderEnricher.class,
    P2PPaymentOrderEnricher.class,
    PaymentFeeEnricher.class,
    RegisterAccountInMPayEnricher.class,
    SetCardLimitsRequestEnricher.class
})
@ConditionalOnProperty(value = "enabled", prefix = "tpm.plugin.instructionenricher", matchIfMissing = true)
public class VbInstructionEnricherConfiguration {

    /**
     * Post construct method.
     */
    @PostConstruct
    public void postConstruct() {
        log.trace("{} applied", this.getClass().getSimpleName());
    }
}
