/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;

import static java.util.Optional.ofNullable;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum.QR_CODE_NOT_FOUND;
import static cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum.TPM_INSTRUCTION_ITEM_NOT_FOUND;
import static cz.finshape.vb.domain.internal.gds.GdsCatalogValues.CountryValue.MOLDOVA;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.IPS_QRC_PAYMENT_ORDER;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getBigDecimalValue;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getStringValue;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.UUID;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.ips.gw.IpsGwIntegrationApiConnector;
import cz.finshape.vb.core.utils.ObjectMapperUtils;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ClientExtension;
import cz.finshape.vb.domain.dbos.gds.Country;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.dbos.tpm.IpsQrcPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.domain.thirdparty.generated.ipsx.gateway.qrc.model.QrExtensionAdditionalInfo;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.exception.TpmSystemException;
import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for IpsQrcPaymentOrder instructions.
 *
 * <ul>
 *     <li>adds client's information</li>
 *     <li>adds creditor data information</li>
 *     <li>adds static payment information</li>
 * </ul>
 *
 * @see BranchEnricher
 *
 * <AUTHOR> Kundrat
 */
@Slf4j
@Component
public class IpsQrcPaymentOrderEnricher extends AbstractIpsEnricher {

    public static final String TRANSACTION_CODE_INDIVIDUAL = "202";

    public static final String TRANSACTION_CODE_LEGAL = "302";

    public static final String PAYMENT_ORDER_BIS_TYPE = "1";

    private final TpmInstructionService tpmInstructionService;

    private final IpsGwIntegrationApiConnector ipsGwIntegrationApiConnector;

    /**
     * Constructor.
     *
     * @param tpmInstructionService TPM instruction service
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param ipsGwIntegrationApiConnector ips gw integration api connector
     */
    public IpsQrcPaymentOrderEnricher(
        final TpmInstructionService tpmInstructionService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final IpsGwIntegrationApiConnector ipsGwIntegrationApiConnector) {

        super(gdsIntegrationApiConnector);
        this.tpmInstructionService = tpmInstructionService;
        this.ipsGwIntegrationApiConnector = ipsGwIntegrationApiConnector;
    }

    @Override
    public ObjectNode enrich(@Nonnull final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        if (!IPS_QRC_PAYMENT_ORDER.equalsInstructionTypeCode(type)) {
            return itemData;
        }
        log.debug("Enriching instruction of type {}", type);

        enrichByClientData(context, itemData);
        enrichByAccountData(context, itemData);
        enrichByDataFromQrc(itemData);
        enrichByTransactionStaticData(itemData);
        enrichByPaymentType(itemData, context);

        return itemData;
    }

    /**
     * Enriches the instruction by client data.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByClientData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (isUnknownOwnership(context)) {
            return;
        }
        if (context.getInstruction().getOwnership() == null || !context.getInstruction().getOwnership().isOwnedByClient()) {
            log.error("Client id is missing in the instruction");
            return;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client clientsData = tpmInstructionService.getClientData(externalClientId);
        final ClientExtension clientAddress = tpmInstructionService.getClientAddress(externalClientId);
        final var residency = Optional.ofNullable(clientsData.getResidency()).map(ResidencyType::getId).orElse(null);

        itemData.put(IpsQrcPaymentOrderInstruction.FN_CLIENTNAME, clientsData.getFullName());
        itemData.put(IpsQrcPaymentOrderInstruction.FN_FISCALCODE, clientsData.getFiscalCode());

        final var country = ofNullable(clientAddress.getCountry())
            .map(Country::getId)
            .orElse(MOLDOVA.getEntityUniqueId());
        itemData.put(IpsQrcPaymentOrderInstruction.FN_CLIENTCOUNTRY, country);
        itemData.put(IpsQrcPaymentOrderInstruction.FN_RESIDENCY, residency);
    }

    /**
     * Enriches the instruction by data from Qrc account info.
     *
     * @param itemData the item data
     */
    private void enrichByDataFromQrc(final ObjectNode itemData) {
        final Optional<String> qrcPayloadOpt = getStringValue(itemData, IpsQrcPaymentOrderInstruction.FN_QRCPAYLOAD);
        if (qrcPayloadOpt.isEmpty()) {
            log.warn("qrcPayload is not present in instruction data");
            throw new TpmSystemException(TPM_INSTRUCTION_ITEM_NOT_FOUND, "qrcPayload is not present in instruction data");
        }

        final var qrcPayload = qrcPayloadOpt.get();
        try {
            UUID.fromString(qrcPayload);
        } catch (IllegalArgumentException e) {
            log.warn("qrcPayload doesn't have proper format of UUID, qrcPayload: {}", qrcPayload);
            throw new TpmSystemException(
                TPM_INSTRUCTION_ITEM_NOT_FOUND,
                "qrcPayload doesn't have proper format of UUID, qrcPayload: " + qrcPayload);
        }

        final Optional<BigDecimal> amount = getBigDecimalValue(itemData, IpsQrcPaymentOrderInstruction.FN_AMOUNT);
        if (amount.isEmpty()) {
            log.warn("Amount is not present in instruction data");
            throw new TpmSystemException(TPM_INSTRUCTION_ITEM_NOT_FOUND, "Amount is not present in instruction data");
        }

        //TODO P20009724-6853 remove money value formation into utils class
        final var transactionData = ipsGwIntegrationApiConnector.findQrcTransactionData(qrcPayload,
                                                                                        amount.get().setScale(2, RoundingMode.DOWN));
        if (transactionData.isEmpty()) {
            log.warn("QR code data not found for qrcPayload: {}", qrcPayload);
            throw new TpmSystemException(QR_CODE_NOT_FOUND, "QR code data not found for qrcPayload: " + qrcPayload);
        }
        if (isEmpty(transactionData.get().getDocumentToken())) {
            log.warn("Document token is missing in the QR code data for qrcPayload: {}", qrcPayload);
            throw new TpmSystemException(QR_CODE_NOT_FOUND, "Document token is missing in the QR code data for qrcPayload: " + qrcPayload);
        }

        final var qrcData = ObjectMapperUtils.convertToString(transactionData.get());
        final var qrExtension = transactionData.get().getResponse2getQr().getExtension();

        itemData.put(IpsQrcPaymentOrderInstruction.FN_QRCTRANSACTIONDATA, qrcData);
        itemData.put(IpsQrcPaymentOrderInstruction.FN_PARTNERACCOUNTNUMBER, qrExtension.getCreditorAccount().getIban());
        itemData.put(IpsQrcPaymentOrderInstruction.FN_PARTNERBIC, qrExtension.getCreditorAgent().getBic());
        itemData.put(IpsQrcPaymentOrderInstruction.FN_PARTNERMEMBERID, qrExtension.getCreditorAgent().getMemberId());
        itemData.put(IpsQrcPaymentOrderInstruction.FN_PARTNERNAME, qrExtension.getCreditorName());
        itemData.put(IpsQrcPaymentOrderInstruction.FN_REMITTANCEINFORMATION, qrExtension.getRemittanceInfo4Payer());

        final var additionalInfo = qrExtension.getAdditionalInfo();
        itemData.put(IpsQrcPaymentOrderInstruction.FN_PARTNERCOUNTRY, additionalInfo.getCountryOfResidence());
        itemData.put(IpsQrcPaymentOrderInstruction.FN_PARTNERFISCALCODE, additionalInfo.getTaxId());

        if (QrExtensionAdditionalInfo.CustomerTypeEnum.LEGAL.equals(additionalInfo.getCustomerType())) {
            itemData.put(IpsQrcPaymentOrderInstruction.FN_TRANSACTIONCODE, TRANSACTION_CODE_LEGAL);
        } else {
            itemData.put(IpsQrcPaymentOrderInstruction.FN_TRANSACTIONCODE, TRANSACTION_CODE_INDIVIDUAL);
        }

    }

    /**
     * Enriches the instruction by transaction static data.
     *
     * @param itemData the item data
     */
    private void enrichByTransactionStaticData(@Nonnull final ObjectNode itemData) {
        itemData.put(IpsQrcPaymentOrderInstruction.FN_BIC, VbConstants.VICTORIA_BANK_BIC);
        itemData.put(IpsQrcPaymentOrderInstruction.FN_BANKNAME, VbConstants.VICTORIA_BANK_NAME.replace(".", EMPTY));
        itemData.put(IpsQrcPaymentOrderInstruction.FN_PAYMENTORDERBISTYPE, PAYMENT_ORDER_BIS_TYPE);
    }

    /**
     * Enriches the instruction by branch id.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void enrichByAccountData(@Nonnull final InstructionItemEnricherContext context, @Nonnull final ObjectNode itemData) {
        if (containsUnknownEntityRelation(context)) {
            return;
        }
        final Optional<String> debitAccountIdOpt = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (debitAccountIdOpt.isEmpty()) {
            log.error("Account id is missing in the instruction (instructionId={})", context.getInstruction().getInstructionId());
            return;
        }
        setAccountNumber(context, itemData, IpsQrcPaymentOrderInstruction.FN_ACCOUNTNUMBER);
    }

    @Override
    protected String getPaymentTypePrefix() {
        return CbsPaymentType.IPS_QR_PREFIX;
    }
}
