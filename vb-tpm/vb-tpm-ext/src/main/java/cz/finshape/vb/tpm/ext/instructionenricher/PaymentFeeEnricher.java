/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;


import static cz.finshape.vb.domain.dbos.tpm.OwnAccountPaymentOrderInstruction.FN_AMOUNT;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.MPAY_PAYMENT_ORDER;
import static cz.finshape.vb.domain.internal.tpm.InstructionType.P2P_PAYMENT_ORDER;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getEnumValue;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getStringValue;

import java.math.BigDecimal;
import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.spx.SpxIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.tpm.MPayPaymentOrderInstruction;
import cz.finshape.vb.domain.dbos.tpm.SwiftPaymentOrderInstruction;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import cz.finshape.vb.domain.internal.spx.SpxPaymentFeeRequest;
import cz.finshape.vb.domain.internal.spx.mpay.Invoices;
import cz.finshape.vb.domain.internal.tpm.CbsPaymentType;
import cz.finshape.vb.domain.internal.tpm.InstructionFlowDefinition;
import cz.finshape.vb.domain.internal.tpm.InstructionType;
import cz.finshape.vb.domain.thirdparty.internal.CbsPaymentFeeRequest;
import cz.finshape.vb.domain.thirdparty.internal.ChargeType;
import cz.finshape.vb.tpm.ext.service.IpsService;
import cz.finshape.vb.tpm.ext.service.TpmInstructionService;
import cz.finshape.vb.tpm.ext.support.InstructionItemUtils;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Enricher for payment instructions which calculates payment fee or use fee from MPay.
 *
 * <AUTHOR> Cernil
 */
@Slf4j
@Component
@Order(Ordered.LOWEST_PRECEDENCE - 1) // Lowest precedence to assure FN_CBSPAYMENTTYPE is available.
public class PaymentFeeEnricher extends VbInstructionItemEnricher {

    private static final String FN_PAYMENT_FEE = "paymentFee"; // paymentFee is common for all payment instructions

    private final SpxIntegrationApiConnector spxIntegrationApiConnector;

    private final TpmInstructionService tpmInstructionService;

    private final IpsService ipsService;

    /**
     * Instantiates a new Payment fee enricher.
     *
     * @param gdsIntegrationApiConnector GDS Integration API Connector
     * @param spxIntegrationApiConnector SPX Integration API Connector
     * @param tpmInstructionService TPM Instruction Service
     * @param ipsService IPS Service
     */
    public PaymentFeeEnricher(
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final SpxIntegrationApiConnector spxIntegrationApiConnector,
        final TpmInstructionService tpmInstructionService,
        final IpsService ipsService) {

        super(gdsIntegrationApiConnector);
        this.spxIntegrationApiConnector = spxIntegrationApiConnector;
        this.tpmInstructionService = tpmInstructionService;
        this.ipsService = ipsService;
    }

    @Override
    public ObjectNode enrich(final InstructionItemEnricherContext context, final ObjectNode itemData) {
        final var type = context.getInstructionTypeDefinition().getCode();
        final var financialInstructionTypes = InstructionFlowDefinition.financialInstructionTypes();

        if (financialInstructionTypes.stream().noneMatch(instructionType -> instructionType.equalsInstructionTypeCode(type))) {
            return itemData;
        }

        if (containsUnknownEntityRelation(context)) {
            return itemData;
        }

        if (type.equals(MPAY_PAYMENT_ORDER.getCode())) {
            loadFeeForMpay(context, itemData);
        } else if (InstructionType.isIpsInstructionType(type)) {
            loadFeeForIpsPayment(context, itemData);
        } else if (type.equals(P2P_PAYMENT_ORDER.getCode())) {
            loadFeeForCardRelation(context, itemData, CbsPaymentType.P2P);
        } else {
            loadFeeForAccountRelation(context, itemData);
        }
        return itemData;
    }

    /**
     * Load payment fee for MPay instruction
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void loadFeeForMpay(@Nonnull final InstructionItemEnricherContext context,
                                @Nonnull final ObjectNode itemData) {
        final Optional<String> invoiceNumber = getStringValue(itemData, MPayPaymentOrderInstruction.FN_INVOICENUMBER);
        if (invoiceNumber.isEmpty()) {
            log.warn("Invoice number not found for instruction: {}", context.getInstruction().getInstructionId());
            itemData.put(FN_PAYMENT_FEE, BigDecimal.ZERO);
            return;
        }

        final Invoices.InvoiceDetail invoiceDetail = spxIntegrationApiConnector.getMPayInvoiceDetail(invoiceNumber.get());
        if (invoiceDetail == null || invoiceDetail.getAttributes() == null) {
            log.warn("Invoice detail not found for instruction: {}", context.getInstruction().getInstructionId());
            itemData.put(FN_PAYMENT_FEE, BigDecimal.ZERO);
            return;
        }

        if (isUnknownOwnership(context)) {
            return;
        }
        final String externalClientId = context.getInstruction().getOwnership().getClientId();
        final Client client = tpmInstructionService.getClientData(externalClientId);
        if (GdsCatalogValues.LegalCategoryTypeValue.isCorporate(client.getLegalCategory())) {
            final var paymentFee = invoiceDetail.getAttributes().get("Extra comision");
            if (paymentFee != null) {
                itemData.put(FN_PAYMENT_FEE, new BigDecimal(paymentFee));
            } else {
                log.warn("Payment fee not found for instruction: {}", context.getInstruction().getInstructionId());
                itemData.put(FN_PAYMENT_FEE, BigDecimal.ZERO);
            }
        } else {
            final var isFee = invoiceDetail.getAttributes().get("PF achita extra");
            final var feeType = invoiceDetail.getAttributes().get("Tip prestator");
            if ("Da".equalsIgnoreCase(isFee)) {
                log.info("Payment fee for instruction {} have type: {}", context.getInstruction().getInstructionId(), feeType);
                //Fee will be load from FEE API, but for now bank doesn't support it (for now we try search value in corp field)
                final var paymentFee = invoiceDetail.getAttributes().get("Extra comision");
                itemData.put(FN_PAYMENT_FEE, paymentFee == null ? BigDecimal.ZERO : new BigDecimal(paymentFee));
            } else {
                itemData.put(FN_PAYMENT_FEE, BigDecimal.ZERO);
                log.warn("Payment fee not found for instruction: {}", context.getInstruction().getInstructionId());
            }
        }
    }

    /**
     * Add fee for IPS payment.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void loadFeeForIpsPayment(@Nonnull final InstructionItemEnricherContext context,
                                      @Nonnull final ObjectNode itemData) {

        if (isUnknownOwnership(context)) {
            return;
        }
        final Optional<BigDecimal> amount = InstructionItemUtils.getBigDecimalValue(itemData, FN_AMOUNT);
        if (amount.isEmpty()) {
            log.warn("Amount not found for instruction: {}", context.getInstruction().getInstructionId());
            itemData.put(FN_PAYMENT_FEE, BigDecimal.ZERO);
            return;
        }

        final var clientId = context.getInstruction().getOwnership().getClientId();
        final var calculatedFee = ipsService.calculateIpsPaymentFee(clientId, amount.get());
        log.info("Payment fee for IPS instruction {} is: {}", context.getInstruction().getInstructionId(), calculatedFee);
        itemData.put(FN_PAYMENT_FEE, calculatedFee);
    }

    /**
     * Load payment fee for account relation instruction from a service.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     */
    private void loadFeeForAccountRelation(@Nonnull final InstructionItemEnricherContext context,
                                           @Nonnull final ObjectNode itemData) {

        final Optional<String> debitAccountIdOptional = getRelatedEntityId(context, Account.EN_SINGULAR);
        if (debitAccountIdOptional.isEmpty()) {
            log.warn("debitAccountId not found for instruction: {}", context.getInstruction().getInstructionId());
            return;
        }
        final var debitAccountId = debitAccountIdOptional.get();

        // Debit account currency is not always available among the instruction item JSON data, so get it from the DB.
        final Account debitAccount = gdsIntegrationApiConnector.getEntityById(Account.EN_PLURAL, debitAccountId, Account.FN_CURRENCY);
        final var debitAccountCurrency = debitAccount.getCurrency().getId();

        String clientId = null;
        if (!context.getInstruction().getOwnership().isUnknownOwnership() && context.getInstruction().getOwnership().isOwnedByClient()) {
            clientId = context.getInstruction().getOwnership().getClientId();
        } else {
            log.warn("Instruction '{}' is not owned by Client", context.getInstruction().getInstructionId());
        }

        final var request = new SpxPaymentFeeRequest(
            new CbsPaymentFeeRequest()
                .withAmount(itemData.get("amount").decimalValue())
                .withPriority(getStringValue(itemData, "priority").orElse(VbConstants.PRIORITY_NORMAL))
                .withCurrency(debitAccountCurrency)
                .withBeneficiaryAccount(itemData.get("partnerAccountNumber").textValue())
                .withClientId(clientId)
                .withPaymentType(getStringValue(itemData, "cbsPaymentType").orElse(null))
                // Swift only attributes below:
                .withBicCode(getStringValue(itemData, SwiftPaymentOrderInstruction.FN_PARTNERBANKBICCODE).orElse(null))
                .withChargeType(getEnumValue(itemData, SwiftPaymentOrderInstruction.FN_FEEDISTRIBUTION, ChargeType::fromValue).orElse(null))
        );

        enrichFeeFromService(context, itemData, request);
    }

    /**
     * Load payment fee for card relation instruction from a service.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     * @param cbsPaymentType CBS payment type
     */
    private void loadFeeForCardRelation(@Nonnull final InstructionItemEnricherContext context,
                                        @Nonnull final ObjectNode itemData,
                                        @Nonnull final CbsPaymentType cbsPaymentType) {

        final var cardIdOpt = getRelatedEntityId(context, Card.EN_SINGULAR);
        final var cardId = cardIdOpt.orElse(null);
        if (cardId == null) {
            log.error("Card id not found for instruction: {}", context.getInstruction().getInstructionId());
            return;
        }

        final var clientsData = tpmInstructionService.getClientDataFromCardRelation(cardId);

        final var request = new SpxPaymentFeeRequest(
            new CbsPaymentFeeRequest()
                .withAmount(itemData.get("amount").decimalValue())
                .withCurrency(itemData.get("currency").textValue())
                .withBeneficiaryAccount(String.valueOf(itemData.get("panId").numberValue()))
                .withClientId(clientsData.getId())
                .withSourceProductId(cardIdOpt.orElse(null))
                .withPaymentType(cbsPaymentType.getCode())
        );

        enrichFeeFromService(context, itemData, request);
    }

    /**
     * Load payment fee from a service.
     *
     * @param context the instruction item enricher context
     * @param itemData the item data
     * @param spxRequest CBS payment type
     */
    private void enrichFeeFromService(@Nonnull final InstructionItemEnricherContext context,
                                      @Nonnull final ObjectNode itemData,
                                      @Nonnull final SpxPaymentFeeRequest spxRequest) {
        try {
            final BigDecimal paymentFee = spxIntegrationApiConnector.getPaymentFee(spxRequest).getFee();
            log.info("Payment fee for instruction {} is: {}", context.getInstruction().getInstructionId(), paymentFee);
            itemData.put(FN_PAYMENT_FEE, paymentFee);
        } catch (Exception e) {
            log.error("Failed to get payment fee for instruction: {}", context.getInstruction().getInstructionId(), e);
        }
    }
}
