/*
 * Copyright (c) 2025 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.tpm.ext.instructionenricher;

import static cz.finshape.vb.domain.internal.VbConstants.VICTORIA_BANK_BIC;
import static cz.finshape.vb.tpm.ext.support.InstructionItemUtils.getStringValue;

import java.util.Locale;

import jakarta.annotation.Nonnull;

import com.fasterxml.jackson.databind.node.ObjectNode;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.core.client.dbos.GraphQLParams;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException;
import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import lombok.extern.slf4j.Slf4j;

import cz.bsc.tpm.plugin.instruction.InstructionItemEnricherContext;


/**
 * Abstract enricher for IPS instructions.
 *
 * <AUTHOR> Jakub
 */
@Slf4j
public abstract class AbstractIpsEnricher extends VbInstructionItemEnricher {

    protected final static String FN_BIC = "bic";

    protected static final String FN_PARTNERBIC = "partnerBic";

    protected static final String FN_CBSPAYMENTTYPE = "cbsPaymentType";

    protected static final String FN_PARTNERACCOUNTNUMBER = "partnerAccountNumber";

    protected static final String DELIMITER = "_";

    protected static final String DELIMITER_DEBIT = "_D_";

    protected static final String DELIMITER_INTRA = "_I_";

    protected static final String POSTFIX_T24 = "T24";

    protected static final String POSTFIX_W4 = "W4";

    /**
     * Constructor.
     *
     * @param gdsIntegrationApiConnector GDS integration API connector
     */
    public AbstractIpsEnricher(final GdsIntegrationApiConnector gdsIntegrationApiConnector) {
        super(gdsIntegrationApiConnector);
    }

    /**
     * Get payment type prefix.
     *
     * @return payment type prefix
     */
    protected abstract String getPaymentTypePrefix();

    /**
     * Enrich item data by payment type.
     *
     * @param itemData item data
     * @param context context
     */
    protected void enrichByPaymentType(@Nonnull final ObjectNode itemData, @Nonnull final InstructionItemEnricherContext context) {
        final var debitBic = geDebitBicFromItemData(itemData);
        final var creditBic = getCreditBicFromItemData(itemData);

        final var evaluatedPaymentType = isVictoriaBankBic(debitBic) && isVictoriaBankBic(creditBic) ?
            getIntrabankPaymentType(itemData, context) : getInterBankPaymentType(context);

        log.info("Evaluated payment type: {}", evaluatedPaymentType);
        itemData.put(FN_CBSPAYMENTTYPE, evaluatedPaymentType);
    }

    /**
     * Get intra bank payment type.
     *
     * @param itemData item data
     * @param context context
     * @return intra bank payment type
     */
    @Nonnull
    private String getIntrabankPaymentType(@Nonnull final ObjectNode itemData, @Nonnull final InstructionItemEnricherContext context) {
        final var debitAccountNumber = getDebitAccountNumber(context);
        final var debitSystem = ExternalSystem.getPaymentExternalSystemFromPrefix(debitAccountNumber).equals(ExternalSystem.T24)
            ? POSTFIX_T24
            : POSTFIX_W4;
        final var creditSystem = getExternalSystemForCreditAccount(itemData).equals(ExternalSystem.T24) ? POSTFIX_T24 : POSTFIX_W4;

        return getPaymentTypePrefix() + DELIMITER_INTRA + debitSystem + DELIMITER + creditSystem;
    }

    /**
     * Get interbank payment type.
     *
     * @param context context
     * @return interbank payment type
     */
    @Nonnull
    private String getInterBankPaymentType(@Nonnull final InstructionItemEnricherContext context) {
        final var debitAccountNumber = getDebitAccountNumber(context);
        final var debitSystem = ExternalSystem.getPaymentExternalSystemFromPrefix(debitAccountNumber).equals(ExternalSystem.T24)
            ? POSTFIX_T24
            : POSTFIX_W4;

        return getPaymentTypePrefix() + DELIMITER_DEBIT + debitSystem;
    }

    /**
     * Get debit BIC from item data.
     *
     * @param itemData item data
     * @return debit BIC
     */
    @Nonnull
    protected String geDebitBicFromItemData(@Nonnull final ObjectNode itemData) {
        return getStringValue(itemData, FN_BIC)
            .orElseThrow(() -> new VbBusinessException(VbErrorCodeEnum.NOT_FOUND, "Debit BIC not found"));
    }

    /**
     * Get credit BIC from item data.
     *
     * @param itemData item data
     * @return credit BIC
     */
    @Nonnull
    protected String getCreditBicFromItemData(@Nonnull final ObjectNode itemData) {
        return getStringValue(itemData, FN_PARTNERBIC)
            .orElseThrow(() -> new VbBusinessException(VbErrorCodeEnum.NOT_FOUND, "Credit BIC not found"));
    }

    /**
     * Get debit account number.
     *
     * @param context context
     * @return debit account number
     */
    @Nonnull
    protected String getDebitAccountNumber(@Nonnull final InstructionItemEnricherContext context) {
        return getRelatedEntityId(context, Account.EN_SINGULAR)
            .orElseThrow(() -> new VbBusinessException(VbErrorCodeEnum.NOT_FOUND, "Debit account number not found"));
    }

    /**
     * Get external system for credit account.
     *
     * @param itemData item data
     * @return credit account number
     */
    @Nonnull
    private ExternalSystem getExternalSystemForCreditAccount(final ObjectNode itemData) {
        //TODO: P20009724-7543: Jirsa: remove/keep exceptions after consulation with Vasek
        final var creditIban = getStringValue(itemData, FN_PARTNERACCOUNTNUMBER).orElseThrow(() -> new VbBusinessException(
            VbErrorCodeEnum.NOT_FOUND,
            "Credit IBAN not found"));

        final var accounts = gdsIntegrationApiConnector.<Account>getEntities(
            Account.EN_PLURAL, GraphQLParams.builder()
                .filter(Account.FN_IBAN + ".eq({0}) and " + Account.FN_ISINTERNAL + ".eq(false)")
                .filterParams(creditIban)
                .fields(Account.FN_IBAN, Account.FN_BISACCOUNTID, Account.FN_ISINTERNAL)
                .build());

        if (accounts.size() != 1) {
            throw new VbBusinessException(VbErrorCodeEnum.NOT_FOUND, "Cannot find non technical account with IBAN " + creditIban);
        }

        final var nonTechnicalBisAccountId = accounts.stream()
            .findFirst()
            .map(Account::getBisAccountId)
            .orElseThrow(() -> new IllegalArgumentException("Cannot find non technical account with IBAN= " + creditIban));

        return ExternalSystem.getPaymentExternalSystemFromPrefix(nonTechnicalBisAccountId);
    }

    /**
     * Check if BIC is Victoria Bank BIC, ignoring camel case.
     *
     * @param bic BIC
     * @return {@code true} if BIC is Victoria Bank BIC, {@code false} otherwise
     */
    private boolean isVictoriaBankBic(@Nonnull final String bic) {
        return bic.toLowerCase(Locale.getDefault()).startsWith(VICTORIA_BANK_BIC.toLowerCase(Locale.getDefault()));
    }
}
