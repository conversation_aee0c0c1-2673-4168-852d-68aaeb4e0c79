package cz.finshape.vb.enr.core.controller;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException;
import cz.finshape.vb.enr.VbEnrApplication;
import cz.finshape.vb.enr.core.config.server.RestServerExceptionHandlerConfiguration;
import cz.finshape.vb.enr.core.domain.RegisterMobileVerificationRequest;
import cz.finshape.vb.enr.core.domain.RegisterMobileVerificationResponse;
import cz.finshape.vb.enr.core.service.EnrollmentService;


/**
 * Tests {@link EnrollmentBusinessController}.
 *
 * <AUTHOR> Sarana
 */
@SpringBootTest(
    classes = {
        VbEnrApplication.class,
        EnrollmentBusinessController.class,
        RestServerExceptionHandlerConfiguration.class
    },
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc(addFilters = false)
class EnrollmentBusinessControllerTest {

    @MockBean
    private EnrollmentService enrollmentService;

    @Autowired
    protected MockMvc mockMvc;

    @Test
    void validateExistingUser() throws Exception {

        mockMvc.perform(post("/business/v1/enrollment/validate-existing-user")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .content("""
                    {
                    "userIdnp": "12345"
                    }
                    """))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.userIdnp").value("12345"));

        verify(enrollmentService).validateExistingUser("12345");
    }

    @Test
    void validateExistingUser_user_doesnt_exist() throws Exception {

        doThrow(new VbBusinessException(VbErrorCodeEnum.ENR_IDNP_NOT_VALID, "User doesnt exist")).when(enrollmentService)
            .validateExistingUser("12345");

        mockMvc.perform(post("/business/v1/enrollment/validate-existing-user")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .content("""
                    {
                        "userIdnp": "12345"
                    }
                    """))
            .andExpect(status().isBadRequest())
            .andExpect(jsonPath("$.exceptionType").value("cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException"))
            .andExpect(jsonPath("$.exceptionMessage").value("User doesnt exist"))
            .andExpect(jsonPath("$.errorCode").value("ENR_IDNP_NOT_VALID"));

        verify(enrollmentService).validateExistingUser("12345");
    }

    @Test
    void registerMobileVerification() throws Exception {

        var authorizationBearerToken = "Bearer <header>.<payload>.<signature>";
        ArgumentCaptor<RegisterMobileVerificationRequest> requestArgumentCaptor =
            ArgumentCaptor.forClass(RegisterMobileVerificationRequest.class);

        var signingId = "asdqwe123";
        var response = new RegisterMobileVerificationResponse(signingId);

        when(enrollmentService.registerMobileVerification(eq(authorizationBearerToken), requestArgumentCaptor.capture())).thenReturn(response);
        mockMvc.perform(post("/business/v1/enrollment/register-mobile-verification")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .header("Authorization", authorizationBearerToken)
                .content("""
                    {
                        "req_type":"verify-mobile-phone",
                        "req_data":{
                            "userIdnp":"12345"
                        }
                    }
                    """))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.signing_req_id").value(signingId));

        verify(enrollmentService).registerMobileVerification(eq(authorizationBearerToken), any());

        assertThat(requestArgumentCaptor.getValue(), notNullValue());

        var request = requestArgumentCaptor.getValue();

        assertThat(request.getRequestType(), is("verify-mobile-phone"));
        assertThat(request.getRequestData().getUserIdnp(), is("12345"));
    }

    @Test
    void registerMobileVerification_device_not_found() throws Exception {

        var authorizationBearerToken = "Bearer <header>.<payload>.<signature>";

        when(enrollmentService.registerMobileVerification(eq(authorizationBearerToken), any())).thenThrow(new VbBusinessException(
            VbErrorCodeEnum.ENR_DEVICE_NOT_FOUND,
            "Device was not found"));
        mockMvc.perform(post("/business/v1/enrollment/register-mobile-verification")
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .accept(MediaType.APPLICATION_JSON_VALUE)
                .header("Authorization", authorizationBearerToken)
                .content("""
                    {
                        "req_type":"verify-mobile-phone",
                        "req_data":{
                            "userIdnp":"12345"
                        }
                    }
                      """))
            .andExpect(status().isNotFound())
            .andExpect(jsonPath("$.exceptionType").value("cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException"))
            .andExpect(jsonPath("$.exceptionMessage").value("Device was not found"))
            .andExpect(jsonPath("$.errorCode").value("ENR_DEVICE_NOT_FOUND"));

        verify(enrollmentService).registerMobileVerification(eq(authorizationBearerToken), any());

    }
}
