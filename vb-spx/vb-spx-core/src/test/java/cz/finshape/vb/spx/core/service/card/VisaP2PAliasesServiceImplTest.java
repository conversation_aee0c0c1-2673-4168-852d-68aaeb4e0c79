package cz.finshape.vb.spx.core.service.card;

import static org.assertj.core.api.Assertions.assertThat;
import static org.easymock.EasyMock.capture;
import static org.easymock.EasyMock.expect;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import org.easymock.Capture;
import org.easymock.CaptureType;
import org.easymock.EasyMock;
import org.easymock.EasyMockExtension;
import org.easymock.EasyMockSupport;
import org.easymock.IAnswer;
import org.easymock.Mock;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.test.util.ReflectionTestUtils;

import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.internal.spx.SpxInquiryP2PAliasRequest;
import cz.finshape.vb.domain.internal.spx.SpxP2PAliasContact;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryEntityDetail;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryEntityItem;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryItem;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryResponseInternal;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasResolveBasicResponse;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasResolveRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.BaseAliasRequestProfile;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialItemAliasResponse;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialPreferredForWithDateItem;
import cz.finshape.vb.spx.core.connector.cbs.way4.Way4PanApiConnector;
import cz.finshape.vb.spx.core.connector.visa.ads.VisaAdsApiConnector;


/**
 * Tests {@link VisaP2PAliasesServiceImpl}.
 *
 * <AUTHOR> Charalambidis
 */
@ExtendWith(EasyMockExtension.class)
class VisaP2PAliasesServiceImplTest extends EasyMockSupport {

    private VisaP2PAliasesServiceImpl tested;

    @Mock
    private CardIntegrationService cardIntegrationService;

    @Mock
    private GdsIntegrationApiConnector gdsIntegrationApiConnector;

    @Mock
    private VisaAdsApiConnector visaAdsApiConnector;

    @Mock
    private Way4PanApiConnector way4PanApiConnector;

    @BeforeEach
    void beforeEach() {
        tested = new VisaP2PAliasesServiceImpl(
            "A1b2C3d4E5f6G7h8I9j0K1l2M3n4O5p6Q7r8S9t0U1v2W3x4Y5z6A7b8C9d0E1f2G3h4I5j6K7l8M9n0O1p2Q3r4S5t6U7v8W9x0Y1z2A3b4C5d6E7f8G9h0I"
                + "1j2K3l4M5n6O7p8Q9r0S1t2U3v4W5x6Y7z8A9b0C1d2E3f4G5h6I7j8K9l0M1n2O3p4Q5r6S7t8U9v0W1x2Y3z4A5b6C7d8E9f0G1h2I3j4K5l6M7n8O9p0Q1r2S"
                + "3t4U5v6W7x8",
            cardIntegrationService, gdsIntegrationApiConnector, visaAdsApiConnector, way4PanApiConnector);

        ReflectionTestUtils.setField(tested, "checkEntityIdEnabled", true);
        ReflectionTestUtils.setField(tested, "checkEntityIdValue", "682bb6af-850b-46d0-955a-d118b2356b7d");
        ReflectionTestUtils.setField(tested, "aliasInquiryBatchSize", 952);
    }

    @Test
    void inquiryAlias() {
        final var aliasInquiryResponseInternal = new AliasInquiryResponseInternal();
        aliasInquiryResponseInternal.setDetails(List.of(
            aliasInquiryItem("37337312345", true),
            aliasInquiryItem("37311111111", false),
            aliasInquiryItem("26391777666", false)
        ));

        final var aliasInquiryRequestCapture = Capture.<AliasInquiryRequest>newInstance();
        expect(visaAdsApiConnector.inquiryAlias(capture(aliasInquiryRequestCapture)))
            .andReturn(aliasInquiryResponseInternal);

        final var request = new SpxInquiryP2PAliasRequest(List.of(
            // Valid Moldovan phone numbers.
            new SpxP2PAliasContact().withContactId("md1").withPhoneNumber("66123456"),
            new SpxP2PAliasContact().withContactId("md2").withPhoneNumber("037312345"),
            new SpxP2PAliasContact().withContactId("md3").withPhoneNumber("37311111111"),
            // Valid foreign (Zimbabwean) phone numbers.
            new SpxP2PAliasContact().withContactId("zw1").withPhoneNumber("459 915"),
            new SpxP2PAliasContact().withContactId("zw2").withPhoneNumber("263 91 777666"),
            new SpxP2PAliasContact().withContactId("zw3").withPhoneNumber("09-207129"),
            // Invalid phone number.
            new SpxP2PAliasContact().withContactId("foo1").withPhoneNumber("112"),
            new SpxP2PAliasContact().withContactId("foo2").withPhoneNumber("123456789"),
            new SpxP2PAliasContact().withContactId("foo3").withPhoneNumber("731000000"),
            new SpxP2PAliasContact().withContactId("foo4").withPhoneNumber("37397009955")
        ));

        replayAll();
        final var result = tested.inquiryAlias(request);
        verifyAll();

        assertThat(result).isNotNull();
        final var data = result.getData();
        assertThat(data).hasSize(10);
        assertAliasContract(data.get(0), "md1", "66123456", null, false, null);
        assertAliasContract(data.get(1), "md2", "037312345", "37337312345", true, true);
        assertAliasContract(data.get(2), "md3", "37311111111", "37311111111", true, false);
        assertAliasContract(data.get(3), "zw1", "459 915", null, false, null);
        assertAliasContract(data.get(4), "zw2", "263 91 777666", "26391777666", true, false);
        assertAliasContract(data.get(5), "zw3", "09-207129", null, false, null);
        assertAliasContract(data.get(6), "foo1", "112", null, false, null);
        assertAliasContract(data.get(7), "foo2", "123456789", null, false, null);
        assertAliasContract(data.get(8), "foo3", "731000000", null, false, null);
        assertAliasContract(data.get(9), "foo4", "37397009955", null, false, null);
    }

    /**
     * Sabína Rybnická has a schizophrenic contact list, it's a material worth to test.
     */
    @Test
    void inquiryAlias_sabina() {
        final var aliasInquiryResponseInternal = new AliasInquiryResponseInternal();
        aliasInquiryResponseInternal.setDetails(List.of(
            aliasInquiryItem("37369250330", true),
            aliasInquiryItem("74958881111", false),
            aliasInquiryItem("37369551946", false),
            aliasInquiryItem("37362025081", true),
            aliasInquiryItem("37322123456", true),
            aliasInquiryItem("111", true),
            aliasInquiryItem("37369250324", true),
            aliasInquiryItem("37362240909", true),
            aliasInquiryItem("971501234567", true),
            aliasInquiryItem("37379999999", false),
            aliasInquiryItem("4357651127", false),
            aliasInquiryItem("37379190362", true),
            aliasInquiryItem("78945324556", false)
        ));

        final var aliasInquiryRequestCapture = Capture.<AliasInquiryRequest>newInstance();
        expect(visaAdsApiConnector.inquiryAlias(capture(aliasInquiryRequestCapture)))
            .andReturn(aliasInquiryResponseInternal);

        final var request = new SpxInquiryP2PAliasRequest(List.of(
            // Valid Moldovan phone numbers.
            new SpxP2PAliasContact().withContactId("1").withPhoneNumber("+373 (69) 250330"),
            new SpxP2PAliasContact().withContactId("2").withPhoneNumber("37369250330"),
            new SpxP2PAliasContact().withContactId("3").withPhoneNumber("00 373 (69) 250330"),
            new SpxP2PAliasContact().withContactId("4").withPhoneNumber("0911 010 101"),
            new SpxP2PAliasContact().withContactId("5").withPhoneNumber("791 903 62"),
            new SpxP2PAliasContact().withContactId("6").withPhoneNumber("37379190362"),
            new SpxP2PAliasContact().withContactId("7").withPhoneNumber("695 51946"),
            new SpxP2PAliasContact().withContactId("8").withPhoneNumber("*#,,333*+;#"),
            new SpxP2PAliasContact().withContactId("9").withPhoneNumber("37362240909"),
            new SpxP2PAliasContact().withContactId("10").withPhoneNumber("+373 (60) 43251"),
            new SpxP2PAliasContact().withContactId("11").withPhoneNumber("9955123456789"),
            new SpxP2PAliasContact().withContactId("12").withPhoneNumber("74958881111"),
            new SpxP2PAliasContact().withContactId("13").withPhoneNumber("+373 (60) 123456"),
            new SpxP2PAliasContact().withContactId("14").withPhoneNumber("+373 (79) 025637"),
            new SpxP2PAliasContact().withContactId("15").withPhoneNumber("37369085544"),
            new SpxP2PAliasContact().withContactId("16").withPhoneNumber("37379999999"),
            new SpxP2PAliasContact().withContactId("17").withPhoneNumber("37362025081"),
            new SpxP2PAliasContact().withContactId("18").withPhoneNumber(""),
            new SpxP2PAliasContact().withContactId("19").withPhoneNumber("+373 (68) 836483"),
            new SpxP2PAliasContact().withContactId("20").withPhoneNumber("*111*#"),
            new SpxP2PAliasContact().withContactId("21").withPhoneNumber("78945324556"),
            new SpxP2PAliasContact().withContactId("22").withPhoneNumber("4357651127"),
            new SpxP2PAliasContact().withContactId("23").withPhoneNumber("37322123456"),
            new SpxP2PAliasContact().withContactId("24").withPhoneNumber("971501234567"),
            new SpxP2PAliasContact().withContactId("25").withPhoneNumber("+373 (69) 250324")
        ));

        replayAll();
        final var result = tested.inquiryAlias(request);
        verifyAll();

        assertThat(aliasInquiryRequestCapture.hasCaptured()).isTrue();
        assertAliasInquiryRequest(aliasInquiryRequestCapture.getValue(),
            "37369250330", "37369250330", "37369250330", "0911010101", "37379190362", "37379190362", "37369551946", "*#,,333*+;#",
            "37362240909", "373(60)43251", "9955123456789", "74958881111", "37360123456", "37379025637", "37369085544", "37379999999",
            "37362025081", "", "37368836483", "*111*#", "78945324556", "4357651127", "37322123456", "971501234567", "37369250324");

        assertThat(result).isNotNull();
        final var data = result.getData();
        assertThat(data).hasSize(25);

        assertAliasContract(data.get(0),  "1",  "+373 (69) 250330", "37369250330", true, true);
        assertAliasContract(data.get(1),  "2",  "37369250330", "37369250330", true, true);
        assertAliasContract(data.get(2),  "3",  "00 373 (69) 250330", "37369250330", true, true);
        assertAliasContract(data.get(3),  "4",  "0911 010 101", null, false, null);
        assertAliasContract(data.get(4),  "5",  "791 903 62", "37379190362", true, true);
        assertAliasContract(data.get(5),  "6",  "37379190362", "37379190362", true, true);
        assertAliasContract(data.get(6),  "7",  "695 51946", "37369551946", true, false);
        assertAliasContract(data.get(7),  "8",  "*#,,333*+;#", null, false, null);
        assertAliasContract(data.get(8),  "9",  "37362240909", "37362240909", true, true);
        assertAliasContract(data.get(9),  "10", "+373 (60) 43251", null, false, null);
        assertAliasContract(data.get(10), "11", "9955123456789", null, false, null);
        assertAliasContract(data.get(11), "12", "74958881111", "74958881111", true, false);
        assertAliasContract(data.get(12), "13", "+373 (60) 123456", null, false, null);
        assertAliasContract(data.get(13), "14", "+373 (79) 025637", null, false, null);
        assertAliasContract(data.get(14), "15", "37369085544", null, false, null);
        assertAliasContract(data.get(15), "16", "37379999999", "37379999999", true, false);
        assertAliasContract(data.get(16), "17", "37362025081", "37362025081", true, true);
        assertAliasContract(data.get(17), "18", "", null, false, null);
        assertAliasContract(data.get(18), "19", "+373 (68) 836483", null, false, null);
        assertAliasContract(data.get(19), "20", "*111*#", null, false, null);
        assertAliasContract(data.get(20), "21", "78945324556", "78945324556", true, false);
        assertAliasContract(data.get(21), "22", "4357651127", "4357651127", true, false);
        assertAliasContract(data.get(22), "23", "37322123456", "37322123456", true, true);
        assertAliasContract(data.get(23), "24", "971501234567", "971501234567", true, true);
        assertAliasContract(data.get(24), "25", "+373 (69) 250324", "37369250324", true, true);
    }



    @Test
    void inquiryAlias_large() {
        // Let's assume the phone user is insane and has tons of contact numbers. We reduce numbers for the sake of test.
        final int contactCount = 25;
        ReflectionTestUtils.setField(tested, "aliasInquiryBatchSize", 10);

        final var contacts = Stream.iterate(1000L, i -> i + 1L)
            .limit(contactCount)
            .map(String::valueOf)
            .map(integer -> new SpxP2PAliasContact()
                .withContactId("contact-" + integer)
                .withPhoneNumber(integer))
            .toList();
        final var aliasInquiryRequestsCapture = Capture.<AliasInquiryRequest>newInstance(CaptureType.ALL);
        expect(visaAdsApiConnector.inquiryAlias(capture(aliasInquiryRequestsCapture)))
            .andAnswer(new AliasInquiryResponseInternalAnswer())
            .times(3);

        final var request = new SpxInquiryP2PAliasRequest(contacts);

        replayAll();
        final var result = tested.inquiryAlias(request);
        verifyAll();

        assertThat(result).isNotNull();
        final var data = result.getData();
        assertThat(data).hasSize(25);

        assertThat(aliasInquiryRequestsCapture.hasCaptured()).isTrue();
        final var aliasInquiryRequests = aliasInquiryRequestsCapture.getValues();
        assertAliasInquiryRequest(aliasInquiryRequests.get(0),
            "1000", "1001", "1002", "1003", "1004", "1005", "1006", "1007", "1008", "1009");
        assertAliasInquiryRequest(aliasInquiryRequests.get(1),
            "1010", "1011", "1012", "1013", "1014", "1015", "1016", "1017", "1018", "1019");
        assertAliasInquiryRequest(aliasInquiryRequests.get(2),
            "1020", "1021", "1022", "1023", "1024");
    }

    @ParameterizedTest
    @CsvSource(value = {
        "John, Doe, John Doe",
        "John, D,   John D.",
        "John, D.,  John D.",
    }, nullValues = {"null"})
    void resolveAliasProfile_withProfile(final String firstName, final String lastName, final String expectedAliasProfileName) {
        final var paymentCredential = new PaymentCredentialItemAliasResponse();
        paymentCredential.setNameOnCard("name-on-card");

        final var profile = new BaseAliasRequestProfile();
        profile.setFirstName(firstName);
        profile.setLastName(lastName);

        final var resolveAliasResponse = new AliasResolveBasicResponse();
        resolveAliasResponse.setPaymentCredentials(List.of(paymentCredential));
        resolveAliasResponse.setProfile(profile);

        final var resolveAliasRequestCapture = Capture.<AliasResolveRequest>newInstance();
        expect(visaAdsApiConnector.resolveAlias(capture(resolveAliasRequestCapture))).andReturn(resolveAliasResponse);

        replayAll();
        final var result = tested.resolveAliasProfile("37366123456");
        verifyAll();

        assertThat(result.getAliasProfileName()).isEqualTo(expectedAliasProfileName);
    }

    @Test
    void resolveAliasProfile_withoutProfile() {
        final var paymentCredential = new PaymentCredentialItemAliasResponse();
        paymentCredential.setNameOnCard("name-on-card");

        final var resolveAliasResponse = new AliasResolveBasicResponse();
        resolveAliasResponse.setPaymentCredentials(List.of(paymentCredential));

        final var resolveAliasRequestCapture = Capture.<AliasResolveRequest>newInstance();
        expect(visaAdsApiConnector.resolveAlias(capture(resolveAliasRequestCapture))).andReturn(resolveAliasResponse);

        replayAll();
        final var result = tested.resolveAliasProfile("37366123456");
        verifyAll();

        assertThat(result.getAliasProfileName()).isEqualTo("name-on-card");
    }

    private void assertAliasInquiryRequest(final AliasInquiryRequest request, final String... aliasValues) {
        assertThat(request).isNotNull();
        assertThat(request.getAliases()).isNotNull();
        assertThat(request.getAliases()).hasSize(aliasValues.length);
        assertThat(request.getAliases()).containsExactly(aliasValues);
    }

    @ParameterizedTest(name = "{index} [{0}] -> [{1}]")
    @CsvSource(value = {
        // Moldovan phone numbers without a plus character.
        " 66123456 ,   37366123456",
        "06-6123456,   37366123456",
        "373 12345,    37337312345",
        "-037312345,   37337312345",
        "6956  7890,   37369567890",
        // Moldovan phone numbers with a plus character.
        "+ 66123456 ,  37366123456",
        "+06-6123456,  37366123456",
        "+373 12345,   37337312345",
        "+0-37312345,  37337312345",
        "+0-69567890,  37369567890",
        // Foreign (Zimbabwean) phone numbers without a plus character.
        "459    915,   459915",
        "26391777666,  26391777666",
        "09-207129,    09207129",
        // Foreign (Zimbabwean) phone numbers with a plus character.
        "+459    915,  459915",
        "+26391777666, 26391777666",
        "+09-207129,   09207129",
        // Invalid phone numbers (they are passed as is).
        "112,          112",
        "123456789,    123456789",
        "731000000,    731000000",
        "37397009955,  37397009955",
        "37320240911,  37320240911",
    }, nullValues = "null")
    void formatAsVisaPhoneNumber(final String rawPhoneNumber, final String phoneNumber) {
        final var result = tested.formatAsVisaPhoneNumber(rawPhoneNumber);
        assertThat(result).isEqualTo(phoneNumber);
    }

    @ParameterizedTest(name = "{index} [{0}]")
    @ValueSource(strings = {
        // Moldovan phone numbers.
        "66123456,  37366123456",
        "066123456, 37366123456",
        "37312345,  37337312345",
        "037312345, 37337312345",
        "69567890,  37369567890",
    })
    void formatAsMoldovanVisaPhoneNumber(final String sanitizedPhoneNumber) {
        final var result = tested.formatAsMoldovanVisaPhoneNumber(sanitizedPhoneNumber);
        assertThat(result).isEmpty();
    }

    @ParameterizedTest(name = "{index} [{0}]")
    @ValueSource(strings = {
        // Foreign (Zimbabwean) phone numbers.
        "459915",
        "26391777666",
        "09207129",
        // Invalid phone numbers.
        "112",
        "123456789",
        "731000000",
        "37397009955",
        "37320240911",
    })
    void formatAsMoldovanVisaPhoneNumber_invalid(final String sanitizedPhoneNumber) {
        final var result = tested.formatAsMoldovanVisaPhoneNumber(sanitizedPhoneNumber);
        assertThat(result).isEmpty();
    }

    private AliasInquiryEntityDetail aliasInquiryEntityDetail(final String entityId, final PaymentCredentialPreferredForWithDateItem.TypeEnum typeEnum) {
        final var paymentCredentialPreferredForWithDateItem = new PaymentCredentialPreferredForWithDateItem();
        paymentCredentialPreferredForWithDateItem.setType(typeEnum);

        final var aliasInquiryEntityItem = new AliasInquiryEntityItem();
        aliasInquiryEntityItem.setPreferredFor(List.of(paymentCredentialPreferredForWithDateItem));
        aliasInquiryEntityItem.setId(entityId);

        final var aliasInquiryEntityDetail = new AliasInquiryEntityDetail();
        aliasInquiryEntityDetail.setEntities(List.of(aliasInquiryEntityItem));

        return aliasInquiryEntityDetail;
    }

    private AliasInquiryItem aliasInquiryItem(final String aliasValue, final boolean isVbPreferred) {
        final var directories = new ArrayList<>(List.of(
            // Mismatching entity ID.
            aliasInquiryEntityDetail("foo", PaymentCredentialPreferredForWithDateItem.TypeEnum.PAY),
            aliasInquiryEntityDetail("foo", PaymentCredentialPreferredForWithDateItem.TypeEnum.RECEIVE),
            aliasInquiryEntityDetail("foo", PaymentCredentialPreferredForWithDateItem.TypeEnum.SEND),
            // Mismatching type enum.
            aliasInquiryEntityDetail("682bb6af-850b-46d0-955a-d118b2356b7d", PaymentCredentialPreferredForWithDateItem.TypeEnum.PAY),
            aliasInquiryEntityDetail("682bb6af-850b-46d0-955a-d118b2356b7d", PaymentCredentialPreferredForWithDateItem.TypeEnum.SEND)
        ));

        if (isVbPreferred) {
            directories.add(aliasInquiryEntityDetail("682bb6af-850b-46d0-955a-d118b2356b7d", PaymentCredentialPreferredForWithDateItem.TypeEnum.RECEIVE));
        }

        final var aliasInquiryItem = new AliasInquiryItem();
        aliasInquiryItem.setAliasType(AliasInquiryItem.AliasTypeEnum.PHONE);
        aliasInquiryItem.setAliasValue(aliasValue);
        aliasInquiryItem.setDirectories(directories);
        return aliasInquiryItem;
    }

    private void assertAliasContract(final SpxP2PAliasContact aliasContact,
                                     final String contactId,
                                     final String phoneNumber,
                                     final String phoneNumberFormatted,
                                     final boolean aliasExists,
                                     final Boolean isVbPreferred) {

        assertThat(aliasContact).isNotNull();
        assertThat(aliasContact.getContactId()).isEqualTo(contactId);
        assertThat(aliasContact.getPhoneNumber()).isEqualTo(phoneNumber);
        assertThat(aliasContact.getPhoneNumberFormatted()).isEqualTo(phoneNumberFormatted);
        assertThat(aliasContact.getAliasExists()).isEqualTo(aliasExists);
        assertThat(aliasContact.getIsVbPreferred()).isEqualTo(isVbPreferred);
    }

    private class AliasInquiryResponseInternalAnswer implements IAnswer<AliasInquiryResponseInternal> {
        @Override
        public AliasInquiryResponseInternal answer() {
            final var aliasInquiryRequest = (AliasInquiryRequest) EasyMock.getCurrentArgument(0);
            final var details = aliasInquiryRequest.getAliases()
                .stream()
                .map(alias -> aliasInquiryItem(alias, true))
                .toList();
            return new AliasInquiryResponseInternal()
                .details(details);
        }
    }
}
