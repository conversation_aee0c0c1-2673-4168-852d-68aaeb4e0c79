/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.spx.core.service.card;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.annotation.Nonnull;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.i18n.phonenumbers.PhoneNumberUtil;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.core.utils.PhoneUtils;
import cz.finshape.vb.core.utils.StreamUtils;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.gds.User;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException;
import cz.finshape.vb.domain.internal.core.exception.technical.VbTechnicalException;
import cz.finshape.vb.domain.internal.core.exception.technical.integration.VbRestException;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import cz.finshape.vb.domain.internal.spx.SpxCheckUserP2PAliasResponse;
import cz.finshape.vb.domain.internal.spx.SpxCreateP2PAliasRequest;
import cz.finshape.vb.domain.internal.spx.SpxCreateP2PAliasResponse;
import cz.finshape.vb.domain.internal.spx.SpxCreatePaymentCredentialsRequest;
import cz.finshape.vb.domain.internal.spx.SpxCreatePaymentCredentialsResponse;
import cz.finshape.vb.domain.internal.spx.SpxGetP2PAliasIdResponse;
import cz.finshape.vb.domain.internal.spx.SpxGetP2PAliasResponse;
import cz.finshape.vb.domain.internal.spx.SpxGetPaymentCredentialResponse;
import cz.finshape.vb.domain.internal.spx.SpxGetPaymentCredentialsResponse;
import cz.finshape.vb.domain.internal.spx.SpxInquiryP2PAliasRequest;
import cz.finshape.vb.domain.internal.spx.SpxInquiryP2PAliasResponse;
import cz.finshape.vb.domain.internal.spx.SpxP2PAliasBillingAddress;
import cz.finshape.vb.domain.internal.spx.SpxP2PAliasCard;
import cz.finshape.vb.domain.internal.spx.SpxP2PAliasConsent;
import cz.finshape.vb.domain.internal.spx.SpxP2PAliasContact;
import cz.finshape.vb.domain.internal.spx.SpxP2PAliasPreferredFor;
import cz.finshape.vb.domain.internal.spx.SpxP2PAliasProfile;
import cz.finshape.vb.domain.internal.spx.SpxResolveAndStoreP2PAliasRequest;
import cz.finshape.vb.domain.internal.spx.SpxResolveAndStoreP2PAliasResponse;
import cz.finshape.vb.domain.internal.spx.SpxResolveP2PAliasProfileResponse;
import cz.finshape.vb.domain.internal.spx.SpxUpdateP2PAliasRequest;
import cz.finshape.vb.domain.internal.spx.SpxUpdateP2PAliasStatusRequest;
import cz.finshape.vb.domain.internal.spx.SpxUpdatePaymentCredentialsRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.Address;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryItem;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasInquiryRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasResolveRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.AliasValueRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.BaseAliasRequestProfile;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.ConsentConsent;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.CreateAliasRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.CreatePaymentCredentialRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialCommonPreferredForInner;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialCommonResponsePreferredForInner;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialItemRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialPreferredForWithDateItem;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.PaymentCredentialType;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.UpdateAliasRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.UpdateAliasStatusRequest;
import cz.finshape.vb.domain.thirdparty.generated.visa.ads.UpdatePaymentCredentialRequest;
import cz.finshape.vb.spx.core.connector.cbs.way4.Way4PanApiConnector;
import cz.finshape.vb.spx.core.connector.visa.ads.VisaAdsApiConnector;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.jsonwebtoken.lang.Assert;
import lombok.extern.slf4j.Slf4j;


/**
 * Visa P2P aliases service implementation.
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@Service
public class VisaP2PAliasesServiceImpl extends AbstractCardService implements VisaP2PAliasesService {

    @Value("${spx.core.service.visa.check-entity-id.enabled}")
    private Boolean checkEntityIdEnabled;

    @Value("${spx.core.service.visa.check-entity-id.value}")
    private String checkEntityIdValue;

    @Value("${spx.core.service.visa.alias-inquiry.batch-size}")
    private Integer aliasInquiryBatchSize;

    private final CardIntegrationService cardIntegrationService;
    private final GdsIntegrationApiConnector gdsIntegrationApiConnector;
    private final VisaAdsApiConnector visaAdsApiConnector;
    private final Way4PanApiConnector way4PanApiConnector;

    /**
     * Primary constructor.
     *
     * @param secretKeyAsString secret key for JWT
     * @param cardIntegrationService card integration service
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param visaAdsApiConnector Visa ADS API connector
     * @param way4PanApiConnector Way4 PAN API connector
     */
    protected VisaP2PAliasesServiceImpl(
        @Value("${vb.spx.jwt.secret}") final String secretKeyAsString,
        final CardIntegrationService cardIntegrationService,
        final GdsIntegrationApiConnector gdsIntegrationApiConnector,
        final VisaAdsApiConnector visaAdsApiConnector,
        final Way4PanApiConnector way4PanApiConnector) {

        super(secretKeyAsString);
        this.cardIntegrationService = cardIntegrationService;
        this.gdsIntegrationApiConnector = gdsIntegrationApiConnector;
        this.visaAdsApiConnector = visaAdsApiConnector;
        this.way4PanApiConnector = way4PanApiConnector;
    }

    @Nonnull
    @Override
    public SpxCreateP2PAliasResponse createAlias(@Nonnull final SpxCreateP2PAliasRequest spxRequest) {
        Assert.notNull(spxRequest, "request is null");
        final var cards = spxRequest.getCards();
        Assert.notNull(cards, "cards is null");
        Assert.notEmpty(cards, "cards is empty");
        final var consent = spxRequest.getConsent();
        Assert.notNull(consent, "consent is null");
        final var profile = spxRequest.getProfile();
        Assert.notNull(profile, "profile is null");

        log.debug("createAlias(request(aliasType={}, aliasValue={}, cards[*].cardId={})",
            spxRequest.getAliasType(), spxRequest.getAliasValue(), cards.stream().map(SpxP2PAliasCard::getCardId).toList());

        final List<PaymentCredentialItemRequest> createAliasPaymentCredentialsList = new ArrayList<>();
        for (final SpxP2PAliasCard card : cards) {
            Assert.notNull(card.getBillingAddress(), "cards[cardId=" + card.getCardId() + "].billingAddress is null");

            // Step 1: Get the card's PAN.
            final var cardInfo = cardIntegrationService.getCardInfo(card.getCardId());
            final var pan = cardInfo.getPan();
            final var cardExpiry = cardInfo.parseCardExpiryAsYearMonth()
                .orElseThrow(() -> new VbTechnicalException(VbErrorCodeEnum.ARGUMENT_ILLEGAL,
                    "Cannot parse '%s' as YearMonth".formatted(cardInfo.getCardExpiry())));

            // Step 2: Visa Alias Directory Service (ADS) "create alias" endpoint to create an alias and assign a card to it.
            final var createAliasPaymentCredentialsBillingAddress = new Address();
            createAliasPaymentCredentialsBillingAddress.setCountry(card.getBillingAddress().getCountry());

            final var paymentCredentialCommonPreferredForInner = new PaymentCredentialCommonPreferredForInner();
            paymentCredentialCommonPreferredForInner.setType(PaymentCredentialCommonPreferredForInner.TypeEnum.RECEIVE);

            final var createAliasPaymentCredentials = new PaymentCredentialItemRequest();
            createAliasPaymentCredentials.setAccountNumber(pan);
            createAliasPaymentCredentials.setBillingAddress(createAliasPaymentCredentialsBillingAddress);
            createAliasPaymentCredentials.setCardType(card.getCardType());
            createAliasPaymentCredentials.setExpirationDate(cardExpiry.toString()); // YearMonth formats as {@code uuuu-MM} by default.
            createAliasPaymentCredentials.setIssuerName(VbConstants.VICTORIA_BANK_NAME);
            createAliasPaymentCredentials.setNameOnCard("%s %s".formatted(profile.getFirstName(), profile.getLastName()));
            createAliasPaymentCredentials.setType(PaymentCredentialType.CARD.getValue());
            createAliasPaymentCredentials.setExternalId(card.getCardId());
            createAliasPaymentCredentials.setPreferredFor(List.of(paymentCredentialCommonPreferredForInner));

            createAliasPaymentCredentialsList.add(createAliasPaymentCredentials);
        }

        // Step 2: Visa Alias Directory Service (ADS) "create alias" endpoint to create an alias and assign a card to it.
        final var createAliasConsent = new ConsentConsent();
        createAliasConsent.setPresenter(VbConstants.VICTORIA_BANK_NAME);
        createAliasConsent.setValidFromDateTime(consent.getValidFromDateTime());
        createAliasConsent.setVersion(consent.getVersion());

        final var createAliasProfile = new BaseAliasRequestProfile();
        createAliasProfile.setFirstName(spxRequest.getProfile().getFirstName());
        createAliasProfile.setLastName(spxRequest.getProfile().getLastName());

        final var createAliasRequest = new CreateAliasRequest();
        createAliasRequest.setAliasType(spxRequest.getAliasType());
        createAliasRequest.setAliasValue(spxRequest.getAliasValue());
        createAliasRequest.setConsent(createAliasConsent);
        createAliasRequest.setPaymentCredentials(createAliasPaymentCredentialsList);
        createAliasRequest.setProfile(createAliasProfile);

        final var createAliasResponse = visaAdsApiConnector.createAlias(createAliasRequest);

        // Step 3: Get detail of all created payment credentials to check if visa tokenized the card and get the value of the token.
        final var getPaymentCredentialsResponse = visaAdsApiConnector.getPaymentCredentials(createAliasResponse.getId());
        final var responseCards = getPaymentCredentialsResponse.stream()
            .map(card -> new SpxP2PAliasCard()
                .withId((String) card.getId())
                .withPaymentCredentialId((String) card.getId())
                .withCardId(card.getExternalId())
                .withCardType(card.getCardType())
                .withPreferredFor(Optional.ofNullable(card.getPreferredFor())
                    .orElse(List.of())
                    .stream()
                    .map(p -> new SpxP2PAliasPreferredFor(p.getType().getValue()))
                    .toList())
                .withNameOnCard(card.getNameOnCard())
                .withType(card.getType().getValue())
                .withBillingAddress(new SpxP2PAliasBillingAddress(card.getBillingAddress().getCountry()))
                .withExpirationDate(card.getExpirationDate())
                .withIssuerName(card.getIssuerName()))
            .toList();

        return new SpxCreateP2PAliasResponse()
            .withAliasId(createAliasResponse.getId())
            .withCards(responseCards);
    }

    @Nonnull
    @Override
    public SpxCheckUserP2PAliasResponse checkUserAlias(@Nonnull final String userId) {
        Assert.notNull(userId, "userId is null");
        log.debug("checkUserAlias(userId={})", userId);

        final User user = gdsIntegrationApiConnector.getEntityById(User.EN_PLURAL, userId);
        final var aliasId = user.getP2pAliasId();
        final var phoneNumber = user.getContactMobileNumber();
        final var formattedPhoneNumber = formatAsVisaPhoneNumber(phoneNumber);

        if (aliasId == null) {
            log.debug("P2P alias is null for user '{}'", userId);
            checkAliasConflict(userId, formattedPhoneNumber);
        } else {
            log.debug("P2P alias is not null for user '{}' : {}", userId, aliasId);
            try {
                final var getAliasResponse = getAlias(aliasId);
                if (!formattedPhoneNumber.equals(getAliasResponse.getAliasValue())) {
                    log.error("P2P alias does not match for user '{}' : actual '{}', expected '{}'",
                        userId, getAliasResponse.getAliasValue(), formattedPhoneNumber);
                    throw new VbRestException(VbErrorCodeEnum.ALIAS_VALUE_INCONSISTENCY, 400);
                } else {
                    final var paymentCredentials = getPaymentCredentials(aliasId);
                    final var cardId = paymentCredentials.getCards().stream()
                        .filter(card -> PaymentCredentialType.CARD.getValue().equals(card.getType()))
                        .filter(card -> card.getPreferredFor().stream()
                            .anyMatch(preferredFor -> preferredFor
                                .getType()
                                .equals(PaymentCredentialCommonResponsePreferredForInner.TypeEnum.RECEIVE.getValue())))
                        .map(SpxP2PAliasCard::getCardId)
                        .reduce(StreamUtils.toOnlyElement())
                        .orElseThrow(() -> new VbTechnicalException(VbErrorCodeEnum.ARGUMENT_NULL,
                            "No payment credential found for aliasId '%s' having type != '%s' and preferredFor[*].type = %s'"
                                .formatted(aliasId,
                                    PaymentCredentialType.CARD.getValue(),
                                    PaymentCredentialCommonResponsePreferredForInner.TypeEnum.RECEIVE.getValue())));

                    final Card card = gdsIntegrationApiConnector.getEntityById(Card.EN_PLURAL, cardId);
                    if (GdsCatalogValues.CardStatusesValue.CLOSED.getEntityUniqueId().equals(card.getStatus().getId())) {
                        return new SpxCheckUserP2PAliasResponse();
                    } else {
                        return new SpxCheckUserP2PAliasResponse()
                            .withAliasId(aliasId)
                            .withAliasValue(formattedPhoneNumber)
                            .withStatus(getAliasResponse.getStatus())
                            .withP2PAliasProfile(new SpxP2PAliasProfile()
                                .withFirstName(getAliasResponse.getProfile().getFirstName())
                                // The Visa ADS returns for CEMEA and EU only the first letter.
                                .withLastName(getAliasResponse.getProfile().getLastName()))
                            .withPreferredCardId(cardId);
                    }
                }
            } catch (VbBusinessException vbBusinessException) {
                if (VbErrorCodeEnum.NOT_FOUND.getCode().equals(vbBusinessException.getError().getCode())) {
                    checkAliasConflict(userId, formattedPhoneNumber);
                } else {
                    throw vbBusinessException;
                }
            }
        }

        throw new VbTechnicalException(VbErrorCodeEnum.UNKNOWN, "Unknown flow error");
    }

    @Override
    public void deleteAlias(@Nonnull final String aliasId) {
        Assert.notNull(aliasId, "aliasId is null");
        log.debug("deleteAlias(aliasId={})", aliasId);

        visaAdsApiConnector.deleteAlias(aliasId);
    }

    @Nonnull
    @Override
    public SpxGetP2PAliasResponse getAlias(@Nonnull final String aliasId) {
        Assert.notNull(aliasId, "aliasId is null");
        log.debug("getAlias(aliasId={})", aliasId);

        final var getAliasResponse = visaAdsApiConnector.getAlias(aliasId);
        final var getAliasResponseProfile = getAliasResponse.getProfile();
        final var getAliasResponseConsent = getAliasResponse.getConsent();

        final var response = new SpxGetP2PAliasResponse()
            .withAliasValue(getAliasResponse.getAliasValue())
            .withAliasType(getAliasResponse.getAliasType() == null ? null : getAliasResponse.getAliasType().getValue())
            .withStatus(getAliasResponse.getStatus() == null ? null : getAliasResponse.getStatus().getValue())
            .withAliasId(aliasId);

        if (getAliasResponseConsent != null) {
            response.setProfile(new SpxP2PAliasProfile()
                .withFirstName(getAliasResponseProfile.getFirstName())
                .withLastName(getAliasResponseProfile.getLastName()));
        }

        if (getAliasResponseConsent != null) {
            response.setConsent(new SpxP2PAliasConsent()
                .withPresenter(getAliasResponseConsent.getPresenter())
                .withValidFromDateTime(getAliasResponseConsent.getValidFromDateTime())
                .withVersion(getAliasResponseConsent.getVersion()));
        }

        return response;
    }

    @Nonnull
    @Override
    public SpxGetP2PAliasIdResponse getAliasIdFromValue(@Nonnull final String aliasValue) {
        Assert.notNull(aliasValue, "aliasValue is null");
        log.debug("getAliasFromValue(aliasValue={})", aliasValue);

        final var aliasValueRequest = new AliasValueRequest()
            .aliasValue(aliasValue);

        final var getAliasIdFromValueResponse = visaAdsApiConnector.getAliasIdFromValue(aliasValueRequest);
        return new SpxGetP2PAliasIdResponse()
            .withAliasId(getAliasIdFromValueResponse.getId());
    }

    @Override
    public void updateAlias(@Nonnull final String aliasId, @Nonnull final SpxUpdateP2PAliasRequest spxRequest) {
        Assert.notNull(aliasId, "aliasId is null");
        Assert.notNull(spxRequest, "spxRequest is null");

        log.debug("updateAlias(aliasId={})", aliasId);

        final var consent = spxRequest.getConsent();
        final var profile = spxRequest.getProfile();

        final var updateAliasRequest = new UpdateAliasRequest();
        if (consent != null) {
            updateAliasRequest.setConsent(new ConsentConsent()
                .version(consent.getVersion())
                .presenter(consent.getPresenter())
                .validFromDateTime(consent.getValidFromDateTime()));
        }
        if (profile != null) {
            updateAliasRequest.setProfile(new BaseAliasRequestProfile()
                .firstName(profile.getFirstName())
                .lastName(profile.getLastName()));
        }

        visaAdsApiConnector.updateAlias(aliasId, updateAliasRequest);
    }

    @Override
    public void updateAliasStatus(@Nonnull final String aliasId, @Nonnull final SpxUpdateP2PAliasStatusRequest spxRequest) {
        Assert.notNull(aliasId, "aliasId is null");
        Assert.notNull(spxRequest, "spxRequest is null");
        Assert.notNull(spxRequest.getStatus(), "spxRequest.status is null");
        log.debug("updateAliasStatus(aliasId={}, spxRequest.status={})", aliasId, spxRequest.getStatus());

        final var updateAliasStatusRequest = new UpdateAliasStatusRequest();
        updateAliasStatusRequest.setStatus(UpdateAliasStatusRequest.StatusEnum.valueOf(spxRequest.getStatus()));
        visaAdsApiConnector.updateAliasStatus(aliasId, updateAliasStatusRequest);
    }

    @Nonnull
    @Override
    public SpxResolveAndStoreP2PAliasResponse resolveAndStoreAlias(@Nonnull final SpxResolveAndStoreP2PAliasRequest spxRequest) {
        Assert.notNull(spxRequest, "spxRequest is null");
        log.debug("resolveAndStoreAlias(spxRequest.phoneNumber={})", spxRequest.getPhoneNumber());

        // Step 1: Visa ADS for alias resolution.
        final var aliasResolveRequest = new AliasResolveRequest();
        aliasResolveRequest.setAliasType(AliasResolveRequest.AliasTypeEnum.PHONE);
        aliasResolveRequest.setAliasValue(formatAsVisaPhoneNumber(spxRequest.getPhoneNumber()));

        final var aliasResolveResponse = visaAdsApiConnector.resolveAlias(aliasResolveRequest);

        // Step 2: Way4 to store the PAN/token received from Visa.
        final var paymentCredential = Optional.ofNullable(aliasResolveResponse.getPaymentCredentials())
            .orElse(List.of())
            .stream()
            .filter(s -> s.getPreferredFor() != null)
            .filter(s -> s.getPreferredFor().stream().anyMatch(d -> d.getType() == PaymentCredentialPreferredForWithDateItem.TypeEnum.RECEIVE))
            .findFirst()
            .orElseThrow(() -> new VbTechnicalException(VbErrorCodeEnum.ARGUMENT_EMPTY, "No qualified payment credential found"));

        final var pan = paymentCredential.getAccountNumber();
        final var accountNumberType = paymentCredential.getAccountNumberType();
        if (accountNumberType == null) {
            throw new VbTechnicalException(VbErrorCodeEnum.ARGUMENT_NULL, "AccountNumberType is null");
        }
        final var isToken = switch (accountNumberType) {
            case TOKEN -> true;
            case PAN -> false;
            default -> throw new VbTechnicalException(VbErrorCodeEnum.ARGUMENT_ILLEGAL,
                "Unexpected accountNumberType : %s".formatted(paymentCredential.getAccountNumberType()));
        };

        final var storePanResponse = way4PanApiConnector.storePan(pan);
        final var panId = storePanResponse.getPanId();
        final var maskedNumber = storePanResponse.getMaskedPan();

        return new SpxResolveAndStoreP2PAliasResponse()
            .withPanId(storePanResponse.getPanId())
            .withIsToken(isToken)
            .withMaskedNumber(maskedNumber)
            .withSignedData(getSignedData(panId, maskedNumber));
    }

    @Nonnull
    @Override
    public SpxGetPaymentCredentialsResponse getPaymentCredentials(@Nonnull final String aliasId) {
        Assert.notNull(aliasId, "aliasId is null");
        log.debug("getPaymentCredential(aliasId={})", aliasId);

        final var responseList = visaAdsApiConnector.getPaymentCredentials(aliasId);

        final var cards = responseList.stream()
            .map(card -> new SpxP2PAliasCard()
                .withId((String) card.getId())
                .withPaymentCredentialId((String) card.getId())
                .withCardId(card.getExternalId())
                .withCardType(card.getCardType())
                .withPreferredFor(Optional.ofNullable(card.getPreferredFor())
                    .orElse(List.of())
                    .stream()
                    .map(p -> new SpxP2PAliasPreferredFor(p.getType().getValue()))
                    .toList())
                .withNameOnCard(card.getNameOnCard())
                .withType(card.getType().getValue())
                .withBillingAddress(new SpxP2PAliasBillingAddress(card.getBillingAddress().getCountry()))
                .withExpirationDate(card.getExpirationDate())
                .withIssuerName(card.getIssuerName()))
            .toList();

        return new SpxGetPaymentCredentialsResponse()
            .withCards(cards);
    }

    @Nonnull
    @Override
    public SpxCreatePaymentCredentialsResponse createPaymentCredentials(@Nonnull final SpxCreatePaymentCredentialsRequest spxRequest,
                                                                        @Nonnull final String aliasId) {

        Assert.notNull(aliasId, "aliasId is null");
        Assert.notNull(spxRequest, "spxRequest is null");
        Assert.notNull(spxRequest.getBillingAddress(), "spxRequest.billingAddress is null");
        log.debug("updateAliasStatus(aliasId={}, spxRequest.cardId={}, spxRequest.cardType={})",
            aliasId, spxRequest.getCardId(), spxRequest.getCardType());

        // Step 1: Way4 native API to get the card's PAN and expiry.
        final var cardInfo = cardIntegrationService.getCardInfo(spxRequest.getCardId());
        final var pan = cardInfo.getPan();
        final var cardExpiry = cardInfo.parseCardExpiryAsYearMonth()
            .orElseThrow(() -> new VbTechnicalException(VbErrorCodeEnum.ARGUMENT_ILLEGAL,
                "Cannot parse '%s' as YearMonth".formatted(cardInfo.getCardExpiry())));

        // Step 2: Visa ADS "create payment credential" endpoint to assign a card to alias.
        final var createPaymentCredentialRequestBillingAddress = new Address();
        createPaymentCredentialRequestBillingAddress.setCountry(spxRequest.getBillingAddress().getCountry());

        final var paymentCredentialCommonPreferredForInner = new PaymentCredentialCommonPreferredForInner();
        paymentCredentialCommonPreferredForInner.setType(PaymentCredentialCommonPreferredForInner.TypeEnum.RECEIVE);

        final var createPaymentCredentialRequest = new CreatePaymentCredentialRequest();
        createPaymentCredentialRequest.setAccountNumber(pan);
        createPaymentCredentialRequest.setBillingAddress(createPaymentCredentialRequestBillingAddress);
        createPaymentCredentialRequest.setCardType(spxRequest.getCardType());
        createPaymentCredentialRequest.setExpirationDate(cardExpiry.toString()); // YearMonth formats as {@code uuuu-MM} by default.
        createPaymentCredentialRequest.setIssuerName(VbConstants.VICTORIA_BANK_NAME);
        createPaymentCredentialRequest.setNameOnCard(spxRequest.getNameOnCard());
        createPaymentCredentialRequest.setType(PaymentCredentialType.CARD.getValue());
        createPaymentCredentialRequest.setExternalId(spxRequest.getCardId());
        createPaymentCredentialRequest.setPreferredFor(List.of(paymentCredentialCommonPreferredForInner));

        final var createPaymentCredentialResponse = visaAdsApiConnector.createPaymentCredentials(aliasId, createPaymentCredentialRequest);

        // OpenAPI defines Object because the return type depends on the associated type.
        // In this case, we need String.
        if (createPaymentCredentialResponse.getId() instanceof String paymentCredentialId) {
            // Step 3: Get detail of all created payment credentials to check if visa tokenized the card and get the value of the token.
            final var getPaymentCredentialResponse = visaAdsApiConnector.getPaymentCredential(paymentCredentialId);

            final var response = new SpxCreatePaymentCredentialsResponse()
                .withPaymentCredentialId(paymentCredentialId)
                .withCardId(createPaymentCredentialResponse.getExternalId());

            final var accountNumberType = getPaymentCredentialResponse.getAccountNumberType();
            if (accountNumberType == null) {
                throw new VbTechnicalException(VbErrorCodeEnum.ARGUMENT_NULL, "AccountNumberType is null");
            }
            switch (accountNumberType) {
                case TOKEN: {
                    response.setIsToken(true);
                    response.setToken(getPaymentCredentialResponse.getAccountNumber());
                    break;
                }
                case PAN: {
                    response.setIsToken(false);
                    break;
                }
                default: throw new VbTechnicalException(VbErrorCodeEnum.ARGUMENT_ILLEGAL,
                    "Unexpected accountNumberType : %s".formatted(getPaymentCredentialResponse.getAccountNumberType()));
            }

            return response;
        } else {
            throw new VbTechnicalException(VbErrorCodeEnum.CLASS_ILLEGAL,
                "Unexpected type : %s".formatted(createPaymentCredentialResponse.getId().getClass().getName()));
        }

    }

    @Nonnull
    @Override
    public SpxGetPaymentCredentialResponse getPaymentCredential(@Nonnull final String paymentCredentialId) {
        Assert.notNull(paymentCredentialId, "paymentCredentialId is null");
        log.debug("getPaymentCredential(paymentCredentialId={})", paymentCredentialId);

        final var getPaymentCredentialResponse = visaAdsApiConnector.getPaymentCredential(paymentCredentialId);

        final var isPreferred = Optional.ofNullable(getPaymentCredentialResponse.getPreferredFor())
            .orElseThrow(() -> new VbTechnicalException(VbErrorCodeEnum.ARGUMENT_NULL, "preferredFor is null"))
            .stream()
            .anyMatch(preferredFor -> preferredFor.getType() == PaymentCredentialCommonResponsePreferredForInner.TypeEnum.RECEIVE);

        return new SpxGetPaymentCredentialResponse()
            .withCardId(getPaymentCredentialResponse.getExternalId())
            .withIsPreferred(isPreferred);
    }

    @Override
    public void updatePaymentCredential(@Nonnull final String paymentCredentialId,
                                        @Nonnull final SpxUpdatePaymentCredentialsRequest spxRequest) {

        Assert.notNull(paymentCredentialId, "paymentCredentialId is null");
        log.debug("updatePaymentCredential(paymentCredentialId={})", paymentCredentialId);

        final var udpatePaymentCredentialRequestBillingAddress = new Address();
        udpatePaymentCredentialRequestBillingAddress.setCountry(spxRequest.getBillingAddress().getCountry());

        final var preferredFor = spxRequest.getPreferredFor()
            .stream()
            .map(spxP2PAliasPreferredFor -> new PaymentCredentialCommonPreferredForInner()
                .type(PaymentCredentialCommonPreferredForInner.TypeEnum.fromValue(spxP2PAliasPreferredFor.getType())))
            .toList();

        final var updatePaymentCredentialRequest = new UpdatePaymentCredentialRequest();
        updatePaymentCredentialRequest.setExternalId(spxRequest.getCardId());
        updatePaymentCredentialRequest.setPreferredFor(preferredFor);
        updatePaymentCredentialRequest.setType(spxRequest.getType());
        updatePaymentCredentialRequest.setBillingAddress(udpatePaymentCredentialRequestBillingAddress);
        updatePaymentCredentialRequest.setCardType(spxRequest.getCardType());
        updatePaymentCredentialRequest.setExpirationDate(spxRequest.getExpirationDate());
        updatePaymentCredentialRequest.setIssuerName(spxRequest.getIssuerName());
        updatePaymentCredentialRequest.setNameOnCard(spxRequest.getNameOnCard());

        visaAdsApiConnector.updatePaymentCredential(paymentCredentialId, updatePaymentCredentialRequest);
    }

    @Override
    public void deletePaymentCredential(@Nonnull final String paymentCredentialId) {
        Assert.notNull(paymentCredentialId, "paymentCredentialId is null");
        log.debug("deletePaymentCredentialId(paymentCredentialId={})", paymentCredentialId);

        visaAdsApiConnector.deletePaymentCredential(paymentCredentialId);
    }

    @Nonnull
    @Override
    public SpxInquiryP2PAliasResponse inquiryAlias(@Nonnull final SpxInquiryP2PAliasRequest spxRequest) {
        Assert.notNull(spxRequest, "spxRequest is null");
        Assert.notNull(spxRequest.getData(), "spxRequest.data is null");
        Assert.notEmpty(spxRequest.getData(), "spxRequest.data is empty");
        log.debug("inquiryAlias(spxRequest.data.size={})", spxRequest.getData().size());

        record SpxTempData(String formattedPhoneNumber, SpxP2PAliasContact spxP2PAliasContact) {}

        // Temporary collection of SPX P2P alias contacts with their formatted phone number.
        final var spxTempDataList = spxRequest.getData()
            .stream()
            .<SpxTempData>mapMulti((contact, consumer) ->
                Optional.ofNullable(contact.getPhoneNumber())
                    .map(this::formatAsVisaPhoneNumber)
                    .map(formattedPhoneNumber -> new SpxTempData(formattedPhoneNumber, contact))
                    .ifPresent(consumer))
            .toList();

        final var aliases = spxTempDataList.stream()
            .map(SpxTempData::formattedPhoneNumber)
            .toList();

        final var aliasInquiryMap = new HashMap<String, AliasInquiryItem>();

        log.debug("Inquiring '{}' aliases by '{}'", aliases.size(), aliasInquiryBatchSize);
        for (int i = 0; i < aliases.size(); i += aliasInquiryBatchSize) {
            final var batch = aliases.subList(i, Math.min(i + aliasInquiryBatchSize, aliases.size()));
            log.debug("Inquiring alias batch, indices : {} - {}", i, i + batch.size() - 1);

            final var aliasInquiryRequest = new AliasInquiryRequest();
            aliasInquiryRequest.setAliases(batch);

            final var aliasInquiryResponse = visaAdsApiConnector.inquiryAlias(aliasInquiryRequest);
            final var batchResult = Optional.ofNullable(aliasInquiryResponse.getDetails())
                .orElse(List.of())
                .stream()
                .filter(s -> AliasInquiryItem.AliasTypeEnum.PHONE.equals(s.getAliasType()))
                .collect(Collectors.toMap(AliasInquiryItem::getAliasValue, Function.identity()));

            aliasInquiryMap.putAll(batchResult);
        }

        final var data = spxTempDataList.stream()
            .map(spxTempData -> {
                final var formattedPhoneNumber = spxTempData.formattedPhoneNumber();
                final var contact = spxTempData.spxP2PAliasContact();

                final var aliasInquiryItem = aliasInquiryMap.get(formattedPhoneNumber);

                final var spxP2PAliasContact = new SpxP2PAliasContact()
                    .withContactId(contact.getContactId())
                    .withPhoneNumber(contact.getPhoneNumber());

                if (aliasInquiryItem != null) {
                    final var isVbPreferred = isVbPreferred(aliasInquiryItem);
                    if (!formattedPhoneNumber.equals(aliasInquiryItem.getAliasValue())) {
                        log.warn("Unexpected Visa behavior: Returned Visa formatted number '{}' differs from input formatted phone number '{}'",
                            aliasInquiryItem.getAliasValue(), formattedPhoneNumber);
                    }
                    return spxP2PAliasContact
                        // Visa ADS returns already formatted phone number.
                        .withPhoneNumberFormatted(aliasInquiryItem.getAliasValue())
                        .withAliasExists(true)
                        .withIsVbPreferred(isVbPreferred);
                } else {
                    return spxP2PAliasContact
                        .withPhoneNumberFormatted(null)
                        .withAliasExists(false)
                        .withIsVbPreferred(null);
                }
            })
            .toList();

        return new SpxInquiryP2PAliasResponse()
            .withData(data);
    }

    @Nonnull
    @Override
    @SuppressFBWarnings("RCN_REDUNDANT_NULLCHECK_OF_NONNULL_VALUE")
    public SpxResolveP2PAliasProfileResponse resolveAliasProfile(@Nonnull final String phoneNumber) {
        Assert.notNull(phoneNumber, "phoneNumber is null");
        log.debug("resolveAliasProfile(phoneNumber={})", phoneNumber);

        final var aliasResolveRequest = new AliasResolveRequest();
        aliasResolveRequest.setAliasType(AliasResolveRequest.AliasTypeEnum.PHONE);
        aliasResolveRequest.setAliasValue(formatAsVisaPhoneNumber(phoneNumber));

        final var aliasResolveResponse = visaAdsApiConnector.resolveAlias(aliasResolveRequest);
        final var aliasResolveResponseProfile = aliasResolveResponse.getProfile();

        // There is a possible bug in Visa ADS as profile might be null.
        if (aliasResolveResponseProfile != null) {
            final var firstName = aliasResolveResponseProfile.getFirstName();
            final var lastName = aliasResolveResponseProfile.getLastName();
            final var aliasProfileName = lastName.length() == 1
                    ? "%s %s.".formatted(firstName, lastName)
                    : "%s %s".formatted(firstName, lastName);
            return new SpxResolveP2PAliasProfileResponse().withAliasProfileName(aliasProfileName);
        } else {
            final var nameOnCard = aliasResolveResponse.getPaymentCredentials().get(0).getNameOnCard();
            log.warn("Possible Visa ADS bug: profile is null, using paymentCredentials[0].nameOnCard instead : {}", nameOnCard);
            return new SpxResolveP2PAliasProfileResponse()
                .withAliasProfileName(nameOnCard);
        }
    }

    /**
     * Visa accepts the phone number as the alias formatted according to ITU-T E.164 (2010) number structure.
     * <strong>Important:</strong> In the E.164 format, the "+" sign and leading zero is included, however the API explicitly says it is not.
     *
     * @see <a href="https://en.wikipedia.org/wiki/E.164">E.164</a>
     *
     * @param rawPhoneNumber raw phone Number
     * @return formatted phone number
     */
    @Nonnull
    String formatAsVisaPhoneNumber(@Nonnull final String rawPhoneNumber) {
        final var sanitizedPhoneNumber = sanitizePhoneNumber(rawPhoneNumber);
        return formatAsMoldovanVisaPhoneNumber(sanitizedPhoneNumber)
            .orElse(sanitizedPhoneNumber);
    }

    @Nonnull
    Optional<String> formatAsMoldovanVisaPhoneNumber(@Nonnull final String sanitizedPhoneNumber) {
        final var phoneNumberOptional = PhoneUtils.asMoldovanPhoneNumber(sanitizedPhoneNumber);
        if (phoneNumberOptional.isEmpty()) {
            log.debug("Not a Moldovan phone number : {}", sanitizedPhoneNumber);
            return Optional.empty();
        }
        final var phoneNumber = phoneNumberOptional.get();
        final var e164FormattedPhoneNumber = PhoneUtils.INSTANCE.format(phoneNumber, PhoneNumberUtil.PhoneNumberFormat.E164);

        // For unknown reason, Visa ADS specifies the following:
        //    Please note that in the E.164 format, the "+" sign is not included.
        final var e164FormattedVisaPhoneNumber = e164FormattedPhoneNumber.replaceFirst("^\\+", "");

        log.info("Formatted phone number '{}' -> '{}'", sanitizedPhoneNumber, e164FormattedVisaPhoneNumber);

        return Optional.of(e164FormattedVisaPhoneNumber);
    }

    /**
     * Strips a leading {@code +} character, spaces and a {@code -} characters.
     * @param rawPhoneNumber raw phone number
     * @return phone number
     */
    @Nonnull
    private String sanitizePhoneNumber(@Nonnull final String rawPhoneNumber) {
        return rawPhoneNumber
            .replaceFirst("^\\+", "")
            .replace(" ", "")
            .replace("-", "");
    }

    private void checkAliasConflict(@Nonnull final String userId, @Nonnull final String phoneNumber) {
        final var formattedPhoneNumber = formatAsVisaPhoneNumber(phoneNumber);
        final var aliasResponse = getAliasIdFromValue(formattedPhoneNumber);
        if (aliasResponse.getAliasId() != null) {
            log.error("P2P alias already exists for user '{}' : {}", userId, aliasResponse.getAliasId());
            throw new VbRestException(VbErrorCodeEnum.ALIAS_USER_PHONE_CONFLICT, 400);
        }
    }

    private boolean isVbPreferred(@Nonnull final AliasInquiryItem aliasInquiryItem) {
        return Optional.ofNullable(aliasInquiryItem.getDirectories())
            .orElse(List.of())
            .stream()
            .filter(s -> s.getEntities() != null)
            .flatMap(s -> s.getEntities().stream())
            .filter(s -> s.getPreferredFor() != null)
            .anyMatch(entity -> {
                final boolean entityIdMatches;
                if (BooleanUtils.isTrue(checkEntityIdEnabled)) {
                    entityIdMatches = Objects.equals(checkEntityIdValue, entity.getId());
                } else {
                    log.debug("Skipped checking of entity.id : checkEntityIdEnabled={}", checkEntityIdEnabled);
                    entityIdMatches = true;
                }
                final boolean isPreferredType = entity.getPreferredFor().stream()
                    .anyMatch(preferred -> PaymentCredentialPreferredForWithDateItem.TypeEnum.RECEIVE.equals(preferred.getType()));
                final var result = entityIdMatches && isPreferredType;
                log.debug("Checking '{}' is preferred : entityIdMatches={}, isPreferredType={} : result={}",
                    aliasInquiryItem.getAliasValue(), entityIdMatches, isPreferredType, result);
                return result;
            });
    }
}
