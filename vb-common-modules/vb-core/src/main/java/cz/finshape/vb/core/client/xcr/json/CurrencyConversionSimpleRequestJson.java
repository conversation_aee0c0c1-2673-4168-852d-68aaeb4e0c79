/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;

import java.math.BigDecimal;
import java.util.Map;

import lombok.Data;


/**
 * The currency conversion simple request JSON.
 *
 * <AUTHOR>
 */
@Data
public class CurrencyConversionSimpleRequestJson {

    private String conversionExchangeRateSetId;

    private String fromCurrency;

    /**
     * Amount in given currency. Format of amount property in JSON is {@code decimal19.4}.
     */
    private BigDecimal fromAmount;

    private String toCurrency;

    /**
     * Amount in given currency. Format of amount property in JSON is {@code decimal19.4}.
     */
    private BigDecimal toAmount;

    /**
     * Map of (assigned category Id, assigned category value).
     */
    private Map<String, String> categorization;

    private Object extendedInfo;

    private boolean withConversionDetail = false;
}
