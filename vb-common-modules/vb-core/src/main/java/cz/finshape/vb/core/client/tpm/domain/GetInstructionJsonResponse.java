/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.tpm.domain;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.With;


/**
 * <AUTHOR>
 */
@With
@Data
@AllArgsConstructor
@RequiredArgsConstructor
@SuppressFBWarnings(
    value = { "ES_COMPARING_PARAMETER_STRING_WITH_EQ", "RC_REF_COMPARISON"},
    justification = "SpotBugs is not friendly with @lombok.With")
public class GetInstructionJsonResponse {

    private String id;
    private Integer operId;
    private Integer channelId;
    private String description;
    private String entityTimestamp;
    private String instructionTypeCode;
    private Object lockedTill;

    private Long clientId;
    private String userId;
    private Long signRuleSeq;
    private OffsetDateTime certifiedAt;
    private String accountId;
    private List<Relation> ownership;
    private List<Relation> relations;

    private String statusCode;
    private Status status;
    private Boolean finalStatus;
    private List<Object> transitions;
    private Map<String, Integer> itemStatusCounts;

    private String flowVersion;
    private String instructionTypeVersion;
    private Integer instructionTypeFlowId;
    private Object instructionImportFileId;

    private LocalDateTime createdDate;
    private LocalDateTime createdAt;
    private User createdBy;

    private LocalDateTime lastUpdateDate;
    private OffsetDateTime updatedAt;
    private User updatedBy;
    private String lastUpdateUser;
    private Integer lastUpdateUserId;
    private String lastUpdateSystemId;

    private Map<String, Subtotal> subtotals;
    private Map<String, Object> item;
    private Integer instructionItemCount;

    @Data
    public static class Relation {

        private String entityType;
        private String id;
    }

    @Data
    public static class Status {

        private String code;
        private String text;
    }

    @Data
    public static class Subtotal {

        private BigDecimal amount;
        private Long count;
    }

    @Data
    public static class User {

        private String id;
        private String name;
    }
}
