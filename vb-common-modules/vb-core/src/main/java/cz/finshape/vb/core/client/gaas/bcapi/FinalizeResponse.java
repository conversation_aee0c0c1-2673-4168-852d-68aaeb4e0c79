/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.gaas.bcapi;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;


/**
 * BCAPI finalize response.
 *
 * <AUTHOR>
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FinalizeResponse {

    private List<String> warnings;

    private Map<String, Object> extendedInfo;

    /**
     * Session token.
     * <p>
     * Example: {@code user:3B52AFB65064DD036BFAE65840E539AD}.
     */
    private String sessionToken;

    /**
     * Set-Cookie header value starting with {@code SESSIONID}.
     * <p>
     * <strong>Important:</strong> the value part of the parameter has a different meaning from {@link #sessionToken}.
     * <p>
     * Example: {@code SESSIONID=user:5DF25D16CAF2CAAE43B7EC36B8719CB1}.
     */
    private String sessionIdCookie;
}
