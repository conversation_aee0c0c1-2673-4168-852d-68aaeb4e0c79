/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;


import java.util.Map;

import lombok.Data;


/**
 * The PostExchangeRateLists request JSON.
 *
 * <AUTHOR> (copied from XCR and modified)
 */
@Data
public class PostExchangeRateListsRequestJson {

    /**
     * Map of exchange rate external ID and exchange rates.
     * <p>
     * Example keys: {@code standard}, {@code bank}, {@code central} or any other exchange rate external ID.
     */
    private Map<String, ExchangeRateListJson> lists;
}
