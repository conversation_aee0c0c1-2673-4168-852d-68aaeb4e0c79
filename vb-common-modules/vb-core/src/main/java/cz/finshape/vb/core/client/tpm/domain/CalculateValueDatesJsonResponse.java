/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.tpm.domain;


import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class CalculateValueDatesJsonResponse {

    /**
     * Non null list of valid value dates in Moldovan time (EET zone).
     */
    private List<LocalDate> valueDates = new ArrayList<>();
}
