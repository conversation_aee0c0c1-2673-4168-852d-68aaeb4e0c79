/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.dbos;


import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;


/**
 * DBOS response: Error detail info
 *
 * <AUTHOR>
 */

@Data
@With
@NoArgsConstructor
@AllArgsConstructor
public class DbosErrorDetailInfo {

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String value;

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String suggestion;

    @Override
    public String toString() {
        return "(value=%s, suggestion=%s)".formatted(value, suggestion);
    }
}
