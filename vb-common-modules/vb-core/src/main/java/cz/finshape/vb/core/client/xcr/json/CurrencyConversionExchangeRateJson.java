/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;

import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * JSON with exchange rate used during currency conversion.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CurrencyConversionExchangeRateJson {

    /**
     * Exchange rate value source currency Id.
     */
    private String currencyId;

    /**
     * Exchange rate list reference currency Id.
     */
    private String referenceCurrencyId;

    /**
     * Exchange rate list type Id.
     */
    private String rateListType;

    /**
     * Exchange rate list external Id.
     */
    private String rateListExternalId;

    /**
     * Exchange rate list release Id.
     */
    private String rateListReleaseId;

    /**
     * Flag if exchange rate has inverse meaning, where rate express units of source currency for one unit of reference currency.
     */
    private boolean hasInverseMeaning;

    /**
     * Exchange rate type Id.
     */
    private String rateType;

    /**
     * Exchange rate original value (as stored in exchange rate list).
     */
    private BigDecimal rate;

    /**
     * Exchange rate currency units per one unit of reference currency or other way, depending on {@code hasInverseMeaning} flag.
     */
    private Integer unit;

    /**
     * Test purpose constructor
     * @param rateType rate type
     * @param rate rate
     */
    public CurrencyConversionExchangeRateJson(final String rateType, final BigDecimal rate) {
        this.rateType = rateType;
        this.rate = rate;
    }

}
