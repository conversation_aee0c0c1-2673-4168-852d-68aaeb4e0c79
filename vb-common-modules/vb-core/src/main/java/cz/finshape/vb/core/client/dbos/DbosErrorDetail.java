/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.dbos;


import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;


/**
 * DBOS response: Error detail
 *
 * <AUTHOR>
 */
@Data
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class DbosErrorDetail {

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String code;

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String message;

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String target;

    private DbosErrorDetailInfo info;

    @Override
    public String toString() {
        final var errorString = new StringBuilder(100);
        errorString.append("ERROR code=[%s] : %s".formatted(code, info));
        if (target != null) {
            errorString.append("%s      │      ├─ Target: %s".formatted(System.lineSeparator(), target));
        }
        if (info != null) {
            errorString.append("%s      │      └─ Info:   %s".formatted(System.lineSeparator(), info));
        }
        return errorString.toString();
    }
}
