/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.tpm.domain;


import java.util.List;

import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class FinishSynchronousFlowJsonResponse {

    private List<Instruction> instructions;
    private Summary summary;
    private String asyncOperationId;
    private Instruction instruction;
    private String status;

    @Data
    public static class Instruction {

        private String id;
        private String entityVersion;
        private String statusCode;
        private Boolean finalStatus;
        private Boolean fullyCertified;
    }

    @Data
    public static class Summary {

        private Long countAll;
        private Long countDone;
        private Long countError;
    }
}
