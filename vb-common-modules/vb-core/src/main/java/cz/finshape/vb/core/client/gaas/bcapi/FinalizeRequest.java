/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.gaas.bcapi;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;


/**
 * BCAPI finalize request.
 *
 * <AUTHOR>
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FinalizeRequest {

    private String certReqId;
    private CertData certData;

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CertData {

        @JsonProperty("waitForOnlineCertification") // this field does not come as snake_case
        private int waitForOnlineCertification;
        private String sessionAccessToken;
        private String signature;
    }
}
