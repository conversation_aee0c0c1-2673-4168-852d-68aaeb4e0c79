/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;


import java.util.Arrays;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * Conversion request JSON.
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CurrencyConversionRequestJson {

    private List<CurrencyConversionRequestPackageJson> packages;

    private Object extendedInfo;

    /**
     * Secondary constructor.
     *
     * @param packages packages
     */
    public CurrencyConversionRequestJson(final CurrencyConversionRequestPackageJson... packages) {
        this(Arrays.asList(packages), null);
    }
}
