/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.auth;


import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Callable;

import org.springframework.http.HttpHeaders;

import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;


/**
 * Authorization context.
 * <p>
 * Combination of GAAS authentication token and current Context ID.
 *
 * <AUTHOR> (copy from JNT)
 * <AUTHOR>
 */
@Slf4j
@Data
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@ToString(of = "clientContextId")
public class AuthorizationContext {

    public static final String BEARER_PREFIX = "Bearer ";

    public static final String JTW_PAYLOAD_CHANNEL = "channel_id";
    public static final String JWT_PAYLOAD_SUB = "sub";
    public static final String JWT_PAYLOAD_ACCESS_TOKEN = "access_token";
    public static final String JWT_PAYLOADR_ROLES = "roles";
    public static final String JWT_PAYLOAD_AUTHORIZATION_METHODS = "amr";

    /**
     * Authorization token as a JWT.
     * It is OpenID Connect token (JWT) that is used for authorized API calls as bearer token.
     * See methods that enrich the HTTP headers:
     * {@link #authorizeWithAuthorizationBearerToken(HttpHeaders)} and {@link #authorizeWithAuthorizationAccessBearerToken(HttpHeaders)}.
     * <p>
     * This parameter contains only JWT form: {@code <header>.<payload>.<signature>}.
     * <p>
     * Example: {@code eyJraWQiOiIxIiwiYWxnIjoiUlMyNTYifQ.eyJzdWIiOi<trimmed>.Teg2HGV_tz<trimmed>}
     * <p>
     * Example of the decoded {@code <payload>} data: <pre>
     * {
     *   "sub": "IDNP:User_7500014",
     *   "amr": [
     *     "LN_PWD"
     *   ],
     *   "roles": [
     *     "MCH_CHANNEL_USER"
     *   ],
     *   "iss": "https://vb.dev.bsccloud.net/gaas",
     *   "nonce": "12318541684684515146",
     *   "ui_locale": "en",
     *   "tid": 3566001,
     *   "sid": 346701,
     *   "auth_strength": 20,
     *   "access_token": "983D4CA0429E5692AC06F840F60EB9C942DD9E58C30A7A7DD105DFED63CEB5A9",
     *   "aud": "MCH:GIB",
     *   "auth_time": 1710823239,
     *   "exp": 1710996039,
     *   "iat": 1710823244,
     *   "channel_id": 7,
     *   "jti": "3566001"
     * }
     * </pre>
     */
    private final String authorizationToken;

    /**
     * GEN context id.
     */
    private final String clientContextId;

    /**
     * Parsed JWT claims set.
     */
    private JWTClaimsSet jwtClaimsSet;

    /**
     * Session token.
     * <p>
     * Example: {@code user:3B52AFB65064DD036BFAE65840E539AD}.
     */
    private String sessionToken;

    /**
     * All available client clientContexts.
     */
    private List<String> clientContexts;

    /**
     * GEN external client ID.
     */
    private String crmId;

    /**
     * Username.
     */
    private String username;

    /**
     * Datetime of authorization that defaults to the UTC time of instantiation.
     */
    private LocalDateTime authorizedAt = OffsetDateTime.now(ZoneOffset.UTC).toLocalDateTime();

    // =========================================
    // Static constructors.
    // =========================================

    /**
     * Instantiates authorization context from authorization.
     * The input authorization can have a form of authorization token or authorization bearer token.
     * <p>
     * <ul>
     *     <li><strong>authorization token</strong></li> is {@code <header>.<payload>.<signature>}
     *     <li><strong>authorization bearer token</strong></li> is {@code Bearer <header>.<payload>.<signature>}
     * </ul>
     * Both inputs are acceptable as the static constructor handles trimming
     *
     * @param authorization authorization
     * @param clientContextId client context ID
     * @return authorization context
     */
    public static AuthorizationContext of(final String authorization, final String clientContextId) {
        Objects.requireNonNull(authorization, "Authorization is null");
        return new AuthorizationContext(trimBearerPrefix(authorization), clientContextId);
    }

    // =========================================
    // Overridden getters & setters.
    // =========================================

    /**
     * Getter for {@link #jwtClaimsSet} that parses the {@link #authorizationToken} lazily.
     *
     * @return {@link #jwtClaimsSet}
     */
    public JWTClaimsSet getJwtClaimsSet() {
        if (jwtClaimsSet == null) {
            try {
                final SignedJWT signedJWT = SignedJWT.parse(authorizationToken);
                jwtClaimsSet = signedJWT.getJWTClaimsSet();
            } catch (ParseException e) {
                throw new RuntimeException("Cannot parse source authorization token.");
            }
        }
        return jwtClaimsSet;
    }

    // =========================================
    // Computed getter methods.
    // =========================================

    /**
     * Gets user identity from the access token.
     * <p>
     * Example: {@code IDNP:User_7500014}
     *
     * @return user identity
     */
    public final String getSub() {
        return tryOrThrow(() -> getJwtClaimsSet().getStringClaim(JWT_PAYLOAD_SUB));
    }

    /**
     * Gets access token.
     * <p>
     * Example: {@code 983D4CA0429E5692AC06F840F60EB9C942DD9E58C30A7A7DD105DFED63CEB5A9}
     *
     * @return access token
     */
    public final String getAccessToken() {
        return tryOrThrow(() -> getJwtClaimsSet().getStringClaim(JWT_PAYLOAD_ACCESS_TOKEN));
    }

    /**
     * Gets channel.
     * <p>
     * Example: {@code 7}
     *
     * @return channel
     */
    public final Long getChannel() {
        return tryOrThrow(() -> getJwtClaimsSet().getLongClaim(JTW_PAYLOAD_CHANNEL));
    }

    /**
     * Gets roles.
     * <p>
     * Example: {@code [ MCH_CHANNEL_USER ]}
     *
     * @return roles
     */
    public final List<String> getRoles() {
        return tryOrThrow(() -> getJwtClaimsSet().getStringListClaim(JWT_PAYLOADR_ROLES));
    }

    /**
     * Gets authentication methods.
     * <p>
     * Example: {@code [ LN_PWD ]}
     *
     * @return authentication methods
     */
    public final List<String> getAuthenticationMethods() {
        return tryOrThrow(() -> getJwtClaimsSet().getStringListClaim(JWT_PAYLOAD_AUTHORIZATION_METHODS));
    }

    /**
     * Gets user identity without the IDM prefix from the access token.
     * The value is the rightmost part of the {@link #getSub()} split by {@code :} delimiter.
     * <p>
     * Example: {@code User_7500014}
     * @return user identity without the IDM prefix
     */
    public final Optional<String> getUserIdFromSub() {
        return Arrays.stream(getSub().split(":")).reduce((left, right) -> right);
    }

    /**
     * Feeds the HTTP headers with a bearer authorization token from this authorization context.
     * <p>
     * <ul>
     *  <li>Header key:</li> {@code Authorization}
     *  <li>Header value:</li> {@code Bearer <header>.<payload>.<signature>}
     * </ul>
     *
     * @param httpHeaders HTTP headers to be fed
     */
    public final void authorizeWithAuthorizationBearerToken(final HttpHeaders httpHeaders) {
        httpHeaders.add(HttpHeaders.AUTHORIZATION, BEARER_PREFIX + getAuthorizationToken());
    }

    /**
     * Feeds the HTTP headers with a bearer authorization token from this authorization context.
     * <p>
     * <ul>
     *  <li>Header key:</li> {@code Authorization}
     *  <li>Header value:</li> {@code Bearer <header>.<payload>.<signature>}
     * </ul>
     *
     * @param httpHeaders HTTP headers to be fed
     */
    public final void authorizeWithAuthorizationAccessBearerToken(final HttpHeaders httpHeaders) {
        httpHeaders.add(HttpHeaders.AUTHORIZATION, BEARER_PREFIX + getAccessToken());
    }

    /**
     * Trims the authorization from the Bearer prefix.
     *
     * @param authorization authorization
     * @return authorization token
     */
    private static String trimBearerPrefix(final String authorization) {
        return authorization.replace(BEARER_PREFIX, "");
    }

    /**
     * Tries to return the result of the {@link Callable} and throws a runtime exception in case it fails.
     *
     * @param callable callable to be called
     * @return callable result
     * @param <T> generic type parameter of callable result
     */
    private static <T> T tryOrThrow(final Callable<T> callable) {
        try {
            return callable.call();
        } catch (Exception e) {
            throw new IllegalStateException("Cannot get", e);
        }
    }
}
