/*
 * Copyright (c) 2020 Banking Software Company s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * The currency amount with exchange rate JSON.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CurrencyAmountWithRateJson {

    /**
     * Exchange rate to convert amount in given currency. Format of exchange rate property in JSON is {@code decimal19.6}.
     */
    private BigDecimal rate;

    /**
     * Amount in given currency. Format of amount property in JSON is {@code decimal19.4}.
     */
    private BigDecimal amount;
}
