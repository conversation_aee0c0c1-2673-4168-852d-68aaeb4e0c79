/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.tpm.domain;


import java.util.Map;

import lombok.Data;


/**
 * Instruction item (there are additional fields in the JSON response, but are not needed so far).
 * /integration/instructions/items/{instructionItemId}
 *
 * <AUTHOR> Cernil
 */
@Data
public class InstructionItem {

    private String id;
    private String statusCode;
    private Map<String, Object> jsonData;
}
