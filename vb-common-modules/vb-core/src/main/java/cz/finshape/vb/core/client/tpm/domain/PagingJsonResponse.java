/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.tpm.domain;


import lombok.Data;


/**
 * Common pagination object.
 *
 * <AUTHOR>
 */
@Data
public class PagingJsonResponse {

    private Long offset;
    private Long recordsFiltered;
    private Cursors cursors;

    @Data
    public static class Cursors {

        private String before;
        private String after;
    }
}
