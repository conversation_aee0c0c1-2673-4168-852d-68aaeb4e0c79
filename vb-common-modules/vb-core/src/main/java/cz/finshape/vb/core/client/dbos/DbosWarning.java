/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.dbos;


import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;


/**
 * DBOS response: Warning
 *
 * <AUTHOR>
 */
@Data
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class DbosWarning {

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String code;

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String message;

    @Override
    public String toString() {
        return "WARNING code=[%s] : %s".formatted(code, message);
    }
}
