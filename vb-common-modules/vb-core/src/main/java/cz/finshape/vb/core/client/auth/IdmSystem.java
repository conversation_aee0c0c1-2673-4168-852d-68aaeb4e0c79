/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.auth;


import javax.annotation.Nonnull;

import cz.finshape.vb.domain.internal.VbConstants;
import lombok.extern.slf4j.Slf4j;


/**
 * Utility class for IDM system.
 *
 * <AUTHOR>
 */
@Slf4j
public final class IdmSystem {

    private static final String IDM_SYSTEM_DELIMITER = ":";
    private static final String IDM_SYSTEM_PREFIX = VbConstants.IDM_SYSTEM_NAME + IDM_SYSTEM_DELIMITER;

    private IdmSystem() {
        throw new UnsupportedOperationException("Nope");
    }

    /**
     * Adds an IDM system prefix to the User ID if it is missing.
     * Otherwise, the User ID is returned as is.
     *
     * @param userId user ID with OR without the IDM system prefix
     * @return user ID with the IDM system prefix
     */
    @Nonnull
    public static String withPrefix(@Nonnull final String userId) {
        return userId.startsWith(IDM_SYSTEM_PREFIX) ? userId : IDM_SYSTEM_PREFIX + userId;
    }

    /**
     * Removes an IDM system prefix to the User ID if it is missing.
     * Otherwise, the User ID is returned as is.
     *
     * @param idmSystemUserId user ID with OR without the IDM system prefix
     * @return user ID without the IDM system prefix
     */
    @Nonnull
    public static String withoutPrefix(@Nonnull final String idmSystemUserId) {
        return idmSystemUserId.startsWith(IDM_SYSTEM_PREFIX) ? idmSystemUserId.replace(IDM_SYSTEM_PREFIX, "") : idmSystemUserId;
    }
}
