/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;


/**
 * The currency conversion simple response JSON.
 *
 * <AUTHOR>
 */
@Data
public class CurrencyConversionSimpleResponseJson {

    private String conversionExchangeRateSetId;

    private String fromCurrency;

    /**
     * Amount in given currency. Format of amount property in JSON is {@code decimal19.4}.
     */
    private BigDecimal fromAmount;

    private String toCurrency;

    /**
     * Amount in given currency. Format of amount property in JSON is {@code decimal19.4}.
     */
    private BigDecimal toAmount;

    /**
     * Exchange rate to convert amount in given currency. Format of exchange rate property in JSON is {@code decimal19.6}.
     */
    private BigDecimal rate;

    private Object extendedInfo;

    /**
     * List with conversion details with all used exchange rates, as they are stored in exchange rate list.
     */
    private List<CurrencyConversionExchangeRateJson> detail = null;
}
