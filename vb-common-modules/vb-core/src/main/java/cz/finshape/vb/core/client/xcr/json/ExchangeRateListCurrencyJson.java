/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;


import java.util.List;
import java.util.Map;

import lombok.Data;


/**
 * The single currency exchange rates JSON with rates in specific validity periods.
 *
 * <AUTHOR> (copied from XCR and modified)
 */
@Data
public class ExchangeRateListCurrencyJson {

    private Integer unit;

    /**
     * Map of (exchange rate type ID, list of currency exchange rates in specific validity periods).
     */
    private Map<String, List<CurrencyExchangeRatesWithValidityPeriodJson>> rates;

    private Integer orderNumber;
}
