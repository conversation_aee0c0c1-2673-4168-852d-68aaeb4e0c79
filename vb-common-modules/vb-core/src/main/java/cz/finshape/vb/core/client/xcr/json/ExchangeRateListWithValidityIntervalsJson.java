/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;


import java.util.Map;

import lombok.Data;


/**
 * The single exchange rate list JSON with map with exchange rates for currencies.
 *
 * <AUTHOR> (copied from XCR and modified)
 */
@Data
public class ExchangeRateListWithValidityIntervalsJson {

    private String referenceCurrencyId;

    private boolean hasInverseMeaning;

    private boolean isPublic;

    /**
     * Map of ({@code currencyId}, exchange rates for this currency of specified types and with validity periods).
     */
    private Map<String, ExchangeRateListCurrencyJson> currencies;
}
