/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.dbos;


import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;


/**
 * DBOS response: Error
 * <p>
 * This error that can be a 4XX or 5XX response itself.
 *
 * <AUTHOR>
 */
@Data
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
public class DbosError {

    private String code;

    private String message;

    private DbosErrorInfo info;

    private List<DbosErrorDetail> errors;

    @Override
    @SuppressFBWarnings(value = "VA_FORMAT_STRING_USES_NEWLINE", justification = "SpotBugs is not friendly with multiline strings")
    public String toString() {
        final var errorsString = errors == null ? "" : System.lineSeparator() + IntStream.range(0, errors.size())
            .mapToObj(i -> "      ├─ %d# %s".formatted(i, errors.get(i)))
            .collect(Collectors.joining(System.lineSeparator()));

        return """
            ERROR code=[%s] : %s
            \s ├─ Info: %s
            \s └─ Errors:%s""".formatted(code, message, info, errorsString);
    }
}
