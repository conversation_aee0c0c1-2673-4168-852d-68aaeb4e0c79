/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.dbos;


import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.With;


/**
 * DBOS response that can be a 4XX or 5XX error response itself.
 *
 * <AUTHOR>
 */
@Data
@With
@NoArgsConstructor
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@EqualsAndHashCode(callSuper = true)
public class DbosResponse extends DbosError {

    /**
     * Successful response can have an error.
     */
    private DbosError error;

    /**
     * Successful response can have a warning.
     */
    private DbosWarning warning;

    @Override
    public String toString() {
        return Stream.of(error, warning)
            .filter(Objects::nonNull)
            .map(Object::toString)
            .collect(Collectors.joining(System.lineSeparator()));
    }

    /**
     * The error in the response is found either in a separate field or the response is an error itself, but never both.
     *
     * @return error
     */
    public DbosError getError() {
        return error != null
            ? error
            // A new instance must be returned to prevent StackOverflowError.
            // And it makes sense to construct it only if it makes sense (code != null).
            : this.getCode() == null
                ? null
                : new DbosError()
                    .withCode(this.getCode())
                    .withInfo(this.getInfo())
                    .withMessage(this.getMessage())
                    .withErrors(this.getErrors());
    }
}
