/*
 * Copyright (c) 2020 Banking Software Company s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * The conversion JSON.
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConversionJson {

    /**
     * Map of (source currency Id, source currency detail with requested amount to convert from that currency).
     */
    private Map<String, CurrencyAmountWithRateJson> from;

    /**
     * Map of (target currency Id, target currency detail with requested amount to convert to that currency, and used exchange rate).
     */
    private Map<String, CurrencyAmountWithRateJson> to;

    /**
     * List with conversion details with all used exchange rates, as they are stored in exchange rate list.
     */
    private List<CurrencyConversionExchangeRateJson> detail = null;
}
