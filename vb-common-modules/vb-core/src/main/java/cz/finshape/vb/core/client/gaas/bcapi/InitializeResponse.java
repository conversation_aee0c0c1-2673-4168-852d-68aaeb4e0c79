/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.gaas.bcapi;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;


/**
 * BCAPI initialize response.
 *
 * <AUTHOR>
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InitializeResponse {

    /**
     * Certification request ID.
     * <p>
     * Example: {@code 59D28CFEADD728AB54AEEC49EC0668FEE990B2F430659AED7FDDA34E76AD7854}
     */
    private String certReqId;
    private PublicKey publicKey;
    private String publicKeyId;
    private ExtendedInfo extendedInfo;
    private Integer timeToFinishCert;
    private Integer certReqValidity;

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PublicKey {

        /**
         * Example: {@code AQAB}
         */
        private String e;

        /**
         * Example: {@code RSA}
         */
        private String kty;

        /**
         * Example:
         * {@code
         * kga2cDUTMQwVqG4whFvCpk_LtwkD1E5zRzR_iUHY-jccVuaPLOOBv6OGQW03EexnqmXJ8RW5Jly0mQ68Eke4t7a3yv86Ps6Jbq19NdJyNELrSRqEf0mmScTeapLKotbv
         * clQi-hyrKXWOaiGfMvsSf9irQh6_OsB_U4jX3r9SDS45TniRE6joau0XpsphmrFqRc8CaAndGwGk4yp7-bULP_qfMzz8wVi7V-Obg45-elQSKtZFDENZIX2nM7oWQ_0p
         * SoNeF_Jnj5WzoSPm5XwA4rlLvGFb_EMYKLxQ39Qbhd86FraW9a8JWzJUxrvYrw451XbCrABejOmPW2Zo-iKmVc1rIO-IsCQucsIcIL0z6qo3ui-BADluHbfSfBpK2-zu
         * l4F90YwDaydlEp_fYhUQH6enLAtqr-CfiuvKhLPXZLZf3eyqQGk3JolZQnJPgXtsFMt5Uhn5KaGJdR1XgdNDhGBHBrEsfG4jXvlNbKAgQjsTG96-Xr7xnHEQKCf6TN4A
         * Fhb_MGZ9UcfiAM305DQu8dt3-yhlXfai1puHhPNw_prok6Q6dBzRqVWfbVv8tvJm520MewApQzPwU24aSLo7QVBjIUxaNTHVBXU0r797GOhbs4hWT5LypOKNLBhovPFj
         * xk-NoKz5_L-WyYbEZHEowkCRcruuU2xKApFrtEFEcs8
         * }
         */
        private String n;
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ExtendedInfo {
        private Boolean biometricSignatureAllowed;
        private String signatureType;
        private String nonce;
        @JsonProperty("QR_CODE")
        private String qrCode;
    }
}
