/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.gen.domain;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * Wrapper of individual responses of entities replication.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ReplicationEntitiesResponse {

    private String code;
    private Info info;
    private List<Error> errors;

    @Data
    public static class Info {

        private String serverName;
        private String nodeName;
        private String traceId;
    }

    @Data
    public static class Error {

        private String code;
        private ErrorInfo info;
        private String target;

    }

    @Data
    public static class ErrorInfo {

        private String value;
    }
}
