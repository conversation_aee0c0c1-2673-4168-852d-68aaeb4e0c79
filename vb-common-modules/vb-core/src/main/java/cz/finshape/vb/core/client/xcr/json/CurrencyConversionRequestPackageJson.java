/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;


import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.springframework.lang.NonNull;

import cz.finshape.vb.domain.internal.xcr.MarketType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * XCR currency conversion request object.
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CurrencyConversionRequestPackageJson {

    private String id;

    /**
     * One of {@link cz.finshape.vb.domain.internal.xcr.MarketType}
     */
    private String conversionExchangeRateSetId;

    private List<ConversionRequestJson> conversions;

    /**
     * Map of (assigned category Id, assigned category value).
     */
    private Map<String, String> categorization;

    private Object extendedInfo;

    private boolean withConversionDetail = false;

    /**
     * Creates a conversion request for a {@link MarketType#STANDARD} market.
     *
     * @param fromAmount from amount
     * @param fromCcy from currency
     * @param toCcy to currency
     * @return standard conversion request
     */
    public static CurrencyConversionRequestPackageJson ofStandard(@NonNull final BigDecimal fromAmount,
                                                                  @NonNull final String fromCcy,
                                                                  @NonNull final String toCcy) {

        return of(MarketType.STANDARD.getId(), fromAmount, fromCcy, toCcy);
    }

    /**
     * Creates a conversion request.
     *
     * @param marketType market type
     * @param fromAmount from amount
     * @param fromCcy from currency
     * @param toCcy to currency
     * @return standard conversion request
     */
    private static CurrencyConversionRequestPackageJson of(
        @NonNull final String marketType,
        @NonNull final BigDecimal fromAmount,
        @NonNull final String fromCcy,
        @NonNull final String toCcy) {

        log.trace("of(marketType={}, fromAmount={}, fromCcy={}, toCcy={})", marketType, fromAmount, fromCcy, toCcy);

        final CurrencyConversionRequestPackageJson result = new CurrencyConversionRequestPackageJson();
        result.setConversionExchangeRateSetId(marketType);
        result.setConversions(List.of(new ConversionRequestJson(
            Map.of(fromCcy, new CurrencyAmountJson(fromAmount)),
            Map.of(toCcy, new CurrencyAmountJson()),
            null)
        ));
        return result;
    }

    @Override
    public String toString() {
        return "set: " + conversionExchangeRateSetId + ", extendedInfo: " + extendedInfo + ", conversions: " +  conversions;
    }
}
