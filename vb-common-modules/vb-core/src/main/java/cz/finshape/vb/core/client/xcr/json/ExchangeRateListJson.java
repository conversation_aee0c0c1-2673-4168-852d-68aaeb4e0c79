/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;


import java.time.OffsetDateTime;
import java.util.Map;

import lombok.Data;


/**
 * The exchange rate list JSON.
 *
 * <AUTHOR> (copied from XCR and modified)
 */
@Data
public class ExchangeRateListJson {

    private OffsetDateTime validFrom;

    private OffsetDateTime expectedValidTo;

    private String releaseId;

    /**
     * Map of currency codes and currency rates.
     * <p>
     * Example keys: {@code EUR}, {@code USD}, {@code GBP} or any other currency code.
     */
    private Map<String, CurrencyRatesJson> currencies;
}
