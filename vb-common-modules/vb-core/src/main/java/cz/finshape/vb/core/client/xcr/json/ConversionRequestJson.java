/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;


import java.math.BigDecimal;
import java.util.Map;

import org.apache.commons.collections.MapUtils;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * XCR currency conversion request object.
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConversionRequestJson {

    /**
     * Map of (source currency Id, source currency detail with requested amount to convert from that currency).
     */
    @JsonProperty("from")
    private Map<String, CurrencyAmountJson> from;

    /**
     * Map of (target currency Id, target currency detail with requested amount to convert to that currency, and used exchange rate).
     */
    @JsonProperty("to")
    private Map<String, CurrencyAmountJson> to;

    /**
     * Extended information - custom rate.
     */
    @JsonProperty("extendedInfo")
    private CustomRateJson customRate;

    /**
     * Conversion object 100 EUR -> ? MDL
     *
     * @param fromAmount the amount to convert from
     * @param fromCurrency the source currency
     * @param toCurrency the target currency
     * @param customRate the custom rate
     * @return a ConversionRequestJson object with the specified amount, source currency, and target currency
     */
    public static ConversionRequestJson of(BigDecimal fromAmount, String fromCurrency, String toCurrency, CustomRateJson customRate) {
        return new ConversionRequestJson(
            Map.of(fromCurrency, new CurrencyAmountJson(fromAmount)),
            Map.of(toCurrency, new CurrencyAmountJson()),
            customRate
        );
    }

    /**
     * Conversion object 100 EUR -> ? MDL
     *
     * @param fromAmount the amount to convert from
     * @param fromCurrency the source currency
     * @param toCurrency the target currency
     * @return a ConversionRequestJson object with the specified amount, source currency, and target currency
     */
    public static ConversionRequestJson of(BigDecimal fromAmount, String fromCurrency, String toCurrency) {
        return new ConversionRequestJson(
            Map.of(fromCurrency, new CurrencyAmountJson(fromAmount)),
            Map.of(toCurrency, new CurrencyAmountJson()),
            null
        );
    }

    /**
     * Conversion object ? EUR -> 100 MDL
     *
     * @param fromCurrency the source currency
     * @param toAmount the amount toCurrency convert fromCurrency
     * @param toCurrency the target currency
     * @param customRate the custom rate
     * @return a ConversionRequestJson object with the specified source currency, amount to convert, and target currency
     */
    public static ConversionRequestJson of(String fromCurrency, BigDecimal toAmount, String toCurrency, CustomRateJson customRate) {
        return new ConversionRequestJson(
            Map.of(fromCurrency, new CurrencyAmountJson()),
            Map.of(toCurrency, new CurrencyAmountJson(toAmount)),
            customRate
        );
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder();
        if (MapUtils.isNotEmpty(from)) {
            sb.append("from: ");
            from.forEach((k, v) -> sb.append(k).append(" -> ").append(v.getAmount()).append(", "));
        }

        if (MapUtils.isNotEmpty(to)) {
            sb.append("to: ");
            to.forEach((k, v) -> sb.append(k).append(" -> ").append(v.getAmount()).append(", "));
        }

        return sb.toString();
    }
}
