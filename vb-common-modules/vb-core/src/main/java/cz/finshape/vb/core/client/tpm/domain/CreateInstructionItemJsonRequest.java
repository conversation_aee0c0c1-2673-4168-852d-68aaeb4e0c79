/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.tpm.domain;


import java.util.List;

import cz.finshape.vb.domain.internal.tpm.TpmEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
public class CreateInstructionItemJsonRequest<T extends TpmEntity> {

    private String instructionTypeCode;
    private String description;
    private String statusCode;
    private List<Relation> relations;
    private T data;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Relation {

        private String entityType;
        private String id;
    }
}
