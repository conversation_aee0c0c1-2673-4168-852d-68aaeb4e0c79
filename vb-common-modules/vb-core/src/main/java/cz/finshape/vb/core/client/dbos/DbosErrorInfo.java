/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.dbos;


import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.With;


/**
 * DBOS response: Error info
 *
 * <AUTHOR>
 */
@Data
@With
@NoArgsConstructor
@AllArgsConstructor
public class DbosErrorInfo {

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String serverName;

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String nodeName;

    @SuppressFBWarnings(value = "ES_COMPARING_PARAMETER_STRING_WITH_EQ", justification = "SpotBugs is not friendly with @lombok.With")
    private String traceId;

    @Override
    public String toString() {
        return "(serverName=%s, nodeName=%s, traceId=%s)".formatted(serverName, nodeName, traceId);
    }
}
