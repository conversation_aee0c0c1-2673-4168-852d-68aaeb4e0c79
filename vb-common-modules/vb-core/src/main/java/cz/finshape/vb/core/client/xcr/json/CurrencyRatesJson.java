/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.xcr.json;


import java.math.BigDecimal;
import java.util.Map;

import lombok.Data;


/**
 * The exchange rates for individual currency JSON.
 *
 * <AUTHOR> (copied from XCR and modified)
 */
@Data
public class CurrencyRatesJson {

    /**
     * Map of exchange type and particular currency rates.
     * <p>
     * Example keys: {@code BUY}, {@code BUY_CASH}, {@code SELL}, {@code SELL_CASH}, {@code MID} or any other exchange type.
     */
    private Map<String, BigDecimal> rates;

    private int unit;
}
