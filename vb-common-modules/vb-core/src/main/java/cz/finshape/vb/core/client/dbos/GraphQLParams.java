/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.core.client.dbos;

import java.util.Collection;

import cz.finshape.vb.core.client.gds.domain.GdsAuthorizationContext;
import lombok.Builder;
import lombok.Data;

/**
 * DBOS GraphQL attributes for querying entities.
 * It is strongly recommended user {@code filter} with {@code filterParams} to avoid problem with escaping and so on.
 * <p>
 * E.g.: {@code filter="name.eq({0}) and id.eq({1})"} and {@code filterParams=new String[]{"Piotr \"Peter\" Rasputin", "100"}}
 *
 * <AUTHOR> (copy from JNT)
 */
@Data
@Builder(builderClassName = "GraphQLParamsBuilder")
public class GraphQLParams {

    /**
     * Filtering condition (i.e. '(currency.code.eq(CZK) or currency.code.eq(EUR)) and amount.lt(500)').
     */
    private String filter;

    /**
     * Values for filter param placeholders (if needed).
     */
    private String[] filterParams;

    /**
     * Fields to return (i.e. 'number,currency,accountbalances').
     */
    private String[] fields;

    /**
     * Sort condition (i.e 'name.asc(),valueDate.desc()')
     */
    private String order;

    /**
     * Limit results.
     */
    private Integer limit;

    /**
     * Offset results.
     */
    private Integer offset;

    /**
     * Aggregate function to apply
     */
    private String aggregate;

    /**
     * Authorization context - used for the INTEGRATION API only.
     */
    private GdsAuthorizationContext authorizationContext;

    /**
     * Accepted-language header.
     */
    private String language;

    /**
     * Custom builder template to override the generated one.
     *
     * <AUTHOR> Charalambidis
     */
    public static class GraphQLParamsBuilder {

        /**
         * Builder with a filter.
         *
         * @param filterParams Filter
         * @return Enriched builder
         */
        public GraphQLParamsBuilder filterParams(String... filterParams) {
            this.filterParams = filterParams;
            return this;
        }

        /**
         * Builder with a filter.
         *
         * @param filterParams Filter
         * @return Enriched builder
         */
        public GraphQLParamsBuilder filterParams(Collection<String> filterParams) {
            this.filterParams = filterParams.toArray(new String[0]);
            return this;
        }

        /**
         * Builder with fields.
         *
         * @param fields fields to be returned (the remaining ones become null); if none are requested, all DEFAULT fields will be returned
         * @return Enriched builder
         */
        public GraphQLParamsBuilder fields(String... fields) {
            this.fields = fields;
            return this;
        }

        /**
         * Builder with fields.
         *
         * @param fields fields to be returned (the remaining ones become null); if none are requested, all DEFAULT fields will be returned
         * @return Enriched builder
         */
        public GraphQLParamsBuilder fields(Collection<String> fields) {
            this.fields = fields.toArray(String[]::new);
            return this;
        }

        /**
         * Builder with filter.
         *
         * @param aggregate aggregate function to apply (e.g. 'amount.sum(),currency.code.count()')
         * @return Enriched builder
         */
        public GraphQLParamsBuilder aggregate(String aggregate) {
            this.aggregate = aggregate;
            return this;
        }

        /**
         * Builder with fields: Unlimited fetching.
         *
         * @return Enriched builder
         */
        public GraphQLParamsBuilder limitless() {
            this.limit = Integer.MAX_VALUE;
            return this;
        }

        /**
         * Builder with fields: Single-item fetching.
         *
         * @return Enriched builder
         */
        public GraphQLParamsBuilder firstOnly() {
            this.limit = 1;
            return this;
        }
    }
}
