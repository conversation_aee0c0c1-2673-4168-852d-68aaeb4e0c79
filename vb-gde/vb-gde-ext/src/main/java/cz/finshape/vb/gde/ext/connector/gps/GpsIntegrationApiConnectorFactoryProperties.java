/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.gps;


import org.springframework.boot.context.properties.ConfigurationProperties;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryProperties;
import cz.finshape.vb.gde.ext.connector.VbGdeConnectorAutoConfiguration;
import lombok.Getter;


/**
 * GPS Integration API connector factory properties.
 *
 * <AUTHOR>
 */
@Getter
@ConfigurationProperties(GpsIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX)
class GpsIntegrationApiConnectorFactoryProperties extends AbstractConnectorFactoryProperties {

    static final String INTEGRATION_API_PREFIX = VbGdeConnectorAutoConfiguration.PREFIX + ".gps.integration-api";

    /**
     * Primary constructor.
     */
    GpsIntegrationApiConnectorFactoryProperties() {
        super(INTEGRATION_API_PREFIX);
    }
}
