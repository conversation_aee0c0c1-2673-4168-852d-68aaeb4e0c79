/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientResponseException;

import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.gen.GenIntegrationApiConnector;
import cz.finshape.vb.core.client.auth.IdmSystem;
import cz.finshape.vb.core.client.gde.domain.DbosReplicationResult;
import cz.finshape.vb.core.client.gde.domain.RelatedUserClients;
import cz.finshape.vb.core.client.gds.domain.GdsReplicationResult;
import cz.finshape.vb.core.client.gen.domain.GenClientUserAssignmentCreateRequest;
import cz.finshape.vb.core.client.gen.domain.GenClientsResponse;
import cz.finshape.vb.core.client.gen.domain.GenExternalUserId;
import cz.finshape.vb.core.client.gen.domain.GenReplicationError;
import cz.finshape.vb.core.client.gen.domain.GenReplicationResult;
import cz.finshape.vb.core.client.gen.domain.GenUserChannelProfileCreateRequest;
import cz.finshape.vb.core.client.gen.domain.GenUserRightProfileAssignmentCreateOrReplaceRequest;
import cz.finshape.vb.core.client.gen.domain.ReferenceReplicationRequest;
import cz.finshape.vb.core.client.gen.domain.ReplicationProduct;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.gds.GdsEntity;
import cz.finshape.vb.gde.ext.mapper.GenProductReplicationMapper;
import lombok.extern.slf4j.Slf4j;


/**
 * Replication service implementation of the entities and their references into GDS and GEN.
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@Service
public class DbosReplicationServiceImpl implements DbosReplicationService {

    private final GenIntegrationApiConnector genIntegrationApiConnector;

    private final GdsIntegrationApiConnector gdsIntegrationApiConnector;

    private final GenProductReplicationMapper genProductReplicationMapper;

    /**
     * Primary constructor.
     *
     * @param genIntegrationApiConnector GEN Integration API connector
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param genProductReplicationMapper GEN product replication mapper
     */
    @Autowired
    public DbosReplicationServiceImpl(final GenIntegrationApiConnector genIntegrationApiConnector,
                                      final GdsIntegrationApiConnector gdsIntegrationApiConnector,
                                      final GenProductReplicationMapper genProductReplicationMapper) {

        this.genIntegrationApiConnector = genIntegrationApiConnector;
        this.gdsIntegrationApiConnector = gdsIntegrationApiConnector;
        this.genProductReplicationMapper = genProductReplicationMapper;
    }

    @Nonnull
    @Override
    public DbosReplicationResult archiveAccounts(@Nonnull final String externalClientId,
                                                 @Nonnull final List<Account> accounts,
                                                 boolean allowPartialReplication) {

        log.debug("archiveAccounts(externalClientId={}, accounts={})", externalClientId, accounts.size());
        if (CollectionUtils.isEmpty(accounts)) {
            return new DbosReplicationResult();
        }

        // GDS replication: Delete products.
        final var gdsReplicationResult = gdsIntegrationApiConnector.deleteEntities(Account.EN_PLURAL, accounts);

        // GEN replication: Select only successfully replicated accounts to GDS
        final var replicatedAccountIds = gdsReplicationResult.successfullyCreatedOrUpdatedEntityUniqueIdentifications(Account.FN_BISACCOUNTID);
        final var accountsForGenReplication = accounts.stream()
            .filter(account -> replicatedAccountIds.contains(account.getBisAccountId()))
            .toList();

        // GEN replication: Delete products.
        final var toDelete = genProductReplicationMapper.mapProducts(accountsForGenReplication)
            .stream()
            .map(ReplicationProduct::getExternalProductId)
            .toList();

        final var genReplicationResult = genIntegrationApiConnector
            .productsDelete(externalClientId, toDelete)
            .processErrors("GEN replication - delete products", allowPartialReplication);

        log.info("Client(externalClientId={}) accounts were deleted from GDS and GEN", externalClientId);
        return new DbosReplicationResult(gdsReplicationResult, genReplicationResult);
    }

    @Nonnull
    @Override
    public DbosReplicationResult replicateAccounts(@Nonnull final String externalClientId,
                                                   @Nonnull final List<Account> accounts,
                                                   boolean allowPartialReplication) {

        log.debug("replicateAccounts(externalClientId={}, accounts.size={})", externalClientId, accounts.size());
        if (CollectionUtils.isEmpty(accounts)) {
            return new DbosReplicationResult();
        }

        // GDS replication: Create or replace products.
        final var gdsReplicationResult = gdsIntegrationApiConnector.createReplaceEntities(Account.EN_PLURAL, accounts, false);

        // GEN replication: Select only successfully replicated accounts to GDS
        final var replicatedAccountIds = gdsReplicationResult.successfullyCreatedOrUpdatedEntityUniqueIdentifications(Account.FN_BISACCOUNTID);
        final var accountsForGenReplication = accounts.stream()
            .filter(account -> replicatedAccountIds.contains(account.getBisAccountId()))
            .toList();

        // GEN replication: Map accounts to replication products.
        final var products = genProductReplicationMapper.mapProducts(accountsForGenReplication);

        // GEN replication: Create or replace products.
        final var genReplicationResult = replicateProducts(externalClientId, allowPartialReplication, products);

        return new DbosReplicationResult(gdsReplicationResult, genReplicationResult);
    }

    @Nonnull
    @Override
    public DbosReplicationResult archiveCards(@Nonnull final String externalClientId,
                                              @Nonnull final List<Card> cards,
                                              final boolean allowPartialReplication) {

        log.debug("archiveCards(externalClientId={}, cards.size={})", externalClientId, cards.size());
        if (CollectionUtils.isEmpty(cards)) {
            return new DbosReplicationResult();
        }

        // GDS replication: Delete products.
        final var gdsReplicationResult = gdsIntegrationApiConnector.deleteEntities(Card.EN_PLURAL, cards);

        // GEN replication: Select only successfully replicated cards to GDS
        final var replicatedCardIds = gdsReplicationResult.successfullyCreatedOrUpdatedEntityUniqueIdentifications(Card.FN_ID);
        final var cardsForGenReplication = cards.stream()
            .filter(card -> replicatedCardIds.contains(card.getId()))
            .toList();

        // GEN replication: Map cards to replication references.
        final var deletedEntities = genProductReplicationMapper.mapReferencesToDeleted(cardsForGenReplication);
        final var metadata = genProductReplicationMapper.mapReferencesToMetadata(Card.class);

        // GEN replication: Delete references.
        final var genReplicationResult = replicateReferences(externalClientId, allowPartialReplication, null, deletedEntities, metadata);

        return new DbosReplicationResult(gdsReplicationResult, genReplicationResult);
    }

    @Nonnull
    @Override
    public DbosReplicationResult replicateCards(@Nonnull final String externalClientId,
                                                @Nullable final RelatedUserClients relatedUserClients,
                                                @Nonnull final List<Card> cards,
                                                final boolean allowPartialReplication) {

        log.debug("replicateCards(externalClientId={}, cards.size={})", externalClientId, cards.size());
        if (CollectionUtils.isEmpty(cards)) {
            return new DbosReplicationResult();
        }

        if (relatedUserClients != null) {
            Assert.notNull(relatedUserClients.getUserId(), "User ID must not be null");
            Assert.notNull(relatedUserClients.getRelatedExternalClientIds(), "Related external client IDs must not be null");
            log.info("Checking cards' owners and creating user-client relation if necessary");
            for (Card card : cards) {
                final var ownerId = card.getOwner().getId();
                if (!relatedUserClients.getRelatedExternalClientIds().contains(ownerId)) {
                    log.info("Card '{}' has an owner '{}' which is not on the list of related external client IDs", card.getId(), ownerId);
                    createUserClientRelation(relatedUserClients.getUserId(), ownerId);
                }
            }
        }

        // GDS replication: Create or replace references.
        final var gdsReplicationResult = gdsIntegrationApiConnector.createReplaceEntities(Card.EN_PLURAL, cards,false);

        // GEN replication: Select only successfully replicated cards to GDS
        final var replicatedCardIds = gdsReplicationResult.successfullyCreatedOrUpdatedEntityUniqueIdentifications(Card.FN_ID);
        final var cardsForGenReplication = cards.stream()
            .filter(card -> replicatedCardIds.contains(card.getId()))
            .toList();

        // GEN replication: Map cards to replication references.
        final var entitiesData = genProductReplicationMapper.mapReferencesToCreateOrUpdate(cardsForGenReplication);
        final var metadata = genProductReplicationMapper.mapReferencesToMetadata(Card.class);

        // GEN replication: Create or replace references.
        final var genReplicationResult = replicateReferences(externalClientId, allowPartialReplication, entitiesData, null, metadata);

        return new DbosReplicationResult(gdsReplicationResult, genReplicationResult);
    }

    /**
     * Creates user-client relation.
     *
     * @param userId user ID
     * @param externalClientId external client ID
     */
    private void createUserClientRelation(@Nonnull final String userId, @Nonnull final String externalClientId) {
        log.info("Creating user-client relation for client '{}'", externalClientId);
        final var genClients = genIntegrationApiConnector.getClients(externalClientId);
        final var externalClientIds = genClients.getResults().stream()
            .map(GenClientsResponse.Result::getExternalClientId)
            .collect(Collectors.toSet());
        if (!externalClientIds.contains(externalClientId)) {
            log.error("Client '{}' not found in GEN, cannot create user-client relation", externalClientId);
            return;
        }

        final var clientUserAssignmentsCreateRequest = new GenClientUserAssignmentCreateRequest()
            .withOriginator(IdmSystem.withPrefix(userId))
            .withClientId(externalClientId)
            .withClientUserAssignment(new GenClientUserAssignmentCreateRequest.ClientUserAssignment()
                .withUserId(new GenExternalUserId(userId))
                .withStatusId(1));
        try {
            genIntegrationApiConnector.clientUserAssignmentsCreate(clientUserAssignmentsCreateRequest);
        } catch (final RestClientResponseException e) {
            if (e.getStatusCode().is4xxClientError()) {
                final var genReplicationError = e.getResponseBodyAs(GenReplicationError.class);
                if (genReplicationError != null &&  "resource.already.exists".equals(genReplicationError.getCode())) {
                    log.error("User-client relation already exists, skipping creation : userId={}, externalClientId={}",
                        userId, externalClientId);
                } else {
                    log.error("Failed to create user-client relation : userId={}, externalClientId={}", userId, externalClientId, e);
                }
            }
            return;
        }
        log.debug("Step 1 - GEN: Create User-Client Relation (DONE)");

        final var userChannelProfileCreateRequest = new GenUserChannelProfileCreateRequest()
            .withOriginator(IdmSystem.withPrefix(userId))
            .withClientId(externalClientId)
            .withUserId(new GenExternalUserId(userId))
            .withUserChannelProfiles(List.of(
                new GenUserChannelProfileCreateRequest.UserChannelProfile()
                    .withChannelId(VbConstants.GenChannels.IBS)
                    .withStatusId(1)
                    .withAuthenticationMethodIds(List.of(VbConstants.GenAuthenticationMethods.LN_MTOKEN))
                    .withCertificationMethodIds(List.of(VbConstants.GenCertificationMethods.MTOKEN)),
                new GenUserChannelProfileCreateRequest.UserChannelProfile()
                    .withChannelId(VbConstants.GenChannels.SPB)
                    .withStatusId(1)
                    .withAuthenticationMethodIds(List.of(VbConstants.GenAuthenticationMethods.MTOKEN))
                    .withCertificationMethodIds(List.of(VbConstants.GenCertificationMethods.MTOKEN))));
        try {
            genIntegrationApiConnector.userChannelProfilesCreate(userChannelProfileCreateRequest);
        } catch (RestClientResponseException e) {
            log.error("Failed to create user channel profile : userId={}, externalClientId={}", userId, externalClientId, e);
            return;
        }
        log.debug("Step 2 - GEN: Create User Channel Profile (DONE)");

        final var userRightProfileAssignmentsCreateOrReplaceRequest = new GenUserRightProfileAssignmentCreateOrReplaceRequest()
            .withOriginator(IdmSystem.withPrefix(userId))
            .withClientId(externalClientId)
            .withUserId(new GenExternalUserId(userId))
            .withUserRightProfileAssignments(new GenUserRightProfileAssignmentCreateOrReplaceRequest.ClientUserAssignment()
                .withRightProfileNames(List.of(
                    new GenUserRightProfileAssignmentCreateOrReplaceRequest.RightProfileName("card-holder-no-rights", "global"))));
        try {
            genIntegrationApiConnector.userRightProfileAssignmentsCreateOrReplace(userRightProfileAssignmentsCreateOrReplaceRequest);
        } catch (RestClientResponseException e) {
            log.error("Failed to create user right profile assignment : userId={}, externalClientId={}", userId, externalClientId, e);
            return;
        }
        log.debug("Step 3 - GEN: Create User Right Profile Assignment (DONE)");
    }

    @Nonnull
    @Override
    public <T extends GdsEntity> GdsReplicationResult replicateEntity(@Nonnull final String plural, @Nonnull final T entity) {
        return this.replicateEntities(plural, List.of(entity));
    }

    @Nonnull
    @Override
    public <T extends GdsEntity> GdsReplicationResult replicateEntities(@Nonnull final String plural, @Nonnull final List<T> entities) {
        log.debug("replicateEntities(plural={}, size={})", plural, entities.size());

        if (entities.isEmpty()) {
            log.warn("No GDS entities were passed for replication");
            return new GdsReplicationResult();
        }

        return gdsIntegrationApiConnector.createReplaceEntities(plural, entities, true);
    }

    /**
     * Replicates entity products.
     *
     * @param externalClientId GDS external client identifier
     * @param allowPartialReplication flag whether partial replication is allowed
     * @param products list of products to be created/updated
     * @return list of errors
     */
    @Nonnull
    private GenReplicationResult replicateProducts(@Nonnull final String externalClientId,
                                                   final boolean allowPartialReplication,
                                                   @Nonnull final List<ReplicationProduct> products) {

        log.debug("replicateProducts(products.size={})", products.size());
        if (products.isEmpty()) {
            log.warn("No products to replicate");
            return GenReplicationResult.empty();
        }

        // GEN replication: Create or replace entities.
        final GenReplicationResult createResult = genIntegrationApiConnector.productsCreateOrReplace(externalClientId, products);

        // GEN replication: Assign to client.
        final var productAssignmentList = genProductReplicationMapper.mapProductsToProductAssignmentList(products);
        final GenReplicationResult assignResult = genIntegrationApiConnector.productsAssignToClient(externalClientId, productAssignmentList);

        return evaluateResultSummaries(externalClientId, allowPartialReplication, createResult, assignResult);
    }

    /**
     * Replicates entity references.
     *
     * @param externalClientId GDS external client identifier
     * @param allowPartialReplication flag whether partial replication is allowed
     * @param createdOrUpdatedEntitiesData list of references to be created/updated
     * @param deletedEntities list of references to be deleted
     * @param metadata metadata
     * @return list of errors
     */
    @Nonnull
    private GenReplicationResult replicateReferences(
            @Nonnull final String externalClientId,
            boolean allowPartialReplication,
            @Nullable final List<ReferenceReplicationRequest.EntityData> createdOrUpdatedEntitiesData,
            @Nullable final List<ReferenceReplicationRequest.EntityIdentification> deletedEntities,
            @Nonnull final Map<String, ReferenceReplicationRequest.EntityTypeMetadata> metadata) {

        log.debug("replicateReferences(createdOrUpdatedEntitiesData.size={}, deletedEntities.size={})",
            createdOrUpdatedEntitiesData == null ? "null" : createdOrUpdatedEntitiesData.size(),
            deletedEntities == null ? "null" : deletedEntities.size());
        if (CollectionUtils.isEmpty(createdOrUpdatedEntitiesData) && CollectionUtils.isEmpty(deletedEntities)) {
            log.warn("No references to replicate");
            return GenReplicationResult.empty();
        }

        log.debug("replicateReferences(externalClientId={}, allowPartialReplication={})", externalClientId, allowPartialReplication);
        // GEN replication: Create or replace references.
        final GenReplicationResult createResult = genIntegrationApiConnector.entitiesReplicate(
            createdOrUpdatedEntitiesData, deletedEntities, metadata);

        // GEN replication: Assign to client.
        final List<ReferenceReplicationRequest.EntityIdentification> entityIdentification = Stream.concat(
                Optional.ofNullable(createdOrUpdatedEntitiesData)
                    .stream()
                    .flatMap(Collection::stream)
                    .map(ReferenceReplicationRequest.EntityData::getChildEntity),
                Optional.ofNullable(deletedEntities).stream()
                    .flatMap(Collection::stream))
            .toList();

        // This is just for code analysis till finished
        if (entityIdentification.isEmpty()) {
            log.debug("No entities replicated");
        }

        return evaluateResultSummaries(externalClientId, allowPartialReplication, createResult);
    }

    /**
     * Evaluates a bunch of {@link GenReplicationResult}.
     * The result is either a list of all errors or short-circuited thrown exception with the relevant error that caused the fail.
     *
     * @param externalClientId GDS external client identifier
     * @param allowPartialReplication flag whether partial replication is allowed
     * @param genReplicationResults results to be evaluated
     * @return list of errors
     */
    @Nonnull
    private GenReplicationResult evaluateResultSummaries(@Nonnull final String externalClientId,
                                                        boolean allowPartialReplication,
                                                        @Nonnull final GenReplicationResult... genReplicationResults) {

        final var mergedResults = Arrays.stream(genReplicationResults)
            .reduce(GenReplicationResult::merge)
            .map(result -> result.processErrors("GEN replication - create/assign products", allowPartialReplication))
            .orElseThrow();

        log.info("Client(externalClientId={}) entities were replicated to GEN", externalClientId);
        return mergedResults;
    }
}
