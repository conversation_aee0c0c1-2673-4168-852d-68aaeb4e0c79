/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import java.util.List;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import cz.finshape.vb.core.client.gde.domain.DbosReplicationResult;
import cz.finshape.vb.core.client.gde.domain.RelatedUserClients;
import cz.finshape.vb.core.client.gds.domain.GdsReplicationResult;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.internal.gds.GdsEntity;


/**
 * Replication service of the entities and their references into GDS and GEN.
 *
 * <AUTHOR>
 */
public interface DbosReplicationService {

    /**
     * Replicates accounts.
     *
     * @param externalClientId GDS external client identifier
     * @param accounts GDS accounts for replication
     * @param allowPartialReplication flag whether partial replication is allowed
     * @return replication result
     */
    @Nonnull
    DbosReplicationResult replicateAccounts(@Nonnull String externalClientId,
                                            @Nonnull List<Account> accounts,
                                            boolean allowPartialReplication);

    /**
     * Archives accounts.
     *
     * @param externalClientId GDS external client identifier
     * @param accounts GDS accounts for replication
     * @param allowPartialReplication flag whether partial replication is allowed
     * @return replication result
     */
    @Nonnull
    DbosReplicationResult archiveAccounts(@Nonnull String externalClientId,
                                          @Nonnull List<Account> accounts,
                                          boolean allowPartialReplication);

    /**
     * Replicates cards.
     *
     * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Cards+-+replication">Confluence</a>
     * @param externalClientId GDS external client identifier
     * @param relatedUserClients related external client IDs the user has access to
     * @param cards GDS cards for replication
     * @param allowPartialReplication flag whether partial replication is allowed
     * @return replication result
     */
    @Nonnull
    DbosReplicationResult replicateCards(@Nonnull String externalClientId,
                                         @Nullable RelatedUserClients relatedUserClients,
                                         @Nonnull List<Card> cards,
                                         boolean allowPartialReplication);

    /**
     * Archives cards.
     *
     * @param externalClientId GDS external client identifier
     * @param cards GDS cards for replication
     * @param allowPartialReplication flag whether partial replication is allowed
     * @return replication result
     */
    @Nonnull
    DbosReplicationResult archiveCards(@Nonnull String externalClientId,
                                       @Nonnull List<Card> cards,
                                       boolean allowPartialReplication);

    /**
     * Replicates a single GDS entity.
     *
     * @param plural GDS entity plural name
     * @param entity GDS entity
     * @param <T> GDS entity generic type parameter
     * @return replication result
     */
    @Nonnull
    <T extends GdsEntity> GdsReplicationResult replicateEntity(@Nonnull String plural, @Nonnull T entity);

    /**
     * Replicates a multiple GDS entity.
     *
     * @param plural GDS entity plural name
     * @param entities GDS entities
     * @param <T> GDS entity generic type parameter
     * @return replication result
     */
    @Nonnull
    <T extends GdsEntity> GdsReplicationResult replicateEntities(@Nonnull String plural, @Nonnull List<T> entities);
}
