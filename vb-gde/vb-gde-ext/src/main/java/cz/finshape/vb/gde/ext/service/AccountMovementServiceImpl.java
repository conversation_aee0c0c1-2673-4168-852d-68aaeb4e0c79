/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;

import java.util.List;

import jakarta.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.domain.dbos.gds.Movement;
import cz.finshape.vb.domain.internal.gde.rabbitmq.AccountMovementPushRequest;
import cz.finshape.vb.gde.ext.mapper.AccountMovementMapper;


/**
 * Default implementation of {@link AccountMovementService}
 *
 * <AUTHOR> <PERSON>na
 */
@Service
public class AccountMovementServiceImpl implements AccountMovementService {

    private final GdsIntegrationApiConnector gdsIntegrationApiConnector;

    private final AccountMovementMapper accountMovementMapper;

    /**
     * Constructor which assaying GDS integration client and account movement mapper
     *
     * @param gdsIntegrationApiConnector GDS Integration API connector
     * @param accountMovementMapper account movement mapper
     */
    @Autowired
    public AccountMovementServiceImpl(final GdsIntegrationApiConnector gdsIntegrationApiConnector,
                                      final AccountMovementMapper accountMovementMapper) {

        this.gdsIntegrationApiConnector = gdsIntegrationApiConnector;
        this.accountMovementMapper = accountMovementMapper;
    }

    @Override
    public void replicateAccountMovement(@Nonnull final AccountMovementPushRequest accountMovementPushRequest) {
        Assert.notNull(accountMovementPushRequest, "Account movement push request cannot be null");
        final var movement = accountMovementMapper.toMovement(accountMovementPushRequest);
        gdsIntegrationApiConnector.createReplaceEntities(Movement.EN_PLURAL, List.of(movement), false);
    }
}
