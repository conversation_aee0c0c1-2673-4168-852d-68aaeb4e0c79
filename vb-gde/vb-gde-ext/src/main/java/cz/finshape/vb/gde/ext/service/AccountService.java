/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import jakarta.annotation.Nonnull;

import cz.finshape.vb.domain.dbos.gds.Account;


/**
 * Account service.
 *
 * <AUTHOR>
 */
public interface AccountService {

    /**
     * Finds master account for the internal account.
     * <p><br>
     * The main purpose of the method is for resolving a correct master account to be referenced in card or account movements replication.
     *
     * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?spaceKey=P20009724&title=Cards+-+replication#Cardsreplication-Replicationflow">Card Replication - Flow</a>
     *
     * @param internalAccountBisAccountId account IBAN
     * @return master account
     */
    @Nonnull
    Account findMasterAccount(@Nonnull String internalAccountBisAccountId);
}
