/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.spx;


import org.springframework.boot.context.properties.ConfigurationProperties;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryProperties;
import cz.finshape.vb.gde.ext.connector.VbGdeConnectorAutoConfiguration;
import lombok.Getter;


/**
 * SPX Proxy API connector factory properties.
 *
 * <AUTHOR>
 */
@Getter
@ConfigurationProperties(SpxProxyApiConnectorFactoryProperties.PROXY_API_PREFIX)
class SpxProxyApiConnectorFactoryProperties extends AbstractConnectorFactoryProperties {

    static final String PROXY_API_PREFIX = VbGdeConnectorAutoConfiguration.PREFIX + ".spx.proxy-api";

    /**
     * Primary constructor.
     */
    SpxProxyApiConnectorFactoryProperties() {
        super(PROXY_API_PREFIX);
    }
}
