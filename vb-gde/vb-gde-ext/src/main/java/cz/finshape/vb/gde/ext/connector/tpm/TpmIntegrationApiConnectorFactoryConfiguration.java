/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.tpm;

import static cz.finshape.vb.gde.ext.connector.tpm.TpmIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInitializer;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryConfiguration;
import cz.finshape.vb.client.restclient.tpm.TpmIntegrationApiConnector;
import cz.finshape.vb.client.restclient.tpm.TpmIntegrationApiConnectorImpl;

import cz.bsc.commons.client.restclient.RestClientFactory;


/**
 * TPM Integration API connector configuration.
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(value = "enabled", prefix = INTEGRATION_API_PREFIX, matchIfMissing = true)
@EnableConfigurationProperties(TpmIntegrationApiConnectorFactoryProperties.class)
public class TpmIntegrationApiConnectorFactoryConfiguration extends AbstractConnectorFactoryConfiguration {

    /**
     * Primary constructor.
     *
     * @param factory RestClient factory
     * @param requestInitializer request initializer
     * @param properties properties
     */
    TpmIntegrationApiConnectorFactoryConfiguration(final RestClientFactory factory,
                                                   final ClientHttpRequestInitializer requestInitializer,
                                                   final TpmIntegrationApiConnectorFactoryProperties properties) {

        super(factory, requestInitializer, properties.getFactory());
    }

    /**
     * Tpm Integration API connector bean.
     *
     * @param properties properties
     * @return connector bean
     */
    @Bean
    TpmIntegrationApiConnector tpmIntegrationApiConnector(final TpmIntegrationApiConnectorFactoryProperties properties) {

        return new TpmIntegrationApiConnectorImpl(restClient, properties);
    }
}
