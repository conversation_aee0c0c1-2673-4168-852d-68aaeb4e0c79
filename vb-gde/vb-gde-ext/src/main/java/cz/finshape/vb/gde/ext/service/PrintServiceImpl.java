/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import java.time.Duration;

import jakarta.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import cz.finshape.vb.client.restclient.gps.GpsIntegrationApiConnector;
import cz.finshape.vb.core.client.gps.domain.GpsDoJobJsonRequest;
import cz.finshape.vb.core.poller.Pollers;
import cz.finshape.vb.domain.dbos.tpm.UploadEndOfYearBalanceReportInstruction;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.core.exception.technical.VbTechnicalException;
import cz.finshape.vb.domain.internal.gps.JobStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * Print service.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrintServiceImpl implements PrintService {

    @Value("${vb.gds.service.print.end-of-year-balance-report.sync.initial-sleep:250}")
    private long initialSleep;

    @Value("${vb.gds.service.print.end-of-year-balance-report.sync.sleep:1000}")
    private long sleep;

    @Value("${vb.gds.service.print.end-of-year-balance-report.sync.attempts:5}")
    private int attempts;

    private final GpsIntegrationApiConnector gpsIntegrationApiConnector;

    @Nonnull
    @Override
    public Resource printEndOfYearBalanceReportSynchronously(@Nonnull final String externalClientId,
                                                             @Nonnull final String instructionId,
                                                             @Nonnull final UploadEndOfYearBalanceReportInstruction instructionData) {

        final var doJobRequest = createGpsDoJobJsonRequest(externalClientId, instructionData);

        log.info("Requesting a GPS print job: Upload end-of-year balance report");
        final var doJobResponse = gpsIntegrationApiConnector.doJob(doJobRequest);
        final var jobId = doJobResponse.getJobId();

        log.info("Requesting the GPS print job status: jobId={}", jobId);
        final var getJobStatusResponse = Pollers
            .polling(() -> gpsIntegrationApiConnector.getJobStatus(jobId))
            .nonNull()
            .conditionally(res -> JobStatus.isTerminal(res.getStatus()))
            .poll(attempts, Duration.ofMillis(initialSleep), Duration.ofMillis(sleep));

        log.debug("Checking the GPS print job status: jobId={}, status={}", jobId, getJobStatusResponse.getStatus());
        final var jobStatus = JobStatus.fromCode(getJobStatusResponse.getStatus()).orElseThrow(); // The presence is guaranteed here.
        switch (jobStatus) {
            case FINISHED -> log.info("The GPS print job has successfully finished: {}", jobId);
            case FAILED -> {
                final var message= "GPS print job failed, jobId=%s, resolution=%s, resultMessage=%s"
                    .formatted(jobId, getJobStatusResponse.getResolution(), getJobStatusResponse.getResultMessage());
                log.error(message);
                throw new VbTechnicalException(VbErrorCodeEnum.GPS_PRINT_FAILED, message);
            }
            default -> throw new VbTechnicalException(VbErrorCodeEnum.UNKNOWN, "Unreachable");
        }

        log.info("Requesting the GPS print job result: jobId={}", jobId);
        return gpsIntegrationApiConnector.getJobResult(jobId);
    }

    /**
     * Creates a GPS do job request for the end-of-year balance report.
     *
     * @param externalClientId external client ID
     * @param instructionData instruction data
     * @return GPS do job request
     */
    @Nonnull
    private static GpsDoJobJsonRequest createGpsDoJobJsonRequest(@Nonnull String externalClientId,
                                                                 @Nonnull UploadEndOfYearBalanceReportInstruction instructionData) {
        final var year = instructionData.getDate().getYear();
        final var jobType = "%s-%s".formatted(externalClientId, year);
        final var fileName = "%s-%s.pdf".formatted(externalClientId, year);

        return new GpsDoJobJsonRequest()
            .withExportFormat("PDF")
            .withExportTemplate("classpath:templates/pdf/endofyearbalancereport/EndOfYearBalanceReport.tdxml")
            .withData(instructionData)
            .withDataSource("bean:endOfYearBalanceReportDataSourceProvider")
            .withJobTitle("Upload end-of-year balance report")
            .withJobType(jobType)
            .withResultFileName(fileName)
            .withResultEncoding("utf-8")
            .withLanguage("en")
            .withContextId("-1") // The field is required by GPS, but not used and not available.
            .withUserId(-1L); // The field is required by GPS, but not used and not available.
    }
}
