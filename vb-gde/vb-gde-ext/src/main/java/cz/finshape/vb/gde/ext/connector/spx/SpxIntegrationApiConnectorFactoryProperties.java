/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.spx;


import org.springframework.boot.context.properties.ConfigurationProperties;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryProperties;
import cz.finshape.vb.gde.ext.connector.VbGdeConnectorAutoConfiguration;
import lombok.Getter;


/**
 * SPX Integration API connector factory properties.
 *
 * <AUTHOR>
 */
@Getter
@ConfigurationProperties(SpxIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX)
class SpxIntegrationApiConnectorFactoryProperties extends AbstractConnectorFactoryProperties {

    static final String INTEGRATION_API_PREFIX = VbGdeConnectorAutoConfiguration.PREFIX + ".spx.integration-api";

    /**
     * Primary constructor.
     */
    SpxIntegrationApiConnectorFactoryProperties() {
        super(INTEGRATION_API_PREFIX);
    }
}
