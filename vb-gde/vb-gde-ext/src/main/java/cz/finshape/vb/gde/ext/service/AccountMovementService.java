/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import cz.finshape.vb.domain.internal.gde.rabbitmq.AccountMovementPushRequest;


/**
 * Account movement service. API defines all needed methods to work with account movements on GDE side
 *
 * <AUTHOR>
 */
public interface AccountMovementService {

    /**
     * Method will take movement, transform it to GFS entity {@link cz.finshape.vb.domain.dbos.gds.Movement}, then it will send it to GDS.
     *
     * @param accountMovementPushRequest {@link AccountMovementPushRequest} which should be send to GDS
     */
    void replicateAccountMovement(AccountMovementPushRequest accountMovementPushRequest);
}
