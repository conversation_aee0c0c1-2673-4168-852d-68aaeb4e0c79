/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.ips.gw;

import org.springframework.boot.context.properties.ConfigurationProperties;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryProperties;
import cz.finshape.vb.gde.ext.connector.VbGdeConnectorAutoConfiguration;
import lombok.Getter;

/**
 * IPS Integration API connector factory properties.
 *
 * <AUTHOR>
 */
@Getter
@ConfigurationProperties(IpsIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX)
public class IpsIntegrationApiConnectorFactoryProperties extends AbstractConnectorFactoryProperties {

    static final String INTEGRATION_API_PREFIX = VbGdeConnectorAutoConfiguration.PREFIX + ".ips.integration-api";

    /**
     * Primary constructor.
     */
    IpsIntegrationApiConnectorFactoryProperties() {
        super(INTEGRATION_API_PREFIX);
    }
}
