/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import cz.finshape.vb.domain.internal.TpmError;
import cz.finshape.vb.domain.internal.tpm.InstructionFlowStatus;


/**
 * TPM instruction service.
 *
 * <AUTHOR>
 */
public interface TpmInstructionService {

    /**
     * Triggers TPM via Kafka to change the instruction item status.
     *
     * @param instructionItemId instruction item ID
     * @param status target TPM instruction flow status
     */
    void triggerTransition(@Nonnull String instructionItemId, @Nonnull InstructionFlowStatus status);

    /**
     * Triggers TPM via Kafka to change the instruction item status.
     *
     * @param instructionItemId instruction item ID
     * @param status target TPM instruction flow status
     * @param tpmError TPM error to be set in the instruction item
     */
    void triggerTransition(@Nonnull String instructionItemId, @Nonnull InstructionFlowStatus status, @Nullable TpmError tpmError);
}
