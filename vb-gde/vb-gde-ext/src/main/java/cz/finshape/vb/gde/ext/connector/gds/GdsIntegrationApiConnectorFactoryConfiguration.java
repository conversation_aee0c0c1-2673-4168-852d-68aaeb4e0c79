/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.gds;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInitializer;

import cz.finshape.gde.connector.gds.GdsIntegrationApiConnectorFactoryProperties;
import cz.finshape.vb.client.restclient.config.support.gds.AbstractGdsConnectorFactoryConfiguration;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnectorImpl;

import cz.bsc.commons.client.restclient.RestClientFactory;


/**
 * GDS Integration API connector configuration.
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(value = "enabled", prefix = GdsIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX, matchIfMissing = true)
@EnableConfigurationProperties(GdsIntegrationApiConnectorFactoryProperties.class)
public class GdsIntegrationApiConnectorFactoryConfiguration extends AbstractGdsConnectorFactoryConfiguration {

    /**
     * Primary constructor.
     *
     * @param factory RestClient factory
     * @param requestInitializer request initializer
     * @param properties properties
     */
    GdsIntegrationApiConnectorFactoryConfiguration(final RestClientFactory factory,
                                                   final ClientHttpRequestInitializer requestInitializer,
                                                   final GdsIntegrationApiConnectorFactoryProperties properties) {

        super(factory, requestInitializer, properties.getFactory());
    }

    /**
     * GDS Integration API connector bean.
     *
     * @param properties properties
     * @return connector bean
     */
    @Bean
    GdsIntegrationApiConnector gdsIntegrationApiConnector(final GdsIntegrationApiConnectorFactoryProperties properties) {

        return new GdsIntegrationApiConnectorImpl(restClient, properties.getPropertiesPrefix(), properties.getFactory());
    }
}
