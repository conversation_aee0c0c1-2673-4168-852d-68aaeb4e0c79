/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.mapper;


import static cz.finshape.vb.domain.internal.VbConstants.IBAN_COUNTRY_MOLDOVA_CODE;
import static cz.finshape.vb.domain.internal.gds.GdsCatalogValues.ResidencyTypeValue.NON_RESIDENT;
import static cz.finshape.vb.domain.internal.gds.GdsCatalogValues.ResidencyTypeValue.RESIDENT;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;

import cz.finshape.vb.client.restclient.gen.GenIntegrationApiConnector;
import cz.finshape.vb.core.client.auth.IdmSystem;
import cz.finshape.vb.core.client.gaas.domain.GaasCreateUserRequest;
import cz.finshape.vb.core.client.gen.domain.GenClient;
import cz.finshape.vb.core.client.gen.domain.GenExternalUserId;
import cz.finshape.vb.core.client.gen.domain.GenUser;
import cz.finshape.vb.domain.dbos.gds.City;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ClientCategory;
import cz.finshape.vb.domain.dbos.gds.ClientExtension;
import cz.finshape.vb.domain.dbos.gds.ClientIdentificationType;
import cz.finshape.vb.domain.dbos.gds.ClientStatus;
import cz.finshape.vb.domain.dbos.gds.Country;
import cz.finshape.vb.domain.dbos.gds.District;
import cz.finshape.vb.domain.dbos.gds.LegalCategoryType;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.dbos.gds.User;
import cz.finshape.vb.domain.internal.VbConstants;
import cz.finshape.vb.domain.internal.gde.rabbitmq.AbstractClientsPushRequest;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import lombok.extern.slf4j.Slf4j;


/**
 * Mapper for client push request to GDS and GEN client entities.
 *
 *
 * <AUTHOR> Cernil
 */
@Slf4j
@Mapper(componentModel = "spring")
public abstract class ClientMapper implements BaseMapper {

    /**
     * Explicit constant mapping into {@code null} using MapStruct expression.
     *
     * @see <a href="https://mapstruct.org/documentation/stable/reference/html/#expressions">Mapstruct#expressions</a>
     */
    protected static final String NULL = "java(null)";

    @Autowired
    private GenIntegrationApiConnector genIntegrationApiConnector;

    /**
     * Maps {@link AbstractClientsPushRequest} to GDS {@link Client}.
     *
     * @param client client to be created or updated
     * @param req client push request
     * @return client
     */
    @Nonnull
    public Client toClient(@Nullable final Client client, @Nonnull final AbstractClientsPushRequest req) {
        return toClientInternal(client != null ? client : new Client(), req);
    }

    /**
     * Maps {@link AbstractClientsPushRequest} to GDS {@link Client}.
     *
     * @param client client to be updated
     * @param req client push request
     * @return client
     */
    @Mapping(target = Client.FN_ID, source = "clientId")
    @Mapping(target = Client.FN_STATUS, source = "req", qualifiedByName = "toStatus")
    @Mapping(target = Client.FN_FISCALCODE, source = "fiscalCode")
    @Mapping(target = Client.FN_FIRSTNAME, source = "firstName")
    @Mapping(target = Client.FN_SURNAME, source = "surname")
    @Mapping(target = Client.FN_FULLNAME, source = "fullName")
    @Mapping(target = Client.FN_IDENTIFICATIONNUMBER, source = "identificationNumber")
    @Mapping(target = Client.FN_BIRTHDATE, source = "birthDate")
    @Mapping(target = Client.FN_MOBILENUMBER, source = "mobileNumber")
    @Mapping(target = Client.FN_BANK, source = "bank")
    @Mapping(target = Client.FN_TARIFFPACKET, source = "tariffPacket")
    @Mapping(target = Client.FN_CONTACTPERSON, source = "contactPerson")
    @Mapping(target = Client.FN_EMAIL, source = "email")
    @Mapping(target = Client.FN_LEGALCATEGORY, source = "req", qualifiedByName = "toLegalCategory")
    @Mapping(target = Client.FN_RESIDENCY, source = "req", qualifiedByName = "toResidency")
    @Mapping(target = Client.FN_CLIENTCATEGORY, source = "req", qualifiedByName = "toClientCategory")
    @Mapping(target = Client.FN_IDENTIFICATIONTYPE, source = "req", qualifiedByName = "toIdentificationType")
    @Mapping(target = Client.FN_LEGALSTATUS, ignore = true)
    @Mapping(target = Client.FN_CASCUSTOMERID, ignore = true) // leave the same value, do not change
    @Mapping(target = Client.FN_CREATIONDATE, ignore = true) // leave the same value, do not change
    @Mapping(target = Client.FN_MODIFIEDAT,expression = NULL) // null
    @Mapping(target = Client.FN_MODIFIEDBY, expression = NULL) // null
    protected abstract Client toClientInternal(@MappingTarget Client client, AbstractClientsPushRequest req);

    /**
     * Maps {@link AbstractClientsPushRequest} to GDS {@link User}.
     *
     * @param user user to be created or updated
     * @param req client push request
     * @return client
     */
    @Nonnull
    public User toUser(@Nullable final User user, @Nonnull final AbstractClientsPushRequest req) {
        return toUserInternal(user != null ? user : new User(), req);
    }

    /**
     * Maps {@link AbstractClientsPushRequest} to GDS {@link User}.
     *
     * @param user mapping target user
     * @param req abstract client push request
     * @return user
     */
    @Mapping(target = "id", source = "fiscalCode", qualifiedByName = "toGdsUserId")
    @Mapping(target = "status", ignore = true)  // set outside this mapper (temporarily) TODO: P20009724-8659: User status.
    @Mapping(target = "fiscalCode", source = "fiscalCode")
    @Mapping(target = "birthDate", source = "birthDate")
    @Mapping(target = "firstName", source = "firstName")
    @Mapping(target = "lastName", source = "surname")
    @Mapping(target = "idCardType", source = "identificationType")
    @Mapping(target = "idCardNumber", source = "identificationNumber")
    @Mapping(target = "contactAddressStreet", source = "street")
    @Mapping(target = "contactAddressStreetNumber", source = "streetNumber")
    @Mapping(target = "contactAddressCity", source = "city")
    @Mapping(target = "contactAddressZip", source = "zipCode")
    @Mapping(target = "contactAddressCountry", source = "country", qualifiedByName = "toCountry")
    @Mapping(target = "permanentAddressStreet", source = "street")
    @Mapping(target = "permanentAddressStreetNumber", source = "streetNumber")
    @Mapping(target = "permanentAddressCity", source = "city")
    @Mapping(target = "permanentAddressZip", source = "zipCode")
    @Mapping(target = "permanentAddressCountry", source = "country", qualifiedByName = "toCountry")
    @Mapping(target = "nationality", expression = NULL) // null
    @Mapping(target = "preferredLanguage", expression = NULL) // null
    @Mapping(target = "gender", expression = NULL) // null
    @Mapping(target = "relations", expression = NULL) // null
    @Mapping(target = "creationDate", ignore = true) // leave the same value, do not change
    @Mapping(target = "modifiedBy", expression = NULL) // null
    @Mapping(target = "modifiedAt", expression = NULL) // null
    @Mapping(target = "legalCapacity", expression = NULL) // null
    @Mapping(target = "role", expression = NULL) // null
    @Mapping(target = "contactEmail", expression = NULL) // null
    @Mapping(target = "contactMobileNumber", source = "mobileNumber")
    @Mapping(target = "p2pAliasId", ignore = true) // leave the same value, do not change
    protected abstract User toUserInternal(@MappingTarget User user, AbstractClientsPushRequest req);

    /**
     * Maps {@link AbstractClientsPushRequest} to {@link GenClient}.
     *
     * @param request request
     * @return GEN Client
     */
    @Mapping(target = "masterSystemCode", constant = "EXTERNAL_CLIENTS")
    @Mapping(target = "externalClientId", source = "clientId")
    @Mapping(target = "statusId", source = "status", qualifiedByName = "toStatusId")
    @Mapping(target = "description", source = "fullName")
    @Mapping(target = "contactName", source = "contactPerson")
    @Mapping(target = "contactPhone", source = "mobileNumber")
    @Mapping(target = "contactFax", ignore = true)
    @Mapping(target = "contactGsm", source = "mobileNumber")
    @Mapping(target = "contactEmail", source = "email")
    @Mapping(target = "address1", source = "street")
    @Mapping(target = "address2", source = "streetNumber")
    @Mapping(target = "address3", source = "city")
    @Mapping(target = "address4", source = "zipCode")
    @Mapping(target = "address5", source = "country")
    @Mapping(target = "address6", source = "district")
    @Mapping(target = "legalCategoryId", source = ".", qualifiedByName = "toLegalCategoryId")
    @Mapping(target = "clientCategoryId", source = ".", qualifiedByName = "toClientCategoryId")
    @Mapping(target = "clientAttr1", source = "tariffPacket")
    // Though GEN has no catalog, we can use GDS values to help as they are identical.
    @Mapping(target = "clientAttr2", source = "residency", qualifiedByName = "toResidencyCode")
    @Mapping(target = "clientAttr3", source = "accessType")
    @Mapping(target = "clientAttr4", source = "riskCategory")
    @Mapping(target = "clientAttr5", source = "brandName")
    @Mapping(target = "clientAttr6", source = "registeringBranch")
    @Mapping(target = "clientAttr7", source = "lastKycUpdate")
    @Mapping(target = "clientAttr8", source = "clientGroup")
    @Mapping(target = "clientAttr9", ignore = true)
    @Mapping(target = "clientAttr10", ignore = true)
    @Mapping(target = "identificationTypeId", constant = "1L")
    @Mapping(target = "identificationNumber", source = "fiscalCode")
    @Mapping(target = "birthDate", source = "birthDate")
    @Mapping(target = "externalVersion", ignore = true)
    public abstract GenClient toGenClient(AbstractClientsPushRequest request);

    /**
     * Maps {@link AbstractClientsPushRequest} to {@link GenUser}.
     *
     * @param request request
     * @return GEN User
     */
    @Mapping(target = "address1", source = "street")
    @Mapping(target = "address2", source = "streetNumber")
    @Mapping(target = "address3", source = "city")
    @Mapping(target = "address4", source = "zipCode")
    @Mapping(target = "address5", source = "country")
    @Mapping(target = "address6", source = "district")
    @Mapping(target = "contactPhone", source = "mobileNumber")
    @Mapping(target = "contactFax", ignore = true)
    @Mapping(target = "contactGsm", source = "mobileNumber")
    @Mapping(target = "contactEmail", source = "email")
    @Mapping(target = "firstName", source = "firstName")
    @Mapping(target = "lastName", source = "surname")
    @Mapping(target = "middleName", ignore = true)
    @Mapping(target = "externalUserId", source = "fiscalCode", qualifiedByName = "toExternalUserId")
    @Mapping(target = "idCardNumber", source = "identificationNumber")
    @Mapping(target = "idCardTypeId", source = "identificationType", qualifiedByName = "toIdentificationTypeId")
    @Mapping(target = "permanentAddress1", source = "street")
    @Mapping(target = "permanentAddress2", source = "streetNumber")
    @Mapping(target = "permanentAddress3", source = "city")
    @Mapping(target = "permanentAddress4", source = "zipCode")
    @Mapping(target = "permanentAddress5", source = "country")
    @Mapping(target = "permanentAddress6", source = "district")
    @Mapping(target = "personalNumber", source = "fiscalCode")
    @Mapping(target = "title", ignore = true)
    @Mapping(target = "titleAfter", ignore = true)
    @Mapping(target = "birthDate", source = "birthDate")
    @Mapping(target = "gender", ignore = true)
    @Mapping(target = "nationality", ignore = true)
    @Mapping(target = "userLanguage", ignore = true)
    @Mapping(target = "statusId", constant = "1L")
    @Mapping(target = "verifiedUser", constant = "true")
    @Mapping(target = "externalVersion", ignore = true)
    public abstract GenUser toGenUser(AbstractClientsPushRequest request);

    /**
     * Maps {@link AbstractClientsPushRequest} to {@link GaasCreateUserRequest}.
     *
     * @param request request
     * @return GAAS Create Client request
     */
    @Mapping(target = "status", constant = "1")
    @Mapping(target = "idmSystemId", constant = "5")
    @Mapping(target = "idmSystemUserId", source = "fiscalCode")
    @Mapping(target = "firstName", source = "firstName")
    @Mapping(target = "lastName", source = "surname")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "phoneNumber", source = "mobileNumber")
    @Mapping(target = "userCategoryId", constant = "2")
    public abstract GaasCreateUserRequest toGaasCreateUserRequest(AbstractClientsPushRequest request);

    /**
     * @param req client push request
     * @return client identification type
     */
    @Nullable
    @Named("toIdentificationType")
    public ClientIdentificationType toIdentificationType(@Nonnull final AbstractClientsPushRequest req) {
       if (req.getIdentificationType() == null) {
            return null;
        }

        return new ClientIdentificationType(req.getIdentificationType());
    }

    /**
     * If residency is {@link AbstractClientsPushRequest#RESIDENCY_MD}, then resident, otherwise non-resident.
     * @param req client push request
     * @return residency type
     */
    @Nullable
    @Named("toResidency")
    public ResidencyType toResidency(@Nonnull final AbstractClientsPushRequest req) {
        if (req.getResidency() == null) {
            return null;
        }

        return new ResidencyType(
            req.isMdResident() ?
                RESIDENT.getEntityUniqueId() :
                NON_RESIDENT.getEntityUniqueId()
        );
    }

    /**
     * @param req client push request
     * @return legal category type
     */
    @Nullable
    @Named("toLegalCategory")
    public LegalCategoryType toLegalCategory(@Nonnull final AbstractClientsPushRequest req) {
        if (req.getLegalCategory() == null) {
            return null;
        }

        return new LegalCategoryType(
            req.isCorporate() ?
                GdsCatalogValues.LegalCategoryTypeValue.CORPORATE.getEntityUniqueId() :
                GdsCatalogValues.LegalCategoryTypeValue.RETAIL.getEntityUniqueId()
        );
    }

    /**
     * Map client category
     * @param req client push request
     * @return client category
     */
    @Nullable
    @Named("toClientCategory")
    public ClientCategory toClientCategory(@Nonnull final AbstractClientsPushRequest req) {
        if (req.getClientCategory() == null) {
            return null;
        }

        return new ClientCategory(req.getClientCategory());
    }

    /**
     * Maps {@link AbstractClientsPushRequest} to GDS {@link ClientExtension}.
     *
     * @param clientExtension client extension to be updated
     * @param req client push request
     * @return client extension
     */
    @Nonnull
    public ClientExtension toClientExtension(@Nullable final ClientExtension clientExtension, @Nonnull final AbstractClientsPushRequest req) {
        return toClientExtensionInternal(clientExtension != null ? clientExtension : new ClientExtension(), req);
    }

    /**
     * Maps client address information to {@link ClientExtension}.
     *
     * @param clientExtension client extension to be updated
     * @param req client push request
     * @return client extension
     */
    @Mapping(target = ClientExtension.FN_CLIENT, source = "req.clientId", qualifiedByName = "toClientReference")
    @Mapping(target = ClientExtension.FN_CITY, source = "req.city", qualifiedByName = "toCity")
    @Mapping(target = ClientExtension.FN_ZIPCODE, source = "req.zipCode")
    @Mapping(target = ClientExtension.FN_COUNTRY, source = "req.country", qualifiedByName = "toCountry")
    @Mapping(target = ClientExtension.FN_DISTRICT, source = "req.district", qualifiedByName = "toDistrict")
    @Mapping(target = ClientExtension.FN_STREET, source = "req.street")
    @Mapping(target = ClientExtension.FN_STREETNUMBER, source = "req.streetNumber")
    protected abstract ClientExtension toClientExtensionInternal(@MappingTarget ClientExtension clientExtension, AbstractClientsPushRequest req);

    /**
     * Crete client with id as client reference
     * @param clientId client id
     * @return client with reference
     */
    @Nullable
    @Named("toClientReference")
    public Client toClientReference(@Nullable final String clientId) {
        return clientId == null ? null : new Client(clientId);
    }

    /**
     * Map city to {@link City}
     * @param city city name
     * @return city
     */
    @Nullable
    @Named("toCity")
    public City toCity(@Nullable final String city) {
        return city == null ? null : new City(city);
    }

    /**
     * Map country to {@link Country}
     * @param countryId country id
     * @return country
     */
    @Nullable
    @Named("toCountry")
    public Country toCountry(@Nullable final String countryId) {
        return countryId == null ? null : new Country(countryId);
    }

    /**
     * Map district to {@link District}
     * @param districtId district id
     * @return district
     */
    @Nullable
    @Named("toDistrict")
    public District toDistrict(@Nullable final String districtId) {
        return districtId == null ? null : new District(districtId);
    }

    /**
     * Maps user ID to {@link GenExternalUserId}.
     * @param fiscalCode fiscal code
     * @return external user ID
     */
    @Nullable
    @Named("toExternalUserId")
    public GenExternalUserId toExternalUserId(@Nullable final String fiscalCode) {
        return fiscalCode == null ? null : new GenExternalUserId(VbConstants.IDM_SYSTEM_NAME, fiscalCode);
    }

    /**
     * Maps status to status ID.
     *
     * @param status status
     * @return status ID
     */
    @Nullable
    @Named("toStatusId")
    public String toStatusId(@Nullable final String status) {
        if (status == null) {
            return null;
        }
        return switch (status) {
            case "1" -> "1";
            case "99" -> "3";
            default -> null;
        };
    }

    /**
     * Maps client category to legal category ID.
     * <strong>Important:</strong> To be sure, we allow case-insensitive equality.
     *
     * @param clientsPushRequest request
     * @return legal category ID
     */
    @Nullable
    @Named("toLegalCategoryId")
    public String toLegalCategoryId(@Nonnull final AbstractClientsPushRequest clientsPushRequest) {
        // YES, clientCategory is used for legalCategoryId.
        final var clientCategory = clientsPushRequest.getClientCategory();
        final var result = genIntegrationApiConnector
            .searchDataEntities(VbConstants.GenAuthorizationEntity.LEGAL_CATEGORY_TYPES);
        final var legalCategoryTypes = result.getDataEntities().get(VbConstants.GenAuthorizationEntity.LEGAL_CATEGORY_TYPES);

        for (var node : legalCategoryTypes) {
            final var hostLegalCategCode = node.get("hostLegalCategCode");
            if (hostLegalCategCode != null && clientCategory.equals(hostLegalCategCode.asText())) {
                return String.valueOf(node.get("legalCategId").asInt());
            }
        }
        return null;
    }

    /**
     * Maps client segment to client category ID.
     * <strong>Important:</strong> To be sure, we allow case-insensitive equality.
     *
     * @param clientsPushRequest request
     * @return client category ID
     */
    @Nullable
    @Named("toClientCategoryId")
    public String toClientCategoryId(@Nonnull final AbstractClientsPushRequest clientsPushRequest) {
        if (clientsPushRequest.isCorporate()) {
            final var clientSegment = clientsPushRequest.getClientSegment();
            final var result = genIntegrationApiConnector
                .searchDataEntities(VbConstants.GenAuthorizationEntity.CLIENT_CATEGORIES);
            final var clientCategories = result.getDataEntities().get(VbConstants.GenAuthorizationEntity.CLIENT_CATEGORIES);

            for (var node : clientCategories) {
                final var bisCodeNode = node.get("bisCode");
                if (bisCodeNode != null && clientSegment.equals(bisCodeNode.asText())) {
                    return String.valueOf(node.get("clientCategId").asInt());
                }
            }
            return null;
        } else {
            // Assuming anything else is retail.
            if (!clientsPushRequest.isRetail()) {
                log.warn("Unknown legal category '{}', assuming retail", clientsPushRequest.getLegalCategory());
            }
            return "1";
        }
    }

    /**
     * Maps identity card type by its short code into identification type ID.
     *
     * @param identityCardType identity card type
     * @return identification type ID
     */
    @Nullable
    @Named("toIdentificationTypeId")
    public Long toIdentificationTypeId(@Nullable final String identityCardType) {
        if (identityCardType == null) {
            return null;
        }
        final var result = genIntegrationApiConnector
            .searchDataEntities(VbConstants.GenAuthorizationEntity.IDENTITY_CARD_TYPE);
        final var identityCardTypes = result.getDataEntities().get(VbConstants.GenAuthorizationEntity.IDENTITY_CARD_TYPE);

        for (var node : identityCardTypes) {
            final var shortCodeNode = node.get("shortCode");
            if (shortCodeNode != null && identityCardType.equals(shortCodeNode.asText())) {
                return (long) node.get("identityCardTypeId").asInt();
            }
        }
        return null;
    }

    /**
     * Maps fiscal code to GDS {@link User#getId()}.
     *
     * @param fiscalCode fiscal code
     * @return GDS {@link User#getId()}
     */
    @Nullable
    @Named("toGdsUserId")
    public String toGdsUserId(@Nullable final String fiscalCode) {
        if (fiscalCode == null) {
            return null;
        }
        return IdmSystem.withPrefix(fiscalCode);
    }

    /**
     * Maps residency to residency code.
     *
     * @param residency residency
     * @return residency code
     */
    @Nullable
    @Named("toResidencyCode")
    public String toResidencyCode(@Nullable final String residency) {
        if (residency == null) {
            return null;
        }
        return (IBAN_COUNTRY_MOLDOVA_CODE.equals(residency) ? RESIDENT : NON_RESIDENT).getEntityUniqueId();
    }

    /**
     * Maps client status.
     *
     * @param req request
     * @return client status
     */
    @Nullable
    @Named("toStatus")
    public ClientStatus toStatus(@Nonnull final AbstractClientsPushRequest req) {
        if (req.getStatus() == null) {
            return null;
        }

        try {
            final int status = Integer.parseInt(req.getStatus());
            if (status == 1 || status == 99) {
                return new ClientStatus(req.getStatus());
            } else {
                log.error("Invalid client status number: {}", req.getStatus());
            }

        } catch (NumberFormatException e) {
            log.error("Invalid client status value: {}", req.getStatus());
        }
        return null;
    }
}
