/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import java.util.Set;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import cz.finshape.vb.core.client.gde.domain.DbosReplicationResult;
import cz.finshape.vb.core.client.gde.domain.RelatedUserClients;
import cz.finshape.vb.core.client.gds.domain.GdsReplicationResult;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.ClientExtension;
import cz.finshape.vb.domain.internal.gde.rabbitmq.BranchesPushRequest;
import cz.finshape.vb.domain.internal.gde.rabbitmq.ClientsPushRequest;
import cz.finshape.vb.domain.internal.gde.rabbitmq.FriendsAndFamilyClientsPushRequest;
import cz.finshape.vb.domain.internal.gde.rabbitmq.SalaryProjectPushRequest;


/**
 * Service handling login replication (synchronous & asynchronous)
 *
 * <AUTHOR>
 */
public interface ReplicationService {

    /**
     * Replicates client accounts.
     *
     * @param externalClientId external client identifier
     * @return replication state
     */
    @Nonnull
    DbosReplicationResult replicateAccounts(@Nonnull String externalClientId);

    /**
     * Replicates client cards.
     *
     * @param externalClientId external client identifier
     * @return replication state
     */
    @Nonnull
    DbosReplicationResult replicateCards(@Nonnull String externalClientId);

    /**
     * Replicates client cards and creates user-client relations if necessary.
     *
     * @param externalClientId external client identifier
     * @param relatedUserClients related external client IDs the user has access to
     * @return replication state
     */
    @Nonnull
    DbosReplicationResult replicateCards(@Nonnull String externalClientId,
                                         @Nullable RelatedUserClients relatedUserClients);

    /**
     * Replicates card limits.
     *
     * @param cardIds GDS card IDs for replication for which limits are fetched replicated
     * @return replication result
     */
    @Nonnull
    GdsReplicationResult replicateCardLimits(@Nonnull Set<String> cardIds);

    /**
     * Replicates Salary Project or Meal Ticket.
     *
     * @param salaryProjectPushRequest Salary Project or Meal Ticket push request
     * @return replication result
     */
    @Nonnull
    GdsReplicationResult replicateSalaryProject(@Nonnull SalaryProjectPushRequest salaryProjectPushRequest);

    /**
     * Replicates Branch.
     *
     * @param branch branch
     * @return replication result
     */
    @Nonnull
    GdsReplicationResult replicateBranch(@Nonnull BranchesPushRequest branch);

    /**
     * Replicates Client and User.
     *
     * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?pageId=312345133">Confluence</a>
     *
     * @param client client
     * @param clientExtension client extension
     * @param payload push request payload
     * @return replication result
     */
    @Nonnull
    GdsReplicationResult replicateClientAndUser(@Nullable Client client,
                                                @Nullable ClientExtension clientExtension,
                                                @Nonnull ClientsPushRequest payload);

    /**
     * Replicates Friends and Family Client and User.
     *
     * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?pageId=312353709">Confluence</a>
     *
     * @param payload push request payload
     * @return replication result
     */
    @Nonnull
    DbosReplicationResult replicateFriendsAndFamilyClientAndUser(@Nonnull FriendsAndFamilyClientsPushRequest payload);
}
