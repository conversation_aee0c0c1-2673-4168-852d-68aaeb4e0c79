/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import jakarta.annotation.Nonnull;

import org.springframework.core.io.Resource;

import cz.finshape.vb.domain.dbos.tpm.UploadEndOfYearBalanceReportInstruction;


/**
 * Print service.
 *
 * <AUTHOR>
 */
public interface PrintService {

    /**
     * Performs <strong>synchronously</strong> requesting the GPS print job and awaits for its result in the blocking manner.
     * The implementation must fail if GPS does not produce a viable result in a reasonable time and attempts.
     *
     * @param externalClientId external client ID
     * @param instructionId instruction ID
     * @param instruction instruction
     * @return generated resource
     */
    @Nonnull
    Resource printEndOfYearBalanceReportSynchronously(@Nonnull String externalClientId,
                                                            @Nonnull String instructionId,
                                                            @Nonnull UploadEndOfYearBalanceReportInstruction instruction);
}
