/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import static java.util.Objects.requireNonNullElse;

import java.util.List;

import jakarta.annotation.Nonnull;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import cz.finshape.vb.client.restclient.tpm.TpmIntegrationApiConnector;
import cz.finshape.vb.client.restclient.xcr.XcrIntegrationApiConnector;
import cz.finshape.vb.domain.internal.gde.rabbitmq.ExchangeRatesPushRequest;
import cz.finshape.vb.gde.ext.mapper.ExchangeRateMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * Exchange rates service implementation.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExchangeRateServiceImpl implements ExchangeRateService {

    private final ExchangeRateMapper exchangeRateMapper;

    private final XcrIntegrationApiConnector xcrIntegrationApiConnector;

    private final TpmIntegrationApiConnector tpmIntegrationApiConnector;

    @Override
    public void replicateExchangeRatesAndLimits(@Nonnull final String releaseId,
                                                @Nonnull final ExchangeRatesPushRequest exchangeRatesPushRequest) {

        if (log.isDebugEnabled()) {
            // Info with a finer detail.
            log.info("Processing exchange rates as releaseId={} : {} ", releaseId, formattedRequest(exchangeRatesPushRequest));
        } else {
            log.info("Processing exchange rates as releaseId={}", releaseId);
        }

        log.info("Replicating exchange rates to XCR...");
        final var exchangeRatesMap = exchangeRateMapper.toExchangeRatesMap(releaseId, exchangeRatesPushRequest);
        xcrIntegrationApiConnector.releaseExchangeRateLists(exchangeRatesMap);

        final var limits = exchangeRateMapper.toExchangeRateLimits(exchangeRatesPushRequest);
        if (CollectionUtils.isEmpty(limits.getLimits())) {
            log.info("No exchange rate limits to replicate to TPM.");
        } else {
            log.info("Replicating exchange rate limits to TPM...");
            tpmIntegrationApiConnector.createExchangeRateLimit(limits);
        }
    }

    /**
     * Formats request for logging.
     * <p>
     * Output example:
     * <pre>
     * rateSets[0]: validFrom=2024-06-28T10:44:08.094509
     *  ├─ currencyMarkets[0]: [standard]
     *  │   ├─ rateList[0]: baseCurrency=[MDL]
     *  │   │   ├─ rate[0]: currency=[MDL], buy=[1], sell=[1]
     *  │   │   ├─ rate[1]: currency=[EUR], buy=[20.11], sell=[21.1155]
     *  │   │   ├─ rate[2]: currency=[USD], buy=[17.61], sell=[18.3144]
     *  │   ├─ rateList[1]: baseCurrency=[EUR]
     *  │   │   ├─ rate[0]: currency=[USD], buy=[1.03], sell=[1.0918]
     *  ├─ currencyMarkets[1]: [corporate]
     *  │   ├─ rateList[0]: baseCurrency=[MDL]
     *  │   │   ├─ rate[0]: currency=[MDL], buy=[1], sell=[1]
     *  │   │   ├─ rate[1]: currency=[EUR], buy=[19.65], sell=[21.4185]
     *  │   │   ├─ rate[2]: currency=[USD], buy=[16.61], sell=[17.6066]
     *  │   ├─ rateList[1]: baseCurrency=[EUR]
     *  │   │   ├─ rate[0]: currency=[USD], buy=[1.12], sell=[1.232]
     *  ├─ currencyMarkets[2]: [happyhour]
     *  │   ├─ rateList[0]: baseCurrency=[MDL]
     *  │   │   ├─ rate[0]: currency=[MDL], buy=[1], sell=[1]
     *  │   │   ├─ rate[1]: currency=[EUR], buy=[17.83], sell=[18.3649]
     *  │   │   ├─ rate[2]: currency=[USD], buy=[18.4], sell=[19.872]
     *  │   ├─ rateList[1]: baseCurrency=[EUR]
     *  │   │   ├─ rate[0]: currency=[USD], buy=[1.04], sell=[1.1232]
     * </pre>
     *
     * @param exchangeRatesPushRequest request
     * @return formatted request for logging
     */
    private String formattedRequest(@Nonnull final ExchangeRatesPushRequest exchangeRatesPushRequest) {
        final var rateSets = requireNonNullElse(exchangeRatesPushRequest.getRateSets(), List.<ExchangeRatesPushRequest.RateSet>of());
        final var stringBuilder = new StringBuilder(25 * rateSets.size());
        for (int i = 0; i < rateSets.size(); i++) {
            final var rateSet = rateSets.get(i);
            stringBuilder.append("%srateSets[%d]: validFrom=%s".formatted(
                System.lineSeparator(), i, rateSet.getValidFrom()));
            final var currencyMarkets = requireNonNullElse(rateSet.getCurrencyMarkets(), List.<ExchangeRatesPushRequest.CurrencyMarket>of());
            for (int j = 0; j < currencyMarkets.size(); j++) {
                final var currencyMarket = currencyMarkets.get(j);
                stringBuilder.append("%s ├─ currencyMarkets[%d]: [%s]".formatted(
                    System.lineSeparator(), j, currencyMarket.getMarketType()));
                final var rateLists = requireNonNullElse(currencyMarket.getRateLists(), List.<ExchangeRatesPushRequest.RateList>of());
                for (int k = 0; k < rateLists.size(); k++) {
                    final var rateList = rateLists.get(k);
                    stringBuilder.append("%s │   ├─ rateList[%d]: baseCurrency=[%s]".formatted(
                        System.lineSeparator(), k, rateList.getBaseCurrency()));
                    final var rates = rateList.getRates();
                    for (int l = 0; l < rates.size(); l++) {
                        final var rate = rates.get(l);
                        stringBuilder.append("%s │   │   ├─ rate[%d]: currency=[%s], buy=[%s], sell=[%s]".formatted(
                            System.lineSeparator(), l, rate.getCurrency(), rate.getBuy(), rate.getSell()));
                    }
                }
                final var limits = requireNonNullElse(currencyMarket.getLimits(), List.<ExchangeRatesPushRequest.Limit>of());
                for (int k = 0; k < limits.size(); k++) {
                    final var limit = limits.get(k);
                    stringBuilder.append("%s │   ├─ limits[%d]: currency=[%s], limit=[%s]".formatted(
                        System.lineSeparator(), k, limit.getCurrency(), limit.getCurrency()));
                }
            }
        }
        return stringBuilder.toString();
    }
}
