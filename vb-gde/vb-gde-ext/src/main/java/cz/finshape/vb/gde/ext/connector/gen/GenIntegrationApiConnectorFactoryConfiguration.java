/*
 * Copyright (c) 2023 Finshape <PERSON>ia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.gen;


import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInitializer;

import cz.finshape.gde.connector.gen.GenIntegrationApiConnectorFactoryProperties;
import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryConfiguration;
import cz.finshape.vb.client.restclient.gen.GenIntegrationApiConnector;
import cz.finshape.vb.client.restclient.gen.GenIntegrationApiConnectorImpl;

import cz.bsc.commons.client.restclient.RestClientFactory;


/**
 * GEN Integration API connector configuration.
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(value = "enabled", prefix = GenIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX, matchIfMissing = true)
@EnableConfigurationProperties(GenIntegrationApiConnectorFactoryProperties.class)
public class GenIntegrationApiConnectorFactoryConfiguration extends AbstractConnectorFactoryConfiguration {

    /**
     * Primary constructor.
     *
     * @param factory RestClient factory
     * @param requestInitializer request initializer
     * @param properties properties
     */
    GenIntegrationApiConnectorFactoryConfiguration(final RestClientFactory factory,
                                                   final ClientHttpRequestInitializer requestInitializer,
                                                   final GenIntegrationApiConnectorFactoryProperties properties) {

        super(factory, requestInitializer, properties.getFactory());
    }

    /**
     * GEN Integration API connector bean.
     *
     * @param properties properties
     * @return connector bean
     */
    @Bean
    GenIntegrationApiConnector genIntegrationClient(final GenIntegrationApiConnectorFactoryProperties properties) {

        return new GenIntegrationApiConnectorImpl(restClient, properties.getPropertiesPrefix(), properties.getFactory());
    }
}


