/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import java.util.Optional;

import jakarta.annotation.Nonnull;

import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.spx.SpxProxyApiConnector;
import cz.finshape.vb.core.client.dbos.GraphQLParams;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException;
import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * Account Service implementation.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountServiceImpl implements AccountService {

    private final GdsIntegrationApiConnector gdsIntegrationApiConnector;
    private final SpxProxyApiConnector spxProxyApiConnector;

    @Nonnull
    @Override
    public Account findMasterAccount(@Nonnull final String internalAccountIban) {
        // Find a T24 account in GDS by IBAN.
        return findT24MasterAccountInGds(internalAccountIban)
            // If not such account find, find a T24 account in CBS T24.
            .or(() -> findT24MasterAccountInCbsT24(internalAccountIban))
            // Remap to a simple GDS reference.
            .map(account -> new Account(account.getBisAccountId()))
            // If there is still no account, we reached the end of the world.
            .orElseThrow(() -> new VbBusinessException(VbErrorCodeEnum.THIRD_PARTY_T24_ERROR,
                "Cannot find T24 master account for internal account(iban=%s)".formatted(internalAccountIban)));
    }

    /**
     * Finds a T24 account by IBAN in GDS.
     *
     * @param iban iban
     * @return T24 account as a GDS account
     */
    @Nonnull
    private Optional<Account> findT24MasterAccountInGds(@Nonnull final String iban) {
        final var masterAccountCandidates = gdsIntegrationApiConnector.<Account>getEntities(Account.EN_PLURAL, GraphQLParams.builder()
                .filter(Account.FN_IBAN + ".eq({0})")
                .filterParams(iban)
                .fields(Account.FN_BISACCOUNTID)
                .build())
            .stream()
            .filter(account -> ExternalSystem.T24.isPrefixed(account.getBisAccountId()))
            .toList();

        // Guarantee a single and unambiguous account.
        if (masterAccountCandidates.isEmpty()) {
            log.debug("No T24 master account found in GDS by IBAN : {}", iban);
            return Optional.empty();
        } else if (masterAccountCandidates.size() > 1) {
            // This means the data are wrong.
            // TODO: Send notification to GNC.
            log.warn("Found more than one ({}) for T24 master account by IBAN : {}", masterAccountCandidates.size(), iban);
            return Optional.empty();
        } else {
            final var account = masterAccountCandidates.get(0);
            log.debug("Found T24 master account in GDS by IBAN : account(bisAccountId={}, iban={})", account.getBisAccountId(), iban);
            return Optional.of(account);
        }
    }

    /**
     * Finds a T24 account by IBAN in CBS T24.
     *
     * @param iban iban
     * @return T24 account as a GDS account
     */
    @Nonnull
    private Optional<Account> findT24MasterAccountInCbsT24(@Nonnull final String iban) {
        try {
            final var clientAccount = spxProxyApiConnector.getClientT24AccountByIban(iban);
            final var account = new Account(clientAccount.getBisAccountId());
            log.debug("Found T24 master account in CBS by IBAN : : account(bisAccountId={}, iban={})", account.getBisAccountId(), iban);
            return Optional.of(account);
        } catch (HttpClientErrorException e) {
            // no need to log too much, it's handled by the caller
            log.debug("Cannot find account in CBS by IBAN : {}", iban, e);
            return Optional.empty();
        }
    }
}
