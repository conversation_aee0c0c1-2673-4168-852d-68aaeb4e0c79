/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import jakarta.annotation.Nonnull;

import cz.finshape.vb.domain.internal.gde.rabbitmq.ExchangeRatesPushRequest;


/**
 * Exchange rates service.
 *
 * <AUTHOR>
 */
public interface ExchangeRateService {

    /**
     * Releases new exchange rate lists.
     *
     * @param releaseId release ID
     * @param exchangeRatesPushRequest request
     */
    void replicateExchangeRatesAndLimits(@Nonnull String releaseId,
                                         @Nonnull ExchangeRatesPushRequest exchangeRatesPushRequest);
}
