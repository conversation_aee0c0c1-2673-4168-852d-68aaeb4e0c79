/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.mapper;


import static cz.finshape.vb.domain.internal.xcr.ExchangeRateType.BUY;
import static cz.finshape.vb.domain.internal.xcr.ExchangeRateType.SELL;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.apache.commons.lang3.tuple.Pair;
import org.mapstruct.Mapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import cz.finshape.vb.core.client.tpm.domain.CreateExchangeRateLimitsJsonRequest;
import cz.finshape.vb.core.client.xcr.json.CurrencyRatesJson;
import cz.finshape.vb.core.client.xcr.json.ExchangeRateListJson;
import cz.finshape.vb.core.utils.TemporalUtils;
import cz.finshape.vb.domain.internal.gde.rabbitmq.ExchangeRatesPushRequest;
import cz.finshape.vb.domain.internal.tpm.VbExchangeRateLimits;
import cz.finshape.vb.domain.internal.xcr.MarketType;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.extern.slf4j.Slf4j;


/**
 * Exchange rate lists mapper.
 *
 * <AUTHOR> Charalambidis
 */
@Mapper(componentModel = "spring")
public interface ExchangeRateMapper {

    /**
     * Logger.
     * Lombok does not support {@link Slf4j annotation} on interfaces.
     * See <a href="https://github.com/projectlombok/lombok/issues/2048">GitHub Issue #2048</a>
     */
    @SuppressWarnings("squid:S1312")
    Logger logger = LoggerFactory.getLogger(ExchangeRateMapper.class);

    /**
     * Comparator for sorting the exchange rates by oldest first.
     * If the exchange rates share the same value date, order them alphabetically.
     *
     * @see <a href="https://docs.bsccloud.net/xcr/features/core/overview.html">XCR (Exchange Rates) in a Nutshell</a>
     * <blockquote>
     * When replicating more exchange rate list releases, for performance reasons it is important to replicate them from oldest to newest.
     * </blockquote>
     */
    Comparator<Pair<String, ExchangeRateListJson>> EXCHANGE_RATE_LIST_JSON_COMPARATOR = Comparator
        // Ascending order: oldest first.
        .<Pair<String, ExchangeRateListJson>, OffsetDateTime>comparing(pair -> pair.getRight().getValidFrom())
        // Ascending order: lexicographic order.
        .thenComparing(Pair::getLeft);

    /**
     * Maps {@link ExchangeRatesPushRequest} to map of {@link String} and {@link ExchangeRateListJson}.
     *
     * @param releaseId exchange rate list release ID
     * @param exchangeRatesPushRequest request
     * @return result map
     */
    @Nonnull
    default Map<String, ExchangeRateListJson> toExchangeRatesMap(@Nonnull final String releaseId,
                                                                 @Nonnull final ExchangeRatesPushRequest exchangeRatesPushRequest) {

        final var exchangeRatesMap = exchangeRatesPushRequest.getRateSets().stream()
            // The 1 to N mapping using mapMulti is more efficient compared to multiple nested flatMap calls.
            .<Pair<String, ExchangeRateListJson>>mapMulti((rateSet, consumer) -> {
                final var validFrom = rateSet.getValidFrom();
                rateSet.getCurrencyMarkets().forEach(currencyMarket -> {
                    final var currencyMarketType = currencyMarket.getMarketType();
                    currencyMarket.getRateLists().forEach(rateList -> {
                        final var baseCurrency = rateList.getBaseCurrency();
                        rateList.getRates().forEach(rate -> {
                            final var exchangeRateName = currencyMarketType + "_" + baseCurrency;
                            final var exchangeRateListJson = toExchangeRateListJson(releaseId, validFrom, rate);
                            final var pair = Pair.of(exchangeRateName, exchangeRateListJson);
                            logger.debug("Creating exchange rate list for currency market {} : {}",
                                exchangeRateName, exchangeRateListJson.getCurrencies().keySet());
                            consumer.accept(pair);
                        });
                    });
                });
            })
            .sorted(EXCHANGE_RATE_LIST_JSON_COMPARATOR)
            .collect(Collectors.toMap(
                Pair<String, ExchangeRateListJson>::getLeft,
                Pair<String, ExchangeRateListJson>::getRight,
                this::merge,
                LinkedHashMap<String, ExchangeRateListJson>::new));

        logger.debug("Mapped requested exchange rates in the order {}", exchangeRatesMap.keySet());
        return exchangeRatesMap;
    }

    /**
     * Maps {@link ExchangeRatesPushRequest} {@link CreateExchangeRateLimitsJsonRequest}.
     *
     * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Forex+implementation#Foreximplementation-Exchangeratesreplication">Confluence</a>
     *
     * @param exchangeRatesPushRequest request
     * @return result map
     */
    @Nonnull
    default CreateExchangeRateLimitsJsonRequest toExchangeRateLimits(@Nonnull final ExchangeRatesPushRequest exchangeRatesPushRequest) {
        final var exchangeRateLimits = exchangeRatesPushRequest.getRateSets().stream()
            // The 1 to N mapping using mapMulti is more efficient compared to multiple nested flatMap calls.
            .<VbExchangeRateLimits>mapMulti((rateSet, consumer) -> {
                final var validFrom = rateSet.getValidFrom();
                rateSet.getCurrencyMarkets().forEach(currencyMarket -> {
                    if (MarketType.CORPORATE.getId().equals(currencyMarket.getMarketType()) && currencyMarket.getLimits() != null) {
                        currencyMarket.getLimits().forEach(limit -> {
                            final var exchangeRateLimit = new VbExchangeRateLimits();
                            exchangeRateLimit.setCurrency(limit.getCurrency());
                            exchangeRateLimit.setValue(limit.getLimit());
                            exchangeRateLimit.setValidFrom(validFrom);
                            consumer.accept(exchangeRateLimit);
                        });
                    }
                });
            })
            .toList();

        return new CreateExchangeRateLimitsJsonRequest(exchangeRateLimits);
    }

    @SuppressFBWarnings(value = "UPM_UNCALLED_PRIVATE_METHOD", justification = "SpotBugs cannot detect private method calls in interfaces")
    private ExchangeRateListJson toExchangeRateListJson(@Nonnull final String releaseId,
                                                        @Nonnull final LocalDateTime validFrom,
                                                        @Nonnull final ExchangeRatesPushRequest.Rate rate) {

        final var rates = new HashMap<String, BigDecimal>();
        putNonNull(rates, BUY.name(), rate.getBuy());
        putNonNull(rates, SELL.name(), rate.getSell());

        final var currencyRatesJson = new CurrencyRatesJson();
        currencyRatesJson.setUnit(rate.getUnit());
        currencyRatesJson.setRates(rates);

        final var currencies = Map.of(
            rate.getCurrency(),
            currencyRatesJson);

        final var exchangeRateListJson = new ExchangeRateListJson();
        exchangeRateListJson.setCurrencies(currencies);
        exchangeRateListJson.setValidFrom(TemporalUtils.asInMoldova(validFrom));
        exchangeRateListJson.setReleaseId(releaseId);
        exchangeRateListJson.setExpectedValidTo(null);
        return exchangeRateListJson;
    }

    /**
     * Merges two {@link ExchangeRateListJson} objects into a single one and asserts the basic fields are equal to each other.
     *
     * @param left left
     * @param right right
     * @return merged
     */
    @SuppressFBWarnings(value = "UPM_UNCALLED_PRIVATE_METHOD", justification = "SpotBugs cannot detect private method calls in interfaces")
    private ExchangeRateListJson merge(@Nonnull final ExchangeRateListJson left, @Nonnull final ExchangeRateListJson right) {
        // Basic assertions.
        assertEqualsOrNulls(left.getValidFrom(), right.getValidFrom(), "validFrom");
        assertEqualsOrNulls(left.getReleaseId(), right.getReleaseId(), "releaseId");
        assertEqualsOrNulls(left.getExpectedValidTo(), right.getExpectedValidTo(), "expectedValidTo");

        final var mergedCurrencies = new HashMap<String, CurrencyRatesJson>();
        mergedCurrencies.putAll(left.getCurrencies());
        mergedCurrencies.putAll(right.getCurrencies());

        final var merged = new ExchangeRateListJson();
        merged.setValidFrom(left.getValidFrom());
        merged.setReleaseId(left.getReleaseId());
        merged.setExpectedValidTo(left.getExpectedValidTo());
        merged.setCurrencies(mergedCurrencies);
        return merged;
    }

    @SuppressFBWarnings(value = "UPM_UNCALLED_PRIVATE_METHOD", justification = "SpotBugs cannot detect private method calls in interfaces")
    private <K, V> void putNonNull(@Nonnull final Map<K, V> map, @Nonnull final K key, @Nullable final V value) {
        if (value != null) {
            map.put(key, value);
        }
    }

    /**
     * Performs an assertion whether the left and right objects are either both equal or both nulls.
     * @param left left
     * @param right right
     * @param name name of the field for the sake of message output
     */
    @SuppressFBWarnings(value = "UPM_UNCALLED_PRIVATE_METHOD", justification = "SpotBugs cannot detect private method calls in interfaces")
    private void assertEqualsOrNulls(@Nullable final Object left, @Nullable final Object right, @Nonnull final String name) {
        Assert.isTrue(
            (left == null && right == null) || Objects.equals(left, right),
            "Both " + name + " must be either equal or null");
    }
}
