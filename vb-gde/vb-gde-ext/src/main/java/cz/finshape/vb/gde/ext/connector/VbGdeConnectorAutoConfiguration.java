/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector;


import jakarta.annotation.PostConstruct;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Import;

import cz.finshape.gde.connector.GdeConnectorAutoConfiguration;
import cz.finshape.vb.gde.ext.connector.gaas.GaasIntegrationApiConnectorFactoryConfiguration;
import cz.finshape.vb.gde.ext.connector.gds.GdsIntegrationApiConnectorFactoryConfiguration;
import cz.finshape.vb.gde.ext.connector.gen.GenIntegrationApiConnectorFactoryConfiguration;
import cz.finshape.vb.gde.ext.connector.gps.GpsIntegrationApiConnectorFactoryConfiguration;
import cz.finshape.vb.gde.ext.connector.ips.gw.IpsIntegrationApiConnectorFactoryConfiguration;
import cz.finshape.vb.gde.ext.connector.spx.SpxIntegrationApiConnectorFactoryConfiguration;
import cz.finshape.vb.gde.ext.connector.spx.SpxProxyApiConnectorFactoryConfiguration;
import cz.finshape.vb.gde.ext.connector.tpm.TpmIntegrationApiConnectorFactoryConfiguration;
import cz.finshape.vb.gde.ext.connector.ups.UpsIntegrationApiConnectorFactoryConfiguration;
import cz.finshape.vb.gde.ext.connector.xcr.XcrIntegrationApiConnectorFactoryConfiguration;
import lombok.extern.slf4j.Slf4j;


/**
 * GDE connector auto-configuration.
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@AutoConfiguration
@ConditionalOnBean(GdeConnectorAutoConfiguration.class)
@ConditionalOnProperty(value = "enabled", prefix = VbGdeConnectorAutoConfiguration.PREFIX, matchIfMissing = true)
@Import({
    GaasIntegrationApiConnectorFactoryConfiguration.class,
    GdsIntegrationApiConnectorFactoryConfiguration.class,
    GenIntegrationApiConnectorFactoryConfiguration.class,
    GpsIntegrationApiConnectorFactoryConfiguration.class,
    IpsIntegrationApiConnectorFactoryConfiguration.class,
    SpxIntegrationApiConnectorFactoryConfiguration.class,
    SpxProxyApiConnectorFactoryConfiguration.class,
    TpmIntegrationApiConnectorFactoryConfiguration.class,
    UpsIntegrationApiConnectorFactoryConfiguration.class,
    XcrIntegrationApiConnectorFactoryConfiguration.class
})
public class VbGdeConnectorAutoConfiguration {

    public static final String PREFIX = "gde.connector";

    /**
     * Post construct method.
     */
    @PostConstruct
    public void postConstruct() {
        log.trace("{} applied", this.getClass().getSimpleName());
    }
}
