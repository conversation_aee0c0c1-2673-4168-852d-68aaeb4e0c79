/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import static cz.finshape.vb.domain.thirdparty.generated.product.ProductGroup.CURRENT;
import static cz.finshape.vb.domain.thirdparty.generated.product.ProductGroup.DEBIT;
import static cz.finshape.vb.domain.thirdparty.generated.product.ProductType.ACCOUNTS;
import static cz.finshape.vb.domain.thirdparty.generated.product.ProductType.CARDS;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.springframework.stereotype.Service;

import cz.finshape.vb.client.restclient.spx.SpxProxyApiConnector;
import cz.finshape.vb.domain.dbos.tpm.AddAccountWithCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddCurrentAccountRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddPhysicalCardToDigitalRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.ReissueCardRequestInstruction;
import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import cz.finshape.vb.domain.thirdparty.generated.product.CardForm;
import cz.finshape.vb.domain.thirdparty.generated.product.DeliveryMethod;
import cz.finshape.vb.domain.thirdparty.generated.product.OpenProductRequest;
import cz.finshape.vb.domain.thirdparty.generated.product.OpenProductResponse;
import cz.finshape.vb.domain.thirdparty.generated.product.ProductGroup;
import cz.finshape.vb.domain.thirdparty.generated.product.ProductType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * Product service.
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductInstructionServiceImpl implements ProductInstructionService {

    private final SpxProxyApiConnector spxProxyApiConnector;

    @Nonnull
    @Override
    public OpenProductResponse openT24CurrentAccount(@Nonnull final String externalClientId,
                                                     @Nonnull final String requestId,
                                                     @Nonnull final String productCode,
                                                     @Nonnull final String productName,
                                                     @Nonnull final AddCurrentAccountRequestInstruction instruction) {

        final var customerData = new OpenProductRequest.CustomerData()
            .withParentId(String.valueOf(instruction.getContinuallyCoordinateClientCenteredManufacturedId()))
            .withChannel(instruction.getChannel())
            .withIpAddress(instruction.getIpAddress())
            .withBranch(instruction.getBranch())
            .withIsReissue(null) // This is not card-related request.
            .withIsNewAccountRequired(null); // This is not card-related request.

        return openT24Product(externalClientId, requestId, productCode, productName, ACCOUNTS, CURRENT, instruction.getCurrency(), customerData);
    }

    @Nonnull
    @Override
    public OpenProductResponse openT24AccountWithCard(@Nonnull final String externalClientId,
                                                      @Nonnull final String requestId,
                                                      @Nonnull final String productCode,
                                                      @Nonnull final String productName,
                                                      @Nonnull final AddAccountWithCardRequestInstruction instruction) {

        final var customerData = new OpenProductRequest.CustomerData()
            .withParentId(String.valueOf(instruction.getContinuallyCoordinateClientCenteredManufacturedId()))
            .withChannel(instruction.getChannel())
            .withIpAddress(instruction.getIpAddress())
            .withBranch(instruction.getBranch())
            .withBisAccountId(null) // We request a new account, so do not link to the existing one.
            .withIsReissue(false)
            .withIsNewAccountRequired(true)
            .withCardForm(CardForm.resolve(instruction.getIsDigital()))
            .withEmbossingFirstName(instruction.getEmbossingFirstName())
            .withEmbossingLastName(instruction.getEmbossingLastName())
            .withHolderIdnp(instruction.getHolderIdnp());

        final var branchPickup = instruction.getBranchPickup();
        if (branchPickup == null) {
            customerData.setDeliveryMethod(DeliveryMethod.COURIER);
            customerData.setDeliveryAddress(new OpenProductRequest.Address()
                .withDistrict(instruction.getDeliveryDistrict())
                .withCity(instruction.getDeliveryCity())
                .withAddress(instruction.getDeliveryStreet()));
        } else {
            customerData.setDeliveryMethod(DeliveryMethod.BRANCH);
            customerData.setDeliveryBranch(branchPickup);
        }

        return openT24Product(externalClientId, requestId, productCode, productName, CARDS, DEBIT, instruction.getCurrency(), customerData);
    }

    @Nonnull
    @Override
    public OpenProductResponse openT24Card(@Nonnull final String externalClientId,
                                           @Nonnull final String requestId,
                                           @Nonnull final String productCode,
                                           @Nonnull final String productName,
                                           @Nonnull final String bisAccountId,
                                           @Nonnull final AddCardRequestInstruction instruction) {
        log.debug("opening T24 card: externalClientId={}, requestId={}, productCode={}, productName={}, bisAccountId={}",
            externalClientId, requestId, productCode, productName, bisAccountId);

        // remove prefixes from bisAccountId (T24 and WAY4), because their system does not accept them
        String bisAccountIdWithoutPrefix = ExternalSystem.T24.withoutPrefix(bisAccountId);
        bisAccountIdWithoutPrefix = ExternalSystem.WAY4.withoutPrefix(bisAccountIdWithoutPrefix);

        final var customerData = new OpenProductRequest.CustomerData()
            .withParentId(String.valueOf(instruction.getContinuallyCoordinateClientCenteredManufacturedId()))
            .withChannel(instruction.getChannel())
            .withIpAddress(instruction.getIpAddress())
            .withBranch(instruction.getBranch())
            .withBisAccountId(bisAccountIdWithoutPrefix)
            .withIsReissue(false)
            .withIsNewAccountRequired(false)
            .withCardForm(CardForm.resolve(instruction.getIsDigital()))
            .withEmbossingFirstName(instruction.getEmbossingFirstName())
            .withEmbossingLastName(instruction.getEmbossingLastName())
            .withHolderIdnp(instruction.getHolderIdnp());

        final var branchPickup = instruction.getBranchPickup();
        if (branchPickup == null) {
            customerData.setDeliveryMethod(DeliveryMethod.COURIER);
            customerData.setDeliveryAddress(new OpenProductRequest.Address()
                .withDistrict(instruction.getDeliveryDistrict())
                .withCity(instruction.getDeliveryCity())
                .withAddress(instruction.getDeliveryStreet()));
        } else {
            customerData.setDeliveryMethod(DeliveryMethod.BRANCH);
            customerData.setDeliveryBranch(branchPickup);
        }

        return openT24Product(externalClientId, requestId, productCode, productName, CARDS, DEBIT, instruction.getCurrency(), customerData);
    }

    @Nonnull
    @Override
    public OpenProductResponse reissueT24Card(@Nonnull final String externalClientId,
                                              @Nonnull final String requestId,
                                              @Nonnull final String productCode,
                                              @Nonnull final String productName,
                                              @Nonnull final String reissuedCardId,
                                              @Nonnull final ReissueCardRequestInstruction instruction) {

        final var customerData = new OpenProductRequest.CustomerData()
            .withParentId(String.valueOf(instruction.getContinuallyCoordinateClientCenteredManufacturedId()))
            .withChannel(instruction.getChannel())
            .withIpAddress(instruction.getIpAddress())
            .withBranch(instruction.getBranch())
            .withIsReissue(true)
            .withIsNewAccountRequired(false)
            .withReissuedCardId(reissuedCardId)
            .withCardReissueReason(instruction.getCardReissueReason());

        final var branchPickup = instruction.getBranchPickup();
        if (branchPickup == null) {
            customerData.setDeliveryMethod(DeliveryMethod.COURIER);
            customerData.setDeliveryAddress(new OpenProductRequest.Address()
                .withDistrict(instruction.getDeliveryDistrict())
                .withCity(instruction.getDeliveryCity())
                .withAddress(instruction.getDeliveryStreet()));
        } else {
            customerData.setDeliveryMethod(DeliveryMethod.BRANCH);
            customerData.setDeliveryBranch(branchPickup);
        }

        return openT24Product(externalClientId, requestId, productCode, productName, CARDS, DEBIT, instruction.getCurrency(), customerData);
    }

    @Nonnull
    @Override
    public OpenProductResponse addPhysicalCardToDigital(
        @Nonnull final String externalClientId,
        @Nonnull final String requestId,
        @Nonnull final String productCode,
        @Nonnull final String productName,
        @Nonnull final AddPhysicalCardToDigitalRequestInstruction instruction,
        @Nonnull final String cardId) {

        final var customerData = new OpenProductRequest.CustomerData()
            .withParentId(String.valueOf(instruction.getContinuallyCoordinateClientCenteredManufacturedId()))
            .withChannel(instruction.getChannel())
            .withIpAddress(instruction.getIpAddress())
            .withBranch(instruction.getBranch())
            .withDigitalCardId(cardId)
            .withIsReissue(false)
            .withIsNewAccountRequired(false)
            .withCardForm(CardForm.PHYSICAL);

        final var branchPickup = instruction.getBranchPickup();
        if (branchPickup == null) {
            customerData.setDeliveryMethod(DeliveryMethod.COURIER);
            customerData.setDeliveryAddress(new OpenProductRequest.Address()
                .withDistrict(instruction.getDeliveryDistrict())
                .withCity(instruction.getDeliveryCity())
                .withAddress(instruction.getDeliveryStreet()));
        } else {
            customerData.setDeliveryMethod(DeliveryMethod.BRANCH);
            customerData.setDeliveryBranch(branchPickup);
        }

        return openT24Product(externalClientId, requestId, productCode, productName, CARDS, DEBIT, instruction.getCurrency(), customerData);
    }

    @Nonnull
    private OpenProductResponse openT24Product(@Nonnull final String externalClientId,
                                               @Nonnull final String requestId,
                                               @Nonnull final String productCode,
                                               @Nonnull final String productName,
                                               @Nonnull final ProductType productType,
                                               @Nonnull final ProductGroup productGroup,
                                               @Nullable final String currency,
                                               @Nonnull final OpenProductRequest.CustomerData customerData) {

        final var request = new OpenProductRequest()
            .withProductCode(productCode)
            .withProductName(productName)
            .withProductType(productType)
            .withProductGroup(productGroup)
            .withRequestId(requestId)
            .withCurrency(currency)
            .withCustomerData(customerData);

        customerData.setClientId(externalClientId);

        final var response = spxProxyApiConnector.openT24Product(externalClientId, request);

        log.debug("Successfully requested to open T24 product : "
                + "requestId={}, productCode={}, productName={}, productType={}, productGroup={}, currency={}, externalClientId={}",
            response.getRequestId(), productCode, productName, productType.getValue(), productGroup.getValue(), currency, externalClientId);

        return response;
    }
}
