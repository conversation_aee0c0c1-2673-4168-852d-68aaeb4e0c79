/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.mapper;


import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;

import org.mapstruct.Named;

import cz.finshape.vb.core.utils.TemporalUtils;


/**
 * Base mapper with common conversion methods.
 *
 * <AUTHOR>
 */
public interface BaseMapper {

    /**
     * Converts local date time into UTC offset date time.
     *
     * @see <a href="https://docs.bsccloud.net/gds/current/customization/entity-types/attribute-types.html">Confluence</a>
     * @param localDateTime local date time
     * @return offset date time
     */
    @Named("asUtcOffsetDateTime")
    default OffsetDateTime asUtcOffsetDateTime(final LocalDateTime localDateTime) {
        return localDateTime == null ? null : localDateTime.atOffset(ZoneOffset.UTC);
    }

    /**
     * Converts local date time into UTC offset date from Moldova time zone.
     *
     * @see <a href="https://docs.bsccloud.net/gds/current/customization/entity-types/attribute-types.html">Confluence</a>
     * @param localDateTime local date time
     * @return offset date time
     */
    @Named("asMoldovanOffsetDateTime")
    default OffsetDateTime asMoldovanOffsetDateTime(final LocalDateTime localDateTime) {
        return localDateTime == null ? null : TemporalUtils.asInMoldova(localDateTime).withOffsetSameInstant(ZoneOffset.UTC);
    }

    /**
     * Converts offset date time into string.
     *
     * @param offsetDateTime offset date time
     * @return string
     */
    default String toString(final OffsetDateTime offsetDateTime) {
        return offsetDateTime == null ? null : offsetDateTime.toString();
    }
}
