/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import jakarta.annotation.Nonnull;

import cz.finshape.vb.domain.dbos.tpm.AddAccountWithCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddCardRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddCurrentAccountRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.AddPhysicalCardToDigitalRequestInstruction;
import cz.finshape.vb.domain.dbos.tpm.ReissueCardRequestInstruction;
import cz.finshape.vb.domain.thirdparty.generated.product.OpenProductResponse;


/**
 * Product instruction service.
 *
 * <AUTHOR>
 */
public interface ProductInstructionService {

    /**
     * Opens a T24 current account.
     *
     * @param externalClientId external client ID
     * @param requestId request ID
     * @param productCode product code
     * @param productName product name
     * @param instruction instruction item data
     * @return T24 open product response
     */
    @Nonnull
    OpenProductResponse openT24CurrentAccount(@Nonnull String externalClientId,
                                              @Nonnull String requestId,
                                              @Nonnull String productCode,
                                              @Nonnull String productName,
                                              @Nonnull AddCurrentAccountRequestInstruction instruction);

    /**
     * Opens a T24 account with card.
     *
     * @param externalClientId external client ID
     * @param requestId request ID
     * @param productCode product code
     * @param productName product name
     * @param instruction instruction item data
     * @return T24 open product response
     */
    @Nonnull
    OpenProductResponse openT24AccountWithCard(@Nonnull String externalClientId,
                                               @Nonnull String requestId,
                                               @Nonnull String productCode,
                                               @Nonnull String productName,
                                               @Nonnull AddAccountWithCardRequestInstruction instruction);

    /**
     * Opens a T24 card.
     *
     * @param externalClientId external client ID
     * @param requestId request ID
     * @param productCode product code
     * @param productName product name
     * @param bisAccountId prefixed BIS account ID
     * @param instruction instruction item data
     * @return T24 open product response
     */
    @Nonnull
    OpenProductResponse openT24Card(@Nonnull String externalClientId,
                                    @Nonnull String requestId,
                                    @Nonnull String productCode,
                                    @Nonnull String productName,
                                    @Nonnull String bisAccountId,
                                    @Nonnull AddCardRequestInstruction instruction);

    /**
     * Reissues a T24 card.
     *
     * @param externalClientId external client ID
     * @param requestId request ID
     * @param productCode product code
     * @param productName product name
     * @param reissuedCardId reissued card ID
     * @param instruction instruction item data
     * @return T24 open product response
     */
    @Nonnull
    OpenProductResponse reissueT24Card(@Nonnull String externalClientId,
                                       @Nonnull String requestId,
                                       @Nonnull String productCode,
                                       @Nonnull String productName,
                                       @Nonnull String reissuedCardId,
                                       @Nonnull ReissueCardRequestInstruction instruction);

    /**
     * Create a physical card from a digital one.
     *
     * @param externalClientId external client ID
     * @param vbCbsExternalId VB CBS external ID
     * @param cardProductCode card product code
     * @param cardProductName card product name
     * @param instruction instruction item data
     * @param cardId card ID
     * @return T24 open product response
     */
    @Nonnull
    OpenProductResponse addPhysicalCardToDigital(
        @Nonnull String externalClientId,
        @Nonnull String vbCbsExternalId,
        @Nonnull String cardProductCode,
        @Nonnull String cardProductName,
        @Nonnull AddPhysicalCardToDigitalRequestInstruction instruction,
        @Nonnull String cardId);
}
