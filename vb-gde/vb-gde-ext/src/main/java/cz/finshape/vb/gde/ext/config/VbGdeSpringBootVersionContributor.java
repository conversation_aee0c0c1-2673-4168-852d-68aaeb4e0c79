/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.config;


import static cz.finshape.vb.core.utils.InfoContributorAttributes.SPRING_BOOT_VERSION_PROPERTY;

import org.springframework.boot.SpringBootVersion;
import org.springframework.boot.actuate.info.Info;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.stereotype.Component;

/**
 * VbGde info contributor to add spring boot version to the monitoring/info endpoint
 *
 * <AUTHOR> Cernil
 */
@Component
public class VbGdeSpringBootVersionContributor implements InfoContributor {
    @Override
    public void contribute(Info.Builder builder) {
        builder.withDetail(SPRING_BOOT_VERSION_PROPERTY, SpringBootVersion.getVersion());
    }
}
