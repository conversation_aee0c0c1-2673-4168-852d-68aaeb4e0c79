/*
 * Copyright (c) 2025 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.service;


import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;


/**
 * VB GDE Service Configuration.
 *
 * <AUTHOR>
 */
@Configuration
@ComponentScan(basePackageClasses = VbGdeServiceConfiguration.class)
@EnableConfigurationProperties(value = ReplicationServiceConfigurationProperties.class)
public class VbGdeServiceConfiguration {

}
