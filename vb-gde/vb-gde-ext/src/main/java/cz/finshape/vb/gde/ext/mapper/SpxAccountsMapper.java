/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.mapper;


import java.util.List;

import jakarta.annotation.Nonnull;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.AccountStatus;
import cz.finshape.vb.domain.dbos.gds.AccountType;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.CreditType;
import cz.finshape.vb.domain.dbos.gds.Currency;
import cz.finshape.vb.domain.thirdparty.generated.t24.T24ClientAccount;
import cz.finshape.vb.domain.thirdparty.generated.way4.AccountsResponse;
import cz.finshape.vb.domain.thirdparty.generated.way4.Way4ClientAccount;


/**
 * Mapper for SPX Account.
 *
 * @see <a href="https://cz-support.finshape.com/confl/pages/viewpage.action?spaceKey=P20009724&title=CAcc-+Technical+analysis">Confluence</a>
 * <AUTHOR> Charalambidis
 */
@Mapper(componentModel = "spring")
public interface SpxAccountsMapper extends BaseMapper {

    String BIC_CODE_SUFFIX = "XXX";

    /**
     * Maps T24 Account response into a list of GDS accounts.
     *
     * @param t24AccountsResponse T24 Account response
     * @return List of GDS accounts
     */
    default List<Account> t24AccountsResponseToGdsAccounts(cz.finshape.vb.domain.thirdparty.generated.t24.AccountsResponse t24AccountsResponse) {
        return t24AccountsResponse.getClientAccounts()
            .stream()
            .map(this::t24AccountToGdsAccounts)
            .toList();
    }

    /**
     * Maps Way4 Account response into a list of GDS accounts.
     *
     * @param way4AccountsResponse Way4 Account response
     * @return List of GDS accounts
     */
    default List<Account> way4AccountsResponseToGdsAccounts(AccountsResponse way4AccountsResponse) {
        return way4AccountsResponse.getClientAccounts()
            .stream()
            .map(this::way4AccountToGdsAccount)
            .toList();
    }

    /**
     * Map T24 Account into GDS account.
     *
     * @param clientAccount T24 Account for mapping
     * @return GDS account {@link Account}
     */
    @Mapping(
        target = Account.FN_BISACCOUNTID,
        expression = "java(cz.finshape.vb.domain.internal.core.system.ExternalSystem.T24.withPrefix(clientAccount.getBisAccountId()))")
    @Mapping(target = Account.FN_STATUS, qualifiedByName = "toAccountStatus")
    @Mapping(target = Account.FN_TYPE, qualifiedByName = "toAccountType")
    @Mapping(target = Account.FN_CURRENCY, qualifiedByName = "toCurrency")
    @Mapping(target = Account.FN_OWNER, source="clientId", qualifiedByName = "toClient")
    @Mapping(target = Account.FN_CREDITTYPE, qualifiedByName = "toCreditType")
    @Mapping(target = Account.FN_MODIFIEDAT, source = "modifyAt", qualifiedByName = "asUtcOffsetDateTime")
    @Mapping(target = Account.FN_MODIFIEDBY, source = "modifyBy")
    @Mapping(target = Account.FN_ISINTERNAL, source = "accountGroup", qualifiedByName = "toIsInternal")
    @Mapping(target = Account.FN_BANKBICCODE, source = "bicCode", qualifiedByName = "toBankBicCode")
    //ignored fields - not coming from T24/WAY4
    @Mapping(target = "statusOrigin", ignore = true)
    @Mapping(target = "noticePeriod", ignore = true)
    @Mapping(target = "interestRate", ignore = true)
    @Mapping(target = "maturityDate", ignore = true)
    @Mapping(target = "feeRate", ignore = true)
    @Mapping(target = "maxTransferWithoutFee", ignore = true)
    @Mapping(target = "fixationPeriod", ignore = true)
    @Mapping(target = "minimumBalance", ignore = true)
    @Mapping(target = "automaticRenewal", ignore = true)
    //ignored fields - valid only for Way4
    @Mapping(target = "creditLimit", ignore = true)
    @Mapping(target = "creditLimitExpiryDate", ignore = true)
    @Mapping(target = "creditCardInterestRate", ignore = true)
    @Mapping(target = "instalmentPayment", ignore = true)
    Account t24AccountToGdsAccounts(T24ClientAccount clientAccount);

    /**
     * Map Way4 Account into GDS account.
     *
     * @param clientAccount Way4 Account for mapping
     * @return GDS account {@link Account}
     */
    @Mapping(
        target = Account.FN_BISACCOUNTID,
        expression = "java(cz.finshape.vb.domain.internal.core.system.ExternalSystem.WAY4.withPrefix(clientAccount.getBisAccountId()))")
    @Mapping(target = Account.FN_STATUS, qualifiedByName = "toAccountStatus")
    @Mapping(target = Account.FN_TYPE, qualifiedByName = "toAccountType")
    @Mapping(target = Account.FN_CURRENCY, qualifiedByName = "toCurrency")
    @Mapping(target = Account.FN_OWNER, source="clientId", qualifiedByName = "toClient")
    @Mapping(target = Account.FN_CREDITTYPE, qualifiedByName = "toCreditType")
    @Mapping(target = Account.FN_MODIFIEDAT, source = "modifyAt", qualifiedByName = "asUtcOffsetDateTime")
    @Mapping(target = Account.FN_MODIFIEDBY, source = "modifyBy")
    @Mapping(target = Account.FN_ISINTERNAL, source = "accountGroup", qualifiedByName = "toIsInternal")
    @Mapping(target = Account.FN_BANKBICCODE, source = "bicCode", qualifiedByName = "toBankBicCode")
    //ignored fields - not coming from T24/WAY4
    @Mapping(target = "interestRate", ignore = true)
    @Mapping(target = "statusOrigin", ignore = true)
    @Mapping(target = "noticePeriod", ignore = true)
    @Mapping(target = "feeRate", ignore = true)
    @Mapping(target = "maxTransferWithoutFee", ignore = true)
    @Mapping(target = "fixationPeriod", ignore = true)
    @Mapping(target = "maturityDate", ignore = true)
    @Mapping(target = "minimumBalance", ignore = true)
    @Mapping(target = "automaticRenewal", ignore = true)
    Account way4AccountToGdsAccount(Way4ClientAccount clientAccount);

    /**
     * Mapping for currency from {@link String} to GDS {@link Currency}.
     *
     * @param currency currency as {@link String}
     * @return currency as {@link Currency}
     */
    @Named("toCurrency")
    default Currency toCurrency(String currency) {
        return new Currency(currency);
    }

    /**
     * Mapping for credit type from {@link String} to GDS {@link CreditType}.
     *
     * @param type credit type as {@link String}
     * @return product type as {@link CreditType}
     */
    @Named("toCreditType")
    default CreditType toCreditType(final String type) {
        return new CreditType(type);
    }

    /**
     * Mapping for client from {@link String} to GDS {@link Client}.
     *
     * @param clientId as {@link String}
     * @return client as {@link Client}
     */
    @Named("toClient")
    default Client toClient(final String clientId) {
        return new Client(clientId);
    }

    /**
     * Mapping for account type from {@link String} to GDS {@link AccountType}.
     *
     * @param type account type as {@link String}
     * @return account type as {@link AccountType}
     */
    @Named("toAccountType")
    default AccountType toAccountType(@Nonnull final String type) {
        return new AccountType(type);
    }

    /**
     * Mapping for account status from {@link String} to GDS {@link AccountStatus}.
     *
     * @param status account status as {@link String}
     * @return account status as {@link AccountStatus}
     */
    @Named("toAccountStatus")
    default AccountStatus toAccountStatus(@Nonnull final String status) {
        return new AccountStatus(status);
    }

    /**
     * Mapping for account group from {@link String} to {@link Boolean}.
     *
     * @param accountGroup account group as {@link String}
     * @return whether the account group is internal or not
     */
    @Named("toIsInternal")
    default boolean toIsInternal(@Nonnull final String accountGroup) {
        return "internal".equals(accountGroup);
    }

    /**
     * Mapping for BIC code from {@link String} to {@link String}.
     * GDS uses only the first 8 characters of the BIC code and adds "XXX" at the end.
     * This should be final as of <a href="https://cz-support.finshape.com/jira/browse/P20009724-7877">Display account detail</a>
     *
     * @param bicCode BIC code as {@link String}
     * @return BIC code as {@link String}
     */
    @Named("toBankBicCode")
    default String toBankBicCode(@Nonnull final String bicCode) {
        if (StringUtils.isBlank(bicCode)) {
            return null;
        }
        return StringUtils.substring(bicCode, 0, 8) + BIC_CODE_SUFFIX;
    }
}
