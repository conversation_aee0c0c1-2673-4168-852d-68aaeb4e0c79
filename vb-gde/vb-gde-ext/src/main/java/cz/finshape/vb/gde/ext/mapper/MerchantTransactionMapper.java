/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.mapper;


import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.Currency;
import cz.finshape.vb.domain.dbos.gds.MerchantTransaction;
import cz.finshape.vb.domain.dbos.gds.Movement;
import cz.finshape.vb.domain.internal.gde.rabbitmq.PosStatementsPushRequest;
import cz.finshape.vb.domain.thirdparty.generated.misc.MerchantStatement;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;


/**
 * Mapper for {@link MerchantTransaction}.
 *
 * <AUTHOR> Charalambidis
 */
@Mapper(componentModel = "spring")
public interface MerchantTransactionMapper {

    /**
     * Maps {@link PosStatementsPushRequest} to {@link MerchantTransaction}.
     *
     * @param posStatementsPushRequest POS statements push request
     * @return merchant transaction
     */
    @Mapping(target = MerchantTransaction.FN_CLIENT, source = "clientId", qualifiedByName = "toClient")
    @Mapping(target = MerchantTransaction.FN_OPERATIONDATE, source = "operationDate")
    @Mapping(target = MerchantTransaction.FN_OPERATIONDATETIME, source = "operationDate")
    @Mapping(target = MerchantTransaction.FN_PROCESSINGDATE, source = "processingDate")
    @Mapping(target = MerchantTransaction.FN_PROCESSINGDATETIME, source = "processingDate")
    @Mapping(target = Movement.FN_SEARCHDATA, ignore = true) // filled via @AfterMapping
    MerchantTransaction toMerchantTransaction(PosStatementsPushRequest posStatementsPushRequest);

    /**
     * Maps {@link MerchantStatement} to {@link MerchantTransaction}.
     *
     * @param merchantStatement merchant statement
     * @return merchant transaction
     */
    @Mapping(target = MerchantTransaction.FN_CLIENT, source = "clientId", qualifiedByName = "toClient")
    @Mapping(target = MerchantTransaction.FN_OPERATIONDATE, source = "operationDate")
    @Mapping(target = MerchantTransaction.FN_OPERATIONDATETIME, source = "operationDate")
    @Mapping(target = MerchantTransaction.FN_PROCESSINGDATE, source = "processingDate")
    @Mapping(target = MerchantTransaction.FN_PROCESSINGDATETIME, source = "processingDate")
    @Mapping(target = Movement.FN_SEARCHDATA, ignore = true) // filled via @AfterMapping
    MerchantTransaction toMerchantTransaction(MerchantStatement merchantStatement);

    /**
     * Mapping for client from {@link String} to GDS {@link Client}.
     *
     * @param clientId as {@link String}
     * @return client as {@link Client}
     */
    @Named("toClient")
    default Client toClient(final String clientId) {
        return new Client(clientId);
    }

    /**
     * Maps currency code to {@link Currency}.
     *
     * @param currencyCode currency code
     * @return GDS currency
     */
    default Currency toCurrency(final String currencyCode) {
        return Optional.ofNullable(currencyCode)
            .map(Currency::new)
            .orElse(null);
    }

    /**
     * Maps offset date time to local date.
     *
     * @param offsetDateTime offset date time
     * @return local date
     */
    default LocalDate toLocalDate(final OffsetDateTime offsetDateTime) {
        return Optional.ofNullable(offsetDateTime)
            .map(OffsetDateTime::toLocalDate)
            .orElse(null);
    }

    /**
     * Fill search data into transaction
     *
     * @param transaction the transaction
     */
    @AfterMapping
    default void fillSearchData(@MappingTarget MerchantTransaction transaction) {
        transaction.setSearchData(generateSearchData(transaction));
    }

    /**
     * Generates search data for the transaction.
     *
     * @param transaction the transaction
     * @return the search data
     */
    @SuppressFBWarnings(value = "UPM_UNCALLED_PRIVATE_METHOD", justification = "SpotBugs cannot detect private method calls in interfaces")
    @Nonnull
    private String generateSearchData(@Nonnull final MerchantTransaction transaction) {
        return Stream.of(
                transaction.getTransactionId(),
                transaction.getContractId(),
                transaction.getOperationDateTime(),
                transaction.getProcessingDateTime(),
                transaction.getAmount(),
                getCurrencyId(transaction.getCurrency()),
                transaction.getReflectedAmount(),
                transaction.getCommission(),
                transaction.getAmountToBePaid(),
                transaction.getOperationType(),
                transaction.getAuthorizationCode(),
                transaction.getRrn(),
                transaction.getDeviceId(),
                transaction.getTerminalName(),
                transaction.getLocality(),
                transaction.getTargetNumber(),
                transaction.getCommentText(),
                transaction.getBin(),
                transaction.getTransactionCondition(),
                transaction.getChannel(),
                transaction.getCardType(),
                transaction.getBank(),
                getCurrencyId(transaction.getTransactionCurrency()),
                transaction.getIsTme()
            )
            .filter(Objects::nonNull)
            .map(Objects::toString)
            .filter(Predicate.not(String::isBlank))
            .collect(Collectors.joining(StringUtils.SPACE));
    }

    /**
     * Gets the currency ID.
     *
     * @param currency the currency
     * @return the currency ID
     */
    @SuppressFBWarnings(value = "UPM_UNCALLED_PRIVATE_METHOD", justification = "SpotBugs cannot detect private method calls in interfaces")
    @Nullable
    private String getCurrencyId(@Nullable final Currency currency) {
        return currency == null ? null : currency.getId();
    }
}
