/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.ips.gw;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.client.ClientHttpRequestInitializer;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryConfiguration;
import cz.finshape.vb.client.restclient.ips.gw.IpsGwIntegrationApiConnector;
import cz.finshape.vb.client.restclient.ips.gw.IpsGwIntegrationApiConnectorImpl;
import cz.finshape.vb.client.restclient.ips.handler.IpsGwRestExchangeHandler;

import cz.bsc.commons.client.restclient.RestClientFactory;

/**
 * IPS Integration API connector configuration.
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(value = "enabled", prefix = IpsIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX, matchIfMissing = true)
@EnableConfigurationProperties(IpsIntegrationApiConnectorFactoryProperties.class)
@Import({
    IpsGwRestExchangeHandler.class
})
public class IpsIntegrationApiConnectorFactoryConfiguration extends AbstractConnectorFactoryConfiguration {

    /**
     * Primary constructor.
     *
     * @param factory RestClient factory
     * @param requestInitializer correlation ID request initializer
     * @param properties properties
     */
    IpsIntegrationApiConnectorFactoryConfiguration(final RestClientFactory factory,
                                                   final ClientHttpRequestInitializer requestInitializer,
                                                   final IpsIntegrationApiConnectorFactoryProperties properties) {

        super(factory, requestInitializer, properties.getFactory());
    }

    /**
     * IPS GW Integration API connector bean.
     *
     * @param properties properties
     * @return connector bean
     */
    @Bean
    IpsGwIntegrationApiConnector ipsGwIntegrationApiConnector(
        final IpsIntegrationApiConnectorFactoryProperties properties,
        final IpsGwRestExchangeHandler exchangeHandler) {

        return new IpsGwIntegrationApiConnectorImpl(restClient, properties, exchangeHandler);
    }
}
