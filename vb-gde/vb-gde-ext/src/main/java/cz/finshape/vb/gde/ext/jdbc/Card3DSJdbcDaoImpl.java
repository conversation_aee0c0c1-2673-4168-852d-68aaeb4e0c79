/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.jdbc;


import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.simple.JdbcClient;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import cz.finshape.gde.core.persistence.dao.AbstractJdbcDao;
import cz.finshape.vb.domain.internal.gde.Card3DSAuthorization;
import lombok.extern.slf4j.Slf4j;


/**
 * DAO implementation.
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class Card3DSJdbcDaoImpl extends AbstractJdbcDao<Card3DSAuthorization, Long> implements Card3DSJdbcDao {

    private final TransactionTemplate transactionTemplate;

    /**
     *
     * @param jdbcClient jdbc client
     * @param transactionTemplate transaction template
     */
    public Card3DSJdbcDaoImpl(final JdbcClient jdbcClient, final TransactionTemplate transactionTemplate) {
        super(jdbcClient);
        this.transactionTemplate = transactionTemplate;
    }

    @Override
    public Optional<Card3DSAuthorization> getByCertificationRequestHash(final String certReqHash) {
        return transactionTemplate.execute(tx -> {
            try {
                final Map<String, Object> objectMap = getJdbcClient()
                    .sql("SELECT * FROM CARD_3DS_AUTHORIZATION WHERE CERT_REQ_HASH = :certReqHash")
                    .param("certReqHash", certReqHash).query().singleRow();

                return Optional.of(new Card3DSAuthorization(
                    (String) objectMap.get("ID"),
                    (String) objectMap.get("TASK_ID"),
                    (String) objectMap.get("CID"),
                    (String) objectMap.get("CERT_REQ_ID"),
                    (String) objectMap.get("CERT_REQ_HASH"),
                    (String) objectMap.get("USER_ID"),
                    null,
                    null,
                    (String) objectMap.get("CERT_RESULT"),
                    (String) objectMap.get("AUTH_RESULT")
                ));
            } catch (EmptyResultDataAccessException e) {
                log.debug("No card 3DS authorization found for certification request hash: {}", certReqHash);
                return Optional.empty();
            }
        });
    }

    @Override
    public void update(final String certReqId, final String authResult, final String certResult) {
        transactionTemplate.execute(tx -> {
            getJdbcClient()
                .sql(
                    "UPDATE CARD_3DS_AUTHORIZATION SET AUTH_RESULT = :authResult, CERT_RESULT = :certResult, FINISHED = :finished WHERE "
                        + "CERT_REQ_ID = :id")
                .param("id", certReqId)
                .param("authResult", authResult)
                .param("certResult", certResult)
                .param("finished", LocalDateTime.now())
                .update();
            return null;
        });
    }
}
