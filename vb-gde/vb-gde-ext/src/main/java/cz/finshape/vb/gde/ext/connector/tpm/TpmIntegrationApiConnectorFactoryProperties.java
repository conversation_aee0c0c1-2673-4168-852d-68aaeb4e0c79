/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.tpm;


import org.springframework.boot.context.properties.ConfigurationProperties;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryProperties;
import cz.finshape.vb.gde.ext.connector.VbGdeConnectorAutoConfiguration;
import lombok.Getter;


/**
 * TPM Integration API connector factory properties.
 *
 * <AUTHOR>
 */
@Getter
@ConfigurationProperties(TpmIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX)
class TpmIntegrationApiConnectorFactoryProperties extends AbstractConnectorFactoryProperties {

    static final String INTEGRATION_API_PREFIX = VbGdeConnectorAutoConfiguration.PREFIX + ".tpm.integration-api";

    /**
     * Primary constructor.
     */
    TpmIntegrationApiConnectorFactoryProperties() {
        super(INTEGRATION_API_PREFIX);
    }
}
