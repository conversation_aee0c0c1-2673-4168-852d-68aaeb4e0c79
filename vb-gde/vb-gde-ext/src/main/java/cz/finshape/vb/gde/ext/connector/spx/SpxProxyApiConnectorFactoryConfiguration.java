/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.spx;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInitializer;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryConfiguration;
import cz.finshape.vb.client.restclient.spx.SpxProxyApiConnector;
import cz.finshape.vb.client.restclient.spx.SpxProxyApiConnectorImpl;

import cz.bsc.commons.client.restclient.RestClientFactory;


/**
 * SPX Proxy API connector configuration.
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(value = "enabled", prefix = SpxProxyApiConnectorFactoryProperties.PROXY_API_PREFIX, matchIfMissing = true)
@EnableConfigurationProperties(SpxProxyApiConnectorFactoryProperties.class)
public class SpxProxyApiConnectorFactoryConfiguration extends AbstractConnectorFactoryConfiguration {

    /**
     * Primary constructor.
     *
     * @param factory RestClient factory
     * @param requestInitializer request initializer
     * @param properties properties
     */
    SpxProxyApiConnectorFactoryConfiguration(final RestClientFactory factory,
                                             final ClientHttpRequestInitializer requestInitializer,
                                             final SpxProxyApiConnectorFactoryProperties properties) {

        super(factory, requestInitializer, properties.getFactory());
    }

    /**
     * SPX Proxy API connector bean.
     *
     * @param properties properties
     * @return connector bean
     */
    @Bean
    SpxProxyApiConnector spxProxyClient(final SpxProxyApiConnectorFactoryProperties properties) {

        return new SpxProxyApiConnectorImpl(restClient, properties);
    }
}
