/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.gps;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInitializer;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryConfiguration;
import cz.finshape.vb.client.restclient.gps.GpsIntegrationApiConnector;
import cz.finshape.vb.client.restclient.gps.GpsIntegrationApiConnectorImpl;

import cz.bsc.commons.client.restclient.RestClientFactory;


/**
 * GPS Integration API connector configuration.
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(value = "enabled", prefix = GpsIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX, matchIfMissing = true)
@EnableConfigurationProperties(GpsIntegrationApiConnectorFactoryProperties.class)
public class GpsIntegrationApiConnectorFactoryConfiguration extends AbstractConnectorFactoryConfiguration {

    /**
     * Primary constructor.
     *
     * @param factory RestClient factory
     * @param requestInitializer request initializer
     * @param properties properties
     */
    GpsIntegrationApiConnectorFactoryConfiguration(final RestClientFactory factory,
                                                   final ClientHttpRequestInitializer requestInitializer,
                                                   final GpsIntegrationApiConnectorFactoryProperties properties) {

        super(factory, requestInitializer, properties.getFactory());
    }

    /**
     * GPS Integration API connector bean.
     *
     * @param properties properties
     * @return connector bean
     */
    @Bean
    GpsIntegrationApiConnector gpsIntegrationApiConnector(final GpsIntegrationApiConnectorFactoryProperties properties) {
        return new GpsIntegrationApiConnectorImpl(restClient, properties.getPropertiesPrefix(), properties.getFactory());
    }
}
