/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.mapper;


import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;

import cz.finshape.vb.core.utils.ClientUtils;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.gds.Currency;
import cz.finshape.vb.domain.dbos.gds.Movement;
import cz.finshape.vb.domain.dbos.gds.MovementChannel;
import cz.finshape.vb.domain.dbos.gds.MovementCharge;
import cz.finshape.vb.domain.dbos.gds.MovementCreditType;
import cz.finshape.vb.domain.dbos.gds.MovementDirection;
import cz.finshape.vb.domain.dbos.gds.MovementGroupType;
import cz.finshape.vb.domain.dbos.gds.MovementMerchantGroupType;
import cz.finshape.vb.domain.dbos.gds.MovementMerchantType;
import cz.finshape.vb.domain.dbos.gds.MovementPriority;
import cz.finshape.vb.domain.dbos.gds.MovementStatus;
import cz.finshape.vb.domain.dbos.gds.MovementType;
import cz.finshape.vb.domain.dbos.gds.ResidencyType;
import cz.finshape.vb.domain.internal.cbs.CbsAccountGroup;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException;
import cz.finshape.vb.domain.internal.gde.rabbitmq.AccountMovementPushRequest;
import cz.finshape.vb.domain.internal.gds.GdsCatalogValues;
import cz.finshape.vb.gde.ext.service.AccountService;
import lombok.extern.slf4j.Slf4j;


/**
 * Mapper provide conversion between {@link AccountMovementPushRequest} and {@link Movement}
 * Some info how to map can be found in confluence
 * <a href="https://cz-support.finshape.com/confl/display/P20009724/Movements+-+Technical+analysis">Movement
 * technical analysis</a>
 *
 * <AUTHOR> Sarana
 */
@Slf4j
@Mapper(componentModel = "spring")
public abstract class AccountMovementMapper implements BaseMapper {

    public static final List<String> MASKED_PARTNER_NUMBER_MOVEMENT_TYPES = List.of("IPS_P2P", "IPS_RTP", "IPS_QRC", "IPS_M2M");

    @Autowired
    private AccountService accountService;

    /**
     * 1st update order for account holds
     */
    private static final int UPDATE_ORDER_FIRST_HOLD = 1;

    /**
     * 2nd update order for movements - provides hint via some aspects for GDS, to store this record in database and replace records marked
     * with 1st update order from account holds spx endpoint
     */
    private static final int UPDATE_ORDER_SECOND_MOVEMENT = 2;

    /**
     * 3rd update order for reversals
     */
    private static final int UPDATE_ORDER_THIRD_REVERSAL = 3;

    /**
     * Maps list of {@link AccountMovementPushRequest} to list of {@link Movement} entities for GDS
     *
     * @param accountMovementPushRequests list of {@link AccountMovementPushRequest} to be mapped to {@link Movement}
     * @return list of {@link Movement} entities for GFS
     */
    public abstract List<Movement> toMovementList(List<AccountMovementPushRequest> accountMovementPushRequests);

    /**
     * Maps {@link AccountMovementPushRequest} to {@link Movement} entity for GDS
     *
     * @param accountMovementPushRequest {@link AccountMovementPushRequest} to be mapped to {@link Movement}
     * @return {@link Movement} entity for GDS
     */
    @Mapping(target = Movement.FN_ACCOUNT, source = "accountMovementPushRequest", qualifiedByName = "toAccount")
    @Mapping(target = Movement.FN_CARD, source = "accountMovementPushRequest", qualifiedByName = "toCard")
    @Mapping(target = Movement.FN_RELATEDACCOUNT, source = "relatedAccount")
    @Mapping(target = Movement.FN_CURRENCY, source = "currency", qualifiedByName = "toCurrency")
    @Mapping(target = Movement.FN_BONUSCURRENCY, source = "bonusCurrency", qualifiedByName = "toCurrency")
    @Mapping(target = Movement.FN_STATUS, source = "status", qualifiedByName = "toClientStatus")
    @Mapping(target = Movement.FN_TYPE, source = "type", qualifiedByName = "toType")
    @Mapping(target = Movement.FN_CHANNEL, source = "channel", qualifiedByName = "toChannel")
    @Mapping(target = Movement.FN_MERCHANTTYPE, source = "merchantType", qualifiedByName = "toMerchantType")
    @Mapping(target = Movement.FN_PRIORITY, source = "priority", qualifiedByName = "toPriority")
    @Mapping(target = Movement.FN_DIRECTION, source = "direction", qualifiedByName = "toDirection")
    @Mapping(target = Movement.FN_CREDITMOVEMENTTYPE, source = "creditMovementType", qualifiedByName = "toCreditMovementType")
    @Mapping(target = Movement.FN_MERCHANTGROUPTYPE, source = "merchantGroupType", qualifiedByName = "toMerchantGroupType")
    @Mapping(target = Movement.FN_PARTNERRESIDENCY, source = "partnerResidency", qualifiedByName = "toResidencyType")
    @Mapping(target = Movement.FN_PARTNERNAME, source = "accountMovementPushRequest", qualifiedByName = "toPartnerName")
    @Mapping(target = Movement.FN_PARTNERCARDMASKEDNUMBER, source = "partnerCardMaskedNumber")
    @Mapping(target = Movement.FN_CHARGE, source = "charge", qualifiedByName = "toCharge")
    @Mapping(target = Movement.FN_UPDATEORDER, source = "status", qualifiedByName = "statusToUpdateOrder")
    @Mapping(target = Movement.FN_PAYMENTREFERENCE, source = "rrn")
    @Mapping(target = Movement.FN_PARTNERCOUNTRY, source = "partnerCountry")
    @Mapping(target = Movement.FN_PARTNERBANKCOUNTRY, source = "partnerBankCountry")
    @Mapping(target = Movement.FN_PAYMENTORDERNO, source = "paymentOrderNo")
    @Mapping(target = Movement.FN_TRANSACTIONCODE, source = "transactionCode")
    @Mapping(target = Movement.FN_TARGETCURRENCY, source = "targetCurrency", qualifiedByName = "toCurrency")
    @Mapping(target = Movement.FN_AMOUNTTEXT, source = "amountText")
    @Mapping(target = Movement.FN_PARTNERFISCALCODE, source = "partnerId")
    @Mapping(target = Movement.FN_CREATEDAT, source = "createdAt", qualifiedByName = "asMoldovanOffsetDateTime")
    @Mapping(target = Movement.FN_SEARCHDATA, ignore = true) // filled via @AfterMapping
    public abstract Movement toMovement(AccountMovementPushRequest accountMovementPushRequest);

    /**
     * Maps {@link AccountMovementPushRequest} to {@link Account} entity for GDS via joining the source system and bisAccountId
     *
     * @param accountMovementPushRequest {@link AccountMovementPushRequest} to be mapped to {@link Account}
     * @return {@link Account} entity for GDS
     */
    @Named("toAccount")
    @Nullable
    protected Account toAccount(@Nonnull final AccountMovementPushRequest accountMovementPushRequest) {
        if (accountMovementPushRequest.getBisAccountId() == null) {
            throw new VbBusinessException(VbErrorCodeEnum.ARGUMENT_EMPTY, "Missing account");
        }
        if (CbsAccountGroup.isInternal(accountMovementPushRequest.getAccountGroup())) {
            final var iban = accountMovementPushRequest.getIban();
            log.debug("Account '{}' has account group as internal, approaching to find the master account by IBAN : {}",
                accountMovementPushRequest.getBisAccountId(), iban);
            return accountService.findMasterAccount(iban);
        } else {
            log.debug("Account '{}' has account group not as internal", accountMovementPushRequest.getBisAccountId());
            return new Account(accountMovementPushRequest.getSource().withPrefix(accountMovementPushRequest.getBisAccountId()));
        }
    }

    /**
     * Maps {@link AccountMovementPushRequest} to {@link Card} entity for GDS
     *
     * @param accountMovementPushRequest {@link AccountMovementPushRequest} to be mapped to {@link Card}
     * @return {@link Card} entity for GDS
     */
    @Named("toCard")
    @Nullable
    protected Card toCard(@Nonnull final AccountMovementPushRequest accountMovementPushRequest) {
        if (accountMovementPushRequest.getCard() == null) {
            return null;
        }
        return new Card(accountMovementPushRequest.getCard());
    }

    /**
     * Convert currency code to {@link Currency} entity from GDS
     *
     * @param currency currency code
     * @return {@link Currency} entity from GDS
     */
    @Named("toCurrency")
    protected Currency toCurrency(final String currency) {
        if (currency == null) {
            return null;
        }

        return new Currency(currency);
    }

    /**
     * <pre>
     * +-----------+---+
     * |   Status  | # |
     * +-----------+---+
     * |  waiting  | 1 |
     * | processed | 2 |
     * | unblocked | 2 |
     * |  reversed | 3 |
     * +-----------+---+
     * </pre>
     * @param status from movement request
     * @return numerical status
     */
    @Named("statusToUpdateOrder")
    protected int statusToUpdateOrder(String status) {
        if (status == null) {
            throw new IllegalArgumentException("Empty movement status");
        }

        return switch (GdsCatalogValues.MovementStatusValue.valueOf(status.toUpperCase(Locale.getDefault()))) {
            case WAITING -> UPDATE_ORDER_FIRST_HOLD;
            case PROCESSED, UNBLOCKED -> UPDATE_ORDER_SECOND_MOVEMENT;
            case REVERSED -> UPDATE_ORDER_THIRD_REVERSAL;
        };
    }

    /**
     * Prepare and fill search data field on Movement. Those are later indexed and FE clients search through it quickly
     * <a href="https://cz-support.finshape.com/confl/display/P20009724/Movements+-+Technical+analysis#MovementsTechnicalanalysis-Search">Search data definition</a>
     *
     * @param target target object
     * @param source source object
     */
    @AfterMapping
    protected void prepareSearchData(@MappingTarget Movement target, AccountMovementPushRequest source) {
        final var searchData = Stream.of(
                target.getPartnerName(),
                source.getPartnerIban(),
                source.getRelatedAccount(), // partner IBAN is null for forex and ips movements and in that case we use relatedAccount
                source.getPartnerCardMaskedNumber(),
                target.getPartnerPhoneNumber(),
                target.getPartnerFiscalCode(),
                target.getMerchantName(),
                target.getContractId(),
                target.getComment(),
                target.getUtilityProviderName(),
                target.getDescription(),
                target.getRemittanceInformation())
            .filter(Objects::nonNull)
            .filter(Predicate.not(String::isBlank))
            .collect(Collectors.joining(StringUtils.SPACE));

        target.setSearchData(searchData);
    }

    /**
     * Converts a status string to a MovementStatus object.
     *
     * @param status the status string to be converted
     * @return the MovementStatus object representing the status, or null if the input status is null
     */
    @Named("toClientStatus")
    protected MovementStatus toStatus(final String status) {
        if (status == null) {
            return null;
        }

        return new MovementStatus(status);
    }

    /**
     * Converts the given type string to a MovementType object.
     *
     * @param type the type string to be converted
     * @return the MovementType object representing the type, or null if the input type is null
     */
    @Named("toType")
    protected MovementType toType(final String type) {
        if (type == null) {
            return null;
        }

        return new MovementType(type);
    }

    /**
     * Converts the given type string to a MovementChannel object.
     *
     * @param channel the type string to be converted
     * @return the MovementChannel object representing the type, or null if the input type is null
     */
    @Named("toChannel")
    protected MovementChannel toChannel(final String channel) {
        if (channel == null) {
            return null;
        }

        return new MovementChannel(channel);
    }

    /**
     * Converts the given type string to a MovementMerchantType object.
     *
     * @param merchantType the type string to be converted
     * @return the MovementMerchantType object representing the type, or null if the input type is null
     */
    @Named("toMerchantType")
    protected MovementMerchantType toMerchantType(final String merchantType) {
        if (merchantType == null) {
            return null;
        }

        return new MovementMerchantType(merchantType);
    }

    /**
     * Converts the given type string to a MovementGroupType object.
     *
     * @param groupType the type string to be converted
     * @return the MovementGroupType object representing the type, or null if the input type is null
     */
    @Named("toGroupType")
    protected MovementGroupType toGroupType(final String groupType) {
        if (groupType == null) {
            return null;
        }

        return new MovementGroupType(groupType);
    }

    /**
     * Converts the given type string to a MovementCharge object.
     *
     * @param charge the type string to be converted
     * @return the MovementCharge object representing the type, or null if the input type is null
     */
    @Named("toCharge")
    protected MovementCharge toCharge(final String charge) {
        if (charge == null) {
            return null;
        }

        return new MovementCharge(charge);
    }

    /**
     * Converts the given type string to a MovementPriority object.
     *
     * @param priority the type string to be converted
     * @return the MovementPriority object representing the type, or null if the input type is null
     */
    @Named("toPriority")
    protected MovementPriority toPriority(final String priority) {
        if (priority == null) {
            return null;
        }

        return new MovementPriority(priority);
    }

    /**
     * Converts the given type string to a MovementDirection object.
     *
     * @param direction the type string to be converted
     * @return the MovementDirection object representing the type, or null if the input type is null
     */
    @Named("toDirection")
    protected MovementDirection toDirection(final String direction) {
        if (direction == null) {
            return null;
        }

        return new MovementDirection(direction);
    }

    /**
     * Converts the given type string to a MovementCreditType object.
     *
     * @param creditMovementType the type string to be converted
     * @return the MovementCreditType object representing the type, or null if the input type is null
     */
    @Named("toCreditMovementType")
    protected MovementCreditType toCreditMovementType(final String creditMovementType) {
        if (creditMovementType == null) {
            return null;
        }

        return new MovementCreditType(creditMovementType);
    }

    /**
     * Converts the given type string to a MovementMerchantGroupType object.
     *
     * @param merchantGroupType the type string to be converted
     * @return the MovementMerchantGroupType object representing the type, or null if the input type is null
     */
    @Named("toMerchantGroupType")
    protected MovementMerchantGroupType toMerchantGroupType(final String merchantGroupType) {
        if (merchantGroupType == null) {
            return null;
        }

        return new MovementMerchantGroupType(merchantGroupType);
    }


    /**
     * Converts the given type string to a PartnerResidencyType object.
     *
     * @param partnerResidencyType the type string to be converted
     * @return the PartnerResidencyType object representing the type, or null if the input type is null
     */
    @Named("toResidencyType")
    protected ResidencyType toResidencyType(final String partnerResidencyType) {
        if (partnerResidencyType == null) {
            return null;
        }

        return new ResidencyType(partnerResidencyType);
    }

    /**
     * Masks the partner name to the first name and the first letter of the last name.
     * BUT only for debit movements with the partner number and the type in the list of {@link #MASKED_PARTNER_NUMBER_MOVEMENT_TYPES}
     * @param movement movement with the partner name to be masked
     * @return the masked partner name
     */
    @Named("toPartnerName")
    protected String toPartnerName(final AccountMovementPushRequest movement) {
        if (movement.getPartnerName() != null) {
            final var partnerName = movement.getPartnerName();
            if (MASKED_PARTNER_NUMBER_MOVEMENT_TYPES.contains(movement.getType())) {
                return ClientUtils.shortenName(partnerName);
            }
            return partnerName;
        } else {
            // If no partner name is available, use other data.
            return Optional
                .ofNullable(movement.getMerchantName())
                .or(() -> Optional.ofNullable(movement.getPartnerIban()))
                .orElseGet(() -> {
                    log.warn("No data found to map into 'partnerName', movement ID : {}", movement.getId());
                    return null;
                });
        }
    }
}
