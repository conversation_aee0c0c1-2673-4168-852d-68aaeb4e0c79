/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.mapper;


import java.util.List;

import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;

import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.core.client.auth.IdmSystem;
import cz.finshape.vb.core.client.dbos.GraphQLParams;
import cz.finshape.vb.core.utils.TemporalUtils;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.AccountType;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.gds.CardForm;
import cz.finshape.vb.domain.dbos.gds.CardIssuer;
import cz.finshape.vb.domain.dbos.gds.CardIssuerType;
import cz.finshape.vb.domain.dbos.gds.CardIssuerTypeMapping;
import cz.finshape.vb.domain.dbos.gds.CardProductType;
import cz.finshape.vb.domain.dbos.gds.CardStatus;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.Currency;
import cz.finshape.vb.domain.dbos.gds.User;
import cz.finshape.vb.domain.internal.cbs.CbsAccountGroup;
import cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum;
import cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException;
import cz.finshape.vb.domain.internal.core.system.ExternalSystem;
import cz.finshape.vb.domain.internal.spx.way4native.SpxCardLimitJsonResponse;
import cz.finshape.vb.domain.thirdparty.generated.way4.Way4Card;
import cz.finshape.vb.domain.thirdparty.generated.way4.Way4CardsResponse;
import cz.finshape.vb.gde.ext.service.AccountService;
import lombok.extern.slf4j.Slf4j;


/**
 * Mapper for SPX Card.
 *
 * <AUTHOR> Cernil
 */
@Slf4j
@Mapper(componentModel = "spring")
public abstract class SpxCardsMapper {

    @Autowired
    private AccountService accountService;

    @Autowired
    private GdsIntegrationApiConnector gdsIntegrationApiConnector;

    /**
     * Maps Way4 Account response into a list of GDS accounts.
     *
     * @param way4AccountsResponse Way4 Account response
     * @return List of GDS accounts
     */
    public List<Card> way4CardsResponseToGdsCards(final Way4CardsResponse way4AccountsResponse) {
        final var cardIssuerTypeMappings = gdsIntegrationApiConnector.<CardIssuerTypeMapping>getEntitiesCacheable(
            CardIssuerTypeMapping.EN_PLURAL,
            GraphQLParams.builder().build());

        return way4AccountsResponse.getCards()
                .stream()
                .map(way4Card -> toCard(way4Card, cardIssuerTypeMappings))
                .toList();
    }

    /**
     * Adds Way4 Card Limit into GDS Card.
     *
     * @param card GDS card
     * @param limits Way4 card limit
     */
    public void addCardLimits(final Card card, final SpxCardLimitJsonResponse limits) {
        card.setInternetAllowedFlag(limits.getInternetAllowedFlag());
        card.setInternetAllowedFlagDisabledByOwner(limits.getInternetAllowedFlagDisabledByOwner());

        card.setInternetAbroadAllowedFlag(limits.getInternetAbroadAllowedFlag());
        card.setInternetAbroadAllowedFlagDisabledByOwner(limits.getInternetAbroadAllowedFlagDisabledByOwner());

        if (limits.getAllOperationAbroad() != null) {
            card.setAllOperationAbroadLimitAmount(limits.getAllOperationAbroad().getAmount());
            card.setAllOperationAbroadLimitCurrency(new Currency(limits.getAllOperationAbroad().getCurrency()));
            card.setAllOperationAbroadLimitStatus(limits.getAllOperationAbroad().getStatus());
        } else {
            log.warn("The card.id={} has a null limit : all operations abroad", limits.getCardId());
        }

        if (limits.getAllRetailOperation() != null){
            card.setAllRetailOperationLimitAmount(limits.getAllRetailOperation().getAmount());
            card.setAllRetailOperationLimitCurrency(new Currency(limits.getAllRetailOperation().getCurrency()));
            card.setAllRetailOperationLimitStatus(limits.getAllRetailOperation().getStatus());
        } else {
            log.warn("The card.id={} has a null limit : all retail operation", limits.getCardId());
        }

        if (limits.getAtmRm() != null) {
            card.setAtmRmLimitAmount(limits.getAtmRm().getAmount());
            card.setAtmRmLimitCurrency(new Currency(limits.getAtmRm().getCurrency()));
            card.setAtmRmLimitStatus(limits.getAtmRm().getStatus());
        } else {
            log.warn("The card.id={} has a null limit : ATM RM", limits.getCardId());
        }

        if (limits.getAtmAbroad() != null) {
            card.setAtmAbroadLimitAmount(limits.getAtmAbroad().getAmount());
            card.setAtmAbroadLimitCurrency(new Currency(limits.getAtmAbroad().getCurrency()));
            card.setAtmAbroadLimitStatus(limits.getAtmAbroad().getStatus());
        } else {
            log.warn("The card.id={} has a null limit : ATM abroad", limits.getCardId());
        }
    }

    /**
     * Map Way4 Card into GDS Card.
     *
     * @param way4Card Way4 card
     * @param cardIssuerTypeMappings card issuer type mappings
     * @return GDS card {@link Card}
     */
    @Mapping(target = Card.FN_ID, source = "way4Card.cardId")
    @Mapping(target = Card.FN_MASKEDNUMBER, source = "way4Card.maskedNumber")
    @Mapping(target = Card.FN_OWNER, source = "way4Card.accountOwner.id", qualifiedByName = "toClient")
    @Mapping(target = Card.FN_ACCOUNT, source = "way4Card.account", qualifiedByName = "toAccount")
    @Mapping(target = Card.FN_HOLDER, source = "way4Card.holderFiscalCode", qualifiedByName = "toUser")
    @Mapping(target = Card.FN_HOLDERNAME, source = "way4Card.holderName")
    @Mapping(target = Card.FN_ISSUER, source = "way4Card.issuer", qualifiedByName = "toCardIssuer")
    @Mapping(target = Card.FN_STATUS, source = "way4Card.status", qualifiedByName = "toCardStatus")
    @Mapping(target = Card.FN_PRODUCTTYPE, source = "way4Card.cardType", qualifiedByName = "toCardProductType")
    @Mapping(target = Card.FN_FORM, source = "way4Card", qualifiedByName = "toCardForm")
    @Mapping(target = Card.FN_ISSUERTYPE, source = "way4Card", qualifiedByName = "toCardIssuerType")
    @Mapping(target = Card.FN_ISREISSUABLE, source = "way4Card", qualifiedByName = "toIsReissuable")
    @Mapping(target = Card.FN_RBS, source = "way4Card.account.rbs")
    @Mapping(target = Card.FN_CURRENCY, source = "way4Card.account.currency", qualifiedByName = "toCurrency")
    public abstract Card toCard(Way4Card way4Card, @Context List<CardIssuerTypeMapping> cardIssuerTypeMappings);

    /**
     * Mapping for client from {@link String} to GDS {@link Client}.
     * @param clientId as {@link String}
     * @return client as {@link Client}
     */
    @Named("toClient")
    protected Client toClient(final String clientId) {
        return new Client(clientId);
    }

    /**
     * Mapping for account from {@link String} to GDS {@link Account}.
     * From discussion in Tech chat with Alexandru Mitcul:
     * <i>"I analyzed the code of the program and, yes, from what I saw, the colleague who developed this API only referred to Way4 accounts"</i>
     *
     * @param account as {@link Way4Card.Account}
     * @return account as {@link Account}
     */
    @Named("toAccount")
    protected Account toAccount(final Way4Card.Account account) {
        if (account == null || account.getBisAccountId() == null) {
            throw new VbBusinessException(VbErrorCodeEnum.ARGUMENT_EMPTY, "Missing account");
        }
        if (CbsAccountGroup.isInternal(account.getAccountGroup())) {
            final var iban = account.getIban();
            log.debug("Account '{}' has account group as internal, approaching to find the master account by IBAN : {}",
                account.getBisAccountId(), iban);
            return accountService.findMasterAccount(iban);
        } else {
            log.debug("Account '{}' has account group not as internal", account.getBisAccountId());
            return new Account(ExternalSystem.WAY4.withPrefix(account.getBisAccountId()));
        }
    }

    /**
     * Mapping for user from {@link String} to GDS {@link User}.
     * @param userId as {@link String}
     * @return user as {@link User}
     */
    @Named("toUser")
    protected User toUser(final String userId) {
        return new User(IdmSystem.withPrefix(userId));
    }

    /**
     * Mapping for holder name from {@link String} to GDS {@link String}.
     * @param holderFiscalCode as {@link String}
     * @return holder name as {@link String}
     */
    @Named("toHolderName")
    protected String toHolderName(final String holderFiscalCode) {
        return IdmSystem.withPrefix(holderFiscalCode);
    }

    /**
     * Mapping for card issuer from {@link String} to GDS {@link CardIssuer}.
     * @param issuerId as {@link String}
     * @return card issuer as {@link CardIssuer}
     */
    @Named("toCardIssuer")
    protected CardIssuer toCardIssuer(final String issuerId) {
        return new CardIssuer(issuerId);
    }

    /**
     * Mapping for card type from {@link String} to GDS {@link CardProductType}.
     * @param productType as {@link String}
     * @return card type as {@link CardProductType}
     */
    @Named("toCardProductType")
    protected CardProductType toCardProductType(final String productType) {
        return new CardProductType(new AccountType(productType));
    }

    /**
     * Mapping for card status from {@link String} to GDS {@link CardStatus}.
     * @param cardStatus as {@link String}
     * @return card status as {@link CardStatus}
     */
    @Named("toCardStatus")
    protected CardStatus toCardStatus(final String cardStatus) {
        return new CardStatus(cardStatus);
    }

    /**
     * Mapping for card form from {@link Way4Card} to GDS {@link CardForm}.
     * @param card card as {@link Way4Card}
     * @return card type as {@link CardForm}
     */
    @Named("toCardForm")
    protected CardForm toCardForm(Way4Card card) {
        return new CardForm(card.getCardProfile());
    }

    /**
     * Mapping for card form from {@link Way4Card} to GDS {@link CardIssuerType}.
     * @param way4Card card as {@link Way4Card}
     * @param cardIssuerTypeMappings card issuer type mappings
     * @return card type as {@link CardIssuerType}
     */
    @Named("toCardIssuerType")
    protected CardIssuerType toCardIssuerType(Way4Card way4Card, @Context final List<CardIssuerTypeMapping> cardIssuerTypeMappings) {
        final var issuer = way4Card.getIssuer();
        final var cardIssuerType = way4Card.getCardIssuerType();

        final var optionalCardIssuerType = cardIssuerTypeMappings.stream()
            .filter(mapping -> mapping.getIssuer() != null && mapping.getIssuer().getId() != null && mapping.getIssuer().getId().equals(issuer))
            .filter(mapping -> mapping.getCbsIssuerType() != null && mapping.getCbsIssuerType().equals(cardIssuerType))
            .findFirst();

        if (optionalCardIssuerType.isPresent()) {
            return optionalCardIssuerType.get().getCardIssuerType();
        } else {
            log.error("No CardIssuerTypeMapping found for issuer '{}' and cardIssuerType '{}'", issuer, cardIssuerType);
            return null;
        }
    }

    /**
     * Mapping for card form from {@link Way4Card} to GDS {@link Card#getIsReissuable()}.
     *
     * @see <a href="https://cz-support.finshape.com/confl/display/P20009724/Technical+Analysis+-+Reissue+Card+-+WIP#TechnicalAnalysisReissueCardWIP-ReissueCardFlagPost-Replication">Confluence</a>
     *
     * @param card card as {@link Way4Card}
     * @return card type as {@link Card#getIsReissuable()}
     */
    @Named("toIsReissuable")
    protected Boolean toIsReissuable(final Way4Card card) {
        final var expiryDate = card.getExpiryDate();
        if (expiryDate == null) {
            log.error("Cannot determine 'isReissuable' field based on null 'expiryDate', skipping...");
            return null;
        }

        final var now = TemporalUtils.nowInMoldova().toLocalDate();
        final var lowerBound = expiryDate.minusDays(30);
        final var upperBound = expiryDate.plusDays(30);

        final var isReissuable =  !now.isBefore(lowerBound) && !now.isAfter(upperBound);
        log.debug("Determined 'isReissuable' based on expiryDate={}, calculation : {} <= {} <= {} : {}",
            expiryDate, lowerBound, now, upperBound, isReissuable);
        return isReissuable;
    }

    /**
     * Mapping for currency from {@link String} to GDS {@link Currency}.
     * @param currency as {@link String}
     * @return currency as {@link Currency}
     */
    @Named("toCurrency")
    protected Currency toCurrency(final String currency) {
        return new Currency(currency);
    }
}
