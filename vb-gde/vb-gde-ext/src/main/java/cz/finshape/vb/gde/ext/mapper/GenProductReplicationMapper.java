/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.mapper;


import static java.text.MessageFormat.format;
import static cz.finshape.vb.domain.internal.core.exception.VbErrorCodeEnum.GDS_ENTITY_NOT_VALID;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.springframework.beans.factory.annotation.Autowired;

import cz.finshape.vb.client.restclient.gds.GdsIntegrationApiConnector;
import cz.finshape.vb.core.client.auth.IdmSystem;
import cz.finshape.vb.core.client.gen.domain.ReferenceReplicationRequest;
import cz.finshape.vb.core.client.gen.domain.ReplicationAssignProductsRequest.ReplicationAssignProductAssignment;
import cz.finshape.vb.core.client.gen.domain.ReplicationProduct;
import cz.finshape.vb.domain.dbos.gds.Account;
import cz.finshape.vb.domain.dbos.gds.AccountType;
import cz.finshape.vb.domain.dbos.gds.Card;
import cz.finshape.vb.domain.dbos.gds.Client;
import cz.finshape.vb.domain.dbos.gds.User;
import cz.finshape.vb.domain.internal.core.exception.business.VbBusinessException;


/**
 * Mapper for GEN replications.
 *
 * <AUTHOR> Charalambidis
 */
@Mapper(componentModel = "spring")
public abstract class GenProductReplicationMapper implements BaseMapper {

    /**
     * GEN: Active product ID for GEN replication
     */
    public static final String GEN_ACTIVE_PRODUCT = "1";

    /**
     * GEN: Product assigment statuses - ACTIVE
     */
    public static final int GEN_ASSIGMENT_STATUS_ACTIVE = 1;

    /**
     * GEN: Product assigment statuses - INACTIVE
     */
    public static final int GEN_ASSIGMENT_STATUS_INACTIVE = 0;

    @Autowired
    protected GdsIntegrationApiConnector gdsIntegrationApiConnector;

    /**
     * Maps list of {@link Account} to list of {@link ReplicationProduct} for GEN product replication.
     *
     * @param accounts list of {@link Account} to be mapped to {@link ReplicationProduct}
     * @return list of {@link ReplicationProduct} for GEN product replication
     */
    public abstract List<ReplicationProduct> mapProducts(List<Account> accounts);

    /**
     * Maps a list of Cards to a list of EntityData for GEN reference replication.
     *
     * @param cards The list of Cards to be mapped to EntityData
     * @return list of EntityData for GEN reference replication
     */
    @Nonnull
    public List<ReferenceReplicationRequest.EntityData> mapReferencesToCreateOrUpdate(@Nonnull final List<Card> cards) {
        return cards.stream()
            .map(this::mapReferenceToCreateOrUpdate)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * Maps a list of Cards to a list of EntityIdentification for GEN reference replication.
     *
     * @param cards The list of Cards to be mapped to EntityIdentification
     * @return The list of EntityIdentification for GEN reference replication
     */
    public List<ReferenceReplicationRequest.EntityIdentification> mapReferencesToDeleted(final List<Card> cards) {
        return cards.stream()
            .map(this::mapReferenceToDeleted)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
    }

    /**
     * Maps {@link Account} to {@link ReplicationProduct} for GEN product replication.
     *
     * @param account {@link Account} to be mapped to {@link ReplicationProduct}
     * @return {@link ReplicationProduct} for GEN product replication
     */
    @Mapping(target = "externalProductId.productId", source = Account.FN_BISACCOUNTID)
    @Mapping(target = "externalProductId.authorizationEntityTypeId", constant = Account.EN_SINGULAR)
    @Mapping(target = "currencyId", source = Account.FN_CURRENCY + ".id")
    @Mapping(target = "productType", source="account", qualifiedByName = "toProductType")
    @Mapping(target = "productName", source="account", qualifiedByName = "toProductTypeName")
    @Mapping(target = "displayId", source = Account.FN_IBAN)
    @Mapping(target = "displayId2", ignore = true)
    @Mapping(target = "statusId", constant = GEN_ACTIVE_PRODUCT) // currently only use this constant until told otherwise for accounts
    @Mapping(target = "externalVersion", source = Account.FN_MODIFIEDAT)
    @Mapping(target = "externalClientId", source = Account.FN_OWNER + ".id")
    abstract ReplicationProduct toReplicationProduct(Account account);

    /**
     * Maps references to metadata.
     *
     * @param type GDS entity type purely to avoid method ambiguity
     * @return Map of metadata
     */
    @SuppressWarnings("unused")
    public Map<String, ReferenceReplicationRequest.EntityTypeMetadata> mapReferencesToMetadata(final Class<Card> type) {
        final var clientRelation = new ReferenceReplicationRequest.Relation();
        clientRelation.setEntityType(Client.EN_SINGULAR);

        final var userRelation = new ReferenceReplicationRequest.Relation();
        userRelation.setEntityType(User.EN_SINGULAR);

        final var accountRelation = new ReferenceReplicationRequest.Relation();
        accountRelation.setEntityType(Account.EN_SINGULAR);

        final var authorizationEntityAttributes = new ReferenceReplicationRequest.EntityTypeMetadata();
        authorizationEntityAttributes.getAuthorizationRelationAttributes().put("ClientId", clientRelation);
        authorizationEntityAttributes.getAuthorizationRelationAttributes().put("UserId", userRelation);
        authorizationEntityAttributes.getAuthorizationRelationAttributes().put("AccountId", accountRelation);

        final var entityMetadata = new HashMap<String, ReferenceReplicationRequest.EntityTypeMetadata>();
        entityMetadata.put(Card.EN_SINGULAR, authorizationEntityAttributes);
        return entityMetadata;
    }

    /**
     * Maps GDS {@link Card} into reference for deletion.
     *
     * @param card GDS card
     * @return reference
     */
    public ReferenceReplicationRequest.EntityIdentification mapReferenceToDeleted(final Card card) {
        if (card == null) {
            return null;
        }

        final var entityIdentification = new ReferenceReplicationRequest.EntityIdentification();
        entityIdentification.setEntityType(Card.EN_SINGULAR);
        entityIdentification.setEntityUniqueId(card.getId());
        return entityIdentification;
    }

    /**
     * Maps GDS {@link Card} into reference for create/update.
     * @param card GDS card
     * @return reference
     */
    @Nullable
    public ReferenceReplicationRequest.EntityData mapReferenceToCreateOrUpdate(@Nullable final Card card) {
        if (card == null) {
            return null;
        }

        final var entityIdentification = new ReferenceReplicationRequest.EntityIdentification();
        entityIdentification.setEntityType(Card.EN_SINGULAR);
        entityIdentification.setEntityUniqueId(card.getId());

        final var entityData = new ReferenceReplicationRequest.EntityData();
        entityData.setChildEntity(entityIdentification);
        entityData.getRelations().put("ClientId", card.getOwner().getId());
        entityData.getRelations().put("UserId", IdmSystem.withPrefix(card.getHolder().getId()));
        if (card.getAccount() != null) {
            entityData.getRelations().put("AccountId", card.getAccount().getBisAccountId());
        }
        return entityData;
    }

    /**
     * Maps list of {@link ReplicationProduct} to list of {@link ReplicationAssignProductAssignment} for GEN product assignment.
     *
     * @param replicationProducts list of {@link ReplicationProduct} to be mapped to {@link ReplicationAssignProductAssignment}
     * @return list of {@link ReplicationAssignProductAssignment} for GEN product assignment
     */
    public abstract List<ReplicationAssignProductAssignment> mapProductsToProductAssignmentList(List<ReplicationProduct> replicationProducts);

    /**
     * Maps {@link ReplicationProduct} to {@link ReplicationAssignProductAssignment} for GEN product assignment.
     *
     * @param p {@link ReplicationProduct} to be mapped to {@link ReplicationAssignProductAssignment}
     * @return {@link ReplicationAssignProductAssignment} for GEN product assignment
     */
    @Mapping(target = "statusId", expression = "java(mapAssigmentStatus(p))")
    @Mapping(target = "externalProductId", source = "externalProductId")
    abstract ReplicationAssignProductAssignment toProductAssignment(ReplicationProduct p);

    /**
     * Maps ReplicationProduct status assignment status.
     *
     * @param p {@link ReplicationProduct}
     * @return GEN_ASSIGMENT_STATUS_ACTIVE if product is active, GEN_ASSIGMENT_STATUS_INACTIVE otherwise
     */
    protected int mapAssigmentStatus(final ReplicationProduct p) {
        // this allows active operations with account to user
        if (p.getStatusId() == 1) {
            return GEN_ASSIGMENT_STATUS_ACTIVE;
        }

        // otherwise, the account is inactive and all operations are forbidden
        return GEN_ASSIGMENT_STATUS_INACTIVE;
    }

    /**
     * Get Account Type identifier
     *
     * @param account {@link Account}
     * @return {@link AccountType} identifier from GDS
     */
    @Named("toProductType")
    protected String toProductType(Account account) {
        if (account == null) {
            throw new VbBusinessException(GDS_ENTITY_NOT_VALID, "Account is null");
        }

        if (account.getBisAccountId() == null) {
            throw new VbBusinessException(GDS_ENTITY_NOT_VALID, "Account.bisAccountId is null");
        }

        final AccountType type = account.getType();
        if (type == null) {
            throw new VbBusinessException(GDS_ENTITY_NOT_VALID, format("Account[bisAccountId={0}].type is null", account.getBisAccountId()));
        }

        final String id = type.getId();
        if (id == null) {
            throw new VbBusinessException(GDS_ENTITY_NOT_VALID, format("Account[bisAccountId={0}].type.id is null", account.getBisAccountId()));
        }

        return id;
    }

    /**
     * Gets product name.
     *
     * @param account {@link Account}
     * @return product name
     */
    @Named("toProductTypeName")
    protected String toProductTypeName(final Account account) {
        final String productType = toProductType(account);
        return "Account Type " + productType;
    }

    /**
     * Gets product type name.
     *
     * @param card {@link Card}
     * @return product name
     */
    @Named("toProductTypeName")
    protected String toProductTypeName(final Card card) {
        return "Debit card";
    }

    /**
     * Gets product type.
     *
     * @param card {@link Card}
     * @return product type
     */
    @Named("toProductType")
    protected String toProductType(final Card card) {
        return "1";
    }

    /**
     * Gets account currency ID.
     *
     * @param card {@link Card}
     * @return currency ID
     */
    @Named("getAccountCurrencyId")
    protected String getAccountCurrencyId(Card card) {
        final String bisAccountId = card.getAccount().getBisAccountId();
        final Account acc = gdsIntegrationApiConnector.getEntityById(Account.EN_PLURAL, bisAccountId, "currency");
        return acc.getCurrency().getId();
    }
}
