/*
 * Copyright (c) 2024 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.jdbc;


import java.util.Optional;

import cz.finshape.vb.domain.internal.gde.Card3DSAuthorization;


/**
 * GDE Card 3DS JDBC DAO.
 *
 * <AUTHOR> Cernil
 */
public interface Card3DSJdbcDao {

    /**
     * Finds optional card 3DS authorization by certification request hashed ID
     * @param certReqId certification request ID
     * @return optional card 3DS authorization
     */
    Optional<Card3DSAuthorization> getByCertificationRequestHash(String certReqId);

    /**
     * Updates the result of the card 3DS authorization.
     * @param certReqId certification request ID
     * @param authResult any result string you can think of now for 3ds
     * @param certResult any result string you can think of now for gaas
     */
    void update(String certReqId, String authResult, String certResult);
}
