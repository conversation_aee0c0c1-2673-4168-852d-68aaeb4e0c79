/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.connector.spx;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInitializer;

import cz.finshape.vb.client.restclient.config.support.AbstractConnectorFactoryConfiguration;
import cz.finshape.vb.client.restclient.spx.SpxIntegrationApiConnector;
import cz.finshape.vb.client.restclient.spx.SpxIntegrationApiConnectorImpl;

import cz.bsc.commons.client.restclient.RestClientFactory;


/**
 * SPX Integration API connector configuration.
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(value = "enabled", prefix = SpxIntegrationApiConnectorFactoryProperties.INTEGRATION_API_PREFIX, matchIfMissing = true)
@EnableConfigurationProperties(SpxIntegrationApiConnectorFactoryProperties.class)
public class SpxIntegrationApiConnectorFactoryConfiguration extends AbstractConnectorFactoryConfiguration {

    /**
     * Primary constructor.
     *
     * @param factory RestClient factory
     * @param requestInitializer request initializer
     * @param properties properties
     */
    SpxIntegrationApiConnectorFactoryConfiguration(final RestClientFactory factory,
                                                   final ClientHttpRequestInitializer requestInitializer,
                                                   final SpxIntegrationApiConnectorFactoryProperties properties) {

        super(factory, requestInitializer, properties.getFactory());
    }

    /**
     * SPX Integration API connector bean.
     *
     * @param properties properties
     * @return connector bean
     */
    @Bean
    SpxIntegrationApiConnector spxIntegrationApiConnector(final SpxIntegrationApiConnectorFactoryProperties properties) {

        return new SpxIntegrationApiConnectorImpl(restClient, properties);
    }
}
