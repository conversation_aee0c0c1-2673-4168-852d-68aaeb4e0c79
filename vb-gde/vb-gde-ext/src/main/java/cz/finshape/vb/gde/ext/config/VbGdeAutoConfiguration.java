/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.vb.gde.ext.config;

import jakarta.annotation.PostConstruct;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;

import cz.finshape.vb.gde.ext.rest.VbGdeRestConfiguration;
import cz.finshape.vb.gde.ext.service.VbGdeServiceConfiguration;
import lombok.extern.slf4j.Slf4j;

/**
 * GDE configuration.
 *
 * <AUTHOR>
 */
@Slf4j
@ComponentScan(basePackages = {
    "cz.finshape.vb.gde.ext.mapper",
    "cz.finshape.vb.gde.ext.jdbc",
})
@Import({
    VbGdeSpringBootVersionContributor.class,
    VbGdeServiceConfiguration.class,
    VbGdeRestConfiguration.class
})
@AutoConfiguration
public class VbGdeAutoConfiguration {

    /**
     * Post construct method.
     */
    @PostConstruct
    public void postConstruct() {
        log.trace("{} applied", this.getClass().getSimpleName());
    }
}
