/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.gde.plugin.rabbitmq.config;

import jakarta.annotation.PostConstruct;

import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.amqp.rabbit.listener.api.RabbitListenerErrorHandler;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConversionException;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.messaging.handler.annotation.support.MethodArgumentNotValidException;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.finshape.gde.plugin.rabbitmq.service.GdePluginRabbitMqServiceConfiguration;
import lombok.extern.slf4j.Slf4j;


/**
 * GDE RabbitMQ auto-configuration
 *
 * <AUTHOR> Sarana
 */
@Slf4j
@EnableRabbit
@AutoConfiguration
@ConditionalOnProperty(value = "enabled", prefix = "gde.rabbitmq")
@Import({
    GdePluginRabbitMqServiceConfiguration.class
})
public class GdeRabbitMqAutoConfiguration {

    public static final String RABBIT_LISTENER_ERROR_HANDLER_BEAN_NAME = "rabbitListenerErrorHandler";

    /**
     * Post construct method.
     */
    @PostConstruct
    public void postConstruct() {
        log.trace("{} applied", this.getClass().getSimpleName());
    }

    /**
     * AMPQ Message converter configuration, converts OUT and IN messages to JSON strings,
     * contains Object mapper configuration to fulfil JSON .requirements
     *
     * @param rabbitMqObjectMapper RabbitMQ Object Mapper
     * @return AMPQ Message Converter
     */
    @Bean
    @ConditionalOnProperty(value = "enabled", prefix = "gde.rabbitmq.message-converter")
    public MessageConverter rabbitMqMessageConverter(final ObjectMapper rabbitMqObjectMapper) {
        return new Jackson2JsonMessageConverter(rabbitMqObjectMapper);
    }

    /**
     * JSON mapper which will be used only by RabbitMQ queue.
     * <p>
     * The bean is defined for the case RabbitMQ de/serialization requires a specific configuration.
     * By default, it is a copy of the existing {@code gdeObjectMapper}.
     *
     * @param gdeObjectMapper GDE object mapper
     *
     * @return JSON object mapper
     */
    @Bean(name = "rabbitMqObjectMapper")
    @ConditionalOnProperty(value = "enabled", prefix = "gde.rabbitmq.object-mapper", matchIfMissing = true)
    public ObjectMapper rabbitMqObjectMapper(@Qualifier("gdeObjectMapper") final ObjectMapper gdeObjectMapper) {
        final var rabbitMqObjectMapper = gdeObjectMapper.copy();
        log.info("Registered ObjectMapper (instance={}) for RabbitMQ", rabbitMqObjectMapper);
        return rabbitMqObjectMapper;
    }

    /**
     * RabbitMQ listener error handler.
     * <li><strong>Poison pill</strong> error messages: They are rejected to be requeued and are routed to DLX, if configured.</li>
     * <li><strong>Transient</strong> error messages: They are handled by the default retry configuration.</li>
     *
     * @return configured RabbitMQ listener error handler
     */
    @Bean(name = RABBIT_LISTENER_ERROR_HANDLER_BEAN_NAME)
    @ConditionalOnProperty(value = "enabled", prefix = "gde.rabbitmq.dead-lettering", matchIfMissing = true)
    public RabbitListenerErrorHandler rabbitListenerErrorHandler() {
        return (amqpMessage, channel, springMessage, exception) -> {
            final var messageProperties = amqpMessage.getMessageProperties();
            final var xDeliveryCount = messageProperties.getHeader("x-delivery-count");
            log.error("Error handling of message from queue '{}', (x-delivery-count={}) : {}",
                messageProperties.getConsumerQueue(), xDeliveryCount, exception.getMessage(), exception);

            final var cause = exception.getCause();

            // Poison Pill: Immediately send to DLX.
            if (cause instanceof MessageConversionException || cause instanceof MethodArgumentNotValidException) {
                throw new AmqpRejectAndDontRequeueException("Poison pill detected: Sending to DLX", exception);
            }

            // Technical Error: Allow retry based on properties.
            throw exception;
        };
    }
}
