/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.gde.plugin.rabbitmq;


import cz.bsc.commons.spring.environment.annotation.SimpleModuleDescriptor;


/**
 * Module specific properties loader that loads {@code gde-plugin-rabbitmq.properties}.
 *
 * <AUTHOR>
 */
public class GdePluginRabbitMqModuleDescriptor extends SimpleModuleDescriptor {

    /**
     * Primary constructor.
     */
    public GdePluginRabbitMqModuleDescriptor() {
        super("gde", "plugin-rabbitmq");
    }
}
