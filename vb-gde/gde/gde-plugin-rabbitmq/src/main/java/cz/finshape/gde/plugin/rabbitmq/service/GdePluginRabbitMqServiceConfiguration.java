/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.gde.plugin.rabbitmq.service;


import java.util.List;

import jakarta.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.finshape.gde.core.route.in.push.RouteInPushingOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * Service GDE Plugin RabbitMQ module auto-configuration.
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(value = "enabled", prefix = "gde.rabbitmq.service", matchIfMissing = true)
public class GdePluginRabbitMqServiceConfiguration {

    /**
     * Post construct method.
     */
    @PostConstruct
    public void postConstruct() {
        log.trace("{} applied", this.getClass().getSimpleName());
    }

    /**
     * RabbitMQ Listener service.
     *
     * @param environment Spring environment
     * @param routeInPushingOperations ROUTE IN pushing operations
     * @param rabbitMqObjectMapper RabbitMQ Object Mapper
     * @return RabbitMQ Listener service
     */
    @Bean
    @ConditionalOnMissingBean
    public RabbitListenerServiceImpl rabbitListenerService(final Environment environment,
                                                           final List<RouteInPushingOperation> routeInPushingOperations,
                                                           @Qualifier("rabbitMqObjectMapper") final ObjectMapper rabbitMqObjectMapper) {

        return new RabbitListenerServiceImpl(environment, routeInPushingOperations, rabbitMqObjectMapper);
    }
}
