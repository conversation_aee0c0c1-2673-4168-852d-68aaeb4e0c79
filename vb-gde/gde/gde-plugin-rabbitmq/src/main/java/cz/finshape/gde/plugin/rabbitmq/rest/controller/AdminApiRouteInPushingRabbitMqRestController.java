/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.gde.plugin.rabbitmq.rest.controller;


import static cz.finshape.gde.plugin.rabbitmq.rest.controller.AdminApiRouteInPushingRabbitMqRestController.ENDPOINT;

import java.util.Map;

import jakarta.annotation.PostConstruct;

import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.databind.JsonNode;
import cz.finshape.gde.plugin.rabbitmq.service.RabbitListenerService;
import lombok.extern.slf4j.Slf4j;


/**
 * Integration API Pushing Controller for ROUTE IN mimicking {@link RabbitListener}.
 *
 * <AUTHOR> Charalambidis
 */
@Slf4j
@RestController
@ConditionalOnProperty(value = "enabled", prefix = "gde.rest.admin-api.controller.pushing.rabbitmq")
@RequestMapping(ENDPOINT)
public class AdminApiRouteInPushingRabbitMqRestController {

    public final static String ENDPOINT = "/admin/v1/route/in/pushing/rabbitmq";

    @Autowired
    private RabbitListenerService rabbitListenerService;

    /**
     * Post construct method.
     */
    @PostConstruct
    public void postConstruct() {
        log.trace("{} applied", this.getClass().getSimpleName());
    }

    /**
     Mimics a {@code @RabbitListener} by programmatic underlying method invocation via reflection.
     * All headers invoked within this REST endpoint are passed into matching Kafka headers of the {@code @RabbitListener}.
     *
     * @param payload JSON payload
     * @param queue RabbitMQ queue
     * @param headers RabbitMQ headers
     */
    @PostMapping("/message")
    @ResponseStatus(HttpStatus.ACCEPTED)
    public void message(@RequestBody final JsonNode payload,
                        @RequestParam final String queue,
                        @RequestHeader final Map<String, Object> headers) {

        rabbitListenerService.execute(queue, payload, headers);
    }
}
