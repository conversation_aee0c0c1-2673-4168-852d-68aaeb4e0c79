/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.gde.plugin.rabbitmq.system;

import cz.finshape.gde.core.domain.message.ServiceType;
import cz.finshape.gde.core.domain.message.System;
import cz.finshape.gde.core.system.SystemIdentification;


/**
 * RabbitMQ system identification data.
 *
 * <AUTHOR>
 */
public record RabbitMqSystemIdentification(
    ServiceType serviceType,
    System system,
    String systemMethod,
    String systemPoint
) implements SystemIdentification {

    /**
     * Primary constructor.
     *
     * @param serviceType service type
     * @param queueName queue name
     */
    public RabbitMqSystemIdentification(final ServiceType serviceType, final String queueName) {
        this(serviceType, System.RABBIT_MQ, null, queueName);
    }
}
