/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.gde.plugin.rabbitmq.service;


import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.core.env.Environment;
import org.springframework.messaging.handler.annotation.Header;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import cz.finshape.gde.core.plugin.AbstractListenerService;
import cz.finshape.gde.core.route.in.push.RouteInPushingOperation;
import lombok.extern.slf4j.Slf4j;


/**
 * Implementation of {@link RabbitListener} service.
 *
 * <AUTHOR>
 */
@Slf4j
public class RabbitListenerServiceImpl extends AbstractListenerService<RabbitListener> implements RabbitListenerService {

    /**
     * Primary constructor.
     *
     * @param environment Spring environment
     * @param routeInPushingOperations list of pushing ROUTE IN operations
     * @param rabbitMqObjectMapper RabbitMQ Object Mapper
     */
    public RabbitListenerServiceImpl(final Environment environment,
                                     final List<RouteInPushingOperation> routeInPushingOperations,
                                     final ObjectMapper rabbitMqObjectMapper) {

        super(environment, routeInPushingOperations, rabbitMqObjectMapper, RabbitListener.class);
    }

    @Nonnull
    @Override
    public String[] getTopicsOrQueue(@Nonnull final RabbitListener listenerAnnotation) {
        return listenerAnnotation.queues();
    }

    /**
     * <strong>Important:</strong> Tomcat transforms response header keys into lower-case.
     * The look-up table must be case-insensitive: to elevate the advantages of hash structures,
     * we convert headers into lower-case for the sake of lookup.
     *
     * @see <a href="https://stackoverflow.com/a/56396686/3764965">StackOverflow</a>
     * @see <a href="https://bz.apache.org/bugzilla/show_bug.cgi?id=58464">Apache issue #58464</a>
     */
    @Nonnull
    @Override
    protected Object[] resolveArguments(@Nonnull final Method method,
                                        @Nullable final JsonNode payload,
                                        @Nonnull final Map<String, Object> headers) {

        final LinkedHashMap<String, Parameter> parameters = Arrays.stream(method.getParameters())
            .collect(Collectors.toMap(Parameter::getName, Function.identity(), (a, b) -> a, LinkedHashMap::new));

        final Map<String, Object> headersInLowerCase = headers.entrySet()
            .stream()
            .collect(Collectors.toMap(e -> e.getKey().toLowerCase(Locale.ENGLISH), Map.Entry::getValue));

        final List<Object> args = new ArrayList<>();

        boolean payloadFound = false;
        for (Map.Entry<String, Parameter> entry: parameters.entrySet()) {
            final var parameterName = entry.getKey();
            final var parameter = entry.getValue();
            if (parameter.isAnnotationPresent(Header.class)) {
                final var headerAnnotation = parameter.getAnnotation(Header.class);
                final var headerKey = environment.resolvePlaceholders(headerAnnotation.value());
                log.debug("Method parameter annotated with @Header: {}, header key: {}", parameterName, headerKey);
                // Use lower-case for lookup.
                args.add(headersInLowerCase.get(headerKey.toLowerCase(Locale.ENGLISH)));
            } else {
                if (!payloadFound) {
                    // @RabbitListener does not need @Payload, let's find the first such parameter.
                    log.debug("Deserializing payload to the type: {}", parameterName);
                    final var deserializedPayload = deserializePayload(payload, parameter.getType());
                    args.add(deserializedPayload);
                    payloadFound = true;
                } else {
                    // No matched parameter
                    log.warn("Unannotated or unresolved method parameter: {}", parameterName);
                    args.add(null);
                }
            }
        }

        return args.toArray(new Object[0]);
    }
}
