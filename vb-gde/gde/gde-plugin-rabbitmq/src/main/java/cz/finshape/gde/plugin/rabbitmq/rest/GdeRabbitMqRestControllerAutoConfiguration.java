/*
 * Copyright (c) 2023 Finshape Czechia s.r.o.
 */

package cz.finshape.gde.plugin.rabbitmq.rest;


import jakarta.annotation.PostConstruct;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;

import lombok.extern.slf4j.Slf4j;


/**
 * GDE Plugin RabbitMQ REST controller configuration.
 *
 * <AUTHOR>
 */
@Slf4j
@AutoConfiguration
@ConditionalOnProperty(value = "enabled", prefix = "gde.rest.admin-api.controller.pushing", matchIfMissing = true)
@ComponentScan(basePackages = "cz.finshape.gde.plugin.rabbitmq.rest.controller")
public class GdeRabbitMqRestControllerAutoConfiguration {

    /**
     * Post construct method.
     */
    @PostConstruct
    public void postConstruct() {
        log.trace("{} applied", this.getClass().getSimpleName());
    }
}
